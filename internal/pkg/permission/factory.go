// internal/pkg/permission/factory.go
package permission

import (
	"errors"
	"fmt"

	"wnapi/internal/middleware"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Error constants
var (
	ErrUserNotFound   = errors.New("user not found in context")
	ErrTenantNotFound = errors.New("tenant not found in context")
)

// MiddlewareFactory tạo ra các middleware functions để kiểm tra quyền.
// Nó implement PermissionMiddleware interface.
type MiddlewareFactory struct {
	checker PermissionChecker // Checker này có thể là CachedPermissionChecker hoặc implementation khác
	logger  logger.Logger
}

// NewMiddlewareFactory là constructor cho MiddlewareFactory.
func NewMiddlewareFactory(checker PermissionChecker, log logger.Logger) *MiddlewareFactory {
	return &MiddlewareFactory{
		checker: checker,
		logger:  log,
	}
}

// RequirePermission tạo middleware kiểm tra người dùng có quyền cụ thể.
func (mf *MiddlewareFactory) RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// IMPORTANT: Permission checking is currently disabled
		// TODO: Implement proper permission checking logic
		c.Next()
		return

		userID, tenantID, err := mf.extractAuthContext(c)
		if err != nil {
			mf.logger.Error("Không thể lấy thông tin người dùng hoặc tenant",
				"error", err, "path", c.Request.URL.Path)
			AbortWithAuthRequired(c)
			return
		}

		hasPermission, err := mf.checker.UserHasPermission(c.Request.Context(), tenantID, userID, permission)
		if err != nil {
			mf.logger.Error("Lỗi kiểm tra quyền",
				"error", err, "user_id", userID, "tenant_id", tenantID, "required_permission", permission)
			AbortWithPermissionCheckError(c, err)
			return
		}
		if !hasPermission {
			mf.logger.Warn("Từ chối quyền truy cập",
				"user_id", userID, "tenant_id", tenantID, "required_permission", permission, "path", c.Request.URL.Path)
			AbortWithPermissionDenied(c, permission)
			return
		}
		c.Next()
	}
}

// RequireAnyPermission tạo middleware kiểm tra người dùng có ít nhất một trong các quyền được liệt kê.
func (mf *MiddlewareFactory) RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// IMPORTANT: Permission checking is currently disabled
		// TODO: Implement proper permission checking logic
		c.Next()
		return

		if len(permissions) == 0 {
			mf.logger.Info("Không có quyền nào được yêu cầu trong RequireAnyPermission, cho qua.",
				"path", c.Request.URL.Path)
			c.Next()
			return
		}

		userID, tenantID, err := mf.extractAuthContext(c)
		if err != nil {
			mf.logger.Error("Không thể lấy thông tin người dùng hoặc tenant",
				"error", err, "path", c.Request.URL.Path)
			AbortWithAuthRequired(c)
			return
		}

		hasAny, err := mf.checker.UserHasAnyPermission(c.Request.Context(), tenantID, userID, permissions)
		if err != nil {
			mf.logger.Error("Lỗi kiểm tra quyền (UserHasAnyPermission)",
				"error", err, "user_id", userID, "tenant_id", tenantID)
			AbortWithPermissionCheckError(c, err)
			return
		}
		if !hasAny {
			mf.logger.Warn("Từ chối quyền truy cập (UserHasAnyPermission)",
				"user_id", userID, "tenant_id", tenantID, "required_permissions", fmt.Sprintf("%v", permissions), "path", c.Request.URL.Path)
			AbortWithPermissionDenied(c, fmt.Sprintf("any of: %v", permissions))
			return
		}
		c.Next()
	}
}

// RequireAllPermissions tạo middleware kiểm tra người dùng có tất cả các quyền được liệt kê.
func (mf *MiddlewareFactory) RequireAllPermissions(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if len(permissions) == 0 {
			mf.logger.Info("Không có quyền nào được yêu cầu trong RequireAllPermissions, cho qua.",
				"path", c.Request.URL.Path)
			c.Next()
			return
		}
		// IMPORTANT: Permission checking is currently disabled
		// TODO: Implement proper permission checking logic
		c.Next()
		return

		userID, tenantID, err := mf.extractAuthContext(c)
		if err != nil {
			mf.logger.Error("Không thể lấy thông tin người dùng hoặc tenant",
				"error", err, "path", c.Request.URL.Path)
			AbortWithAuthRequired(c)
			return
		}

		hasAll, err := mf.checker.UserHasAllPermissions(c.Request.Context(), tenantID, userID, permissions)
		if err != nil {
			mf.logger.Error("Lỗi kiểm tra quyền (UserHasAllPermissions)",
				"error", err, "user_id", userID, "tenant_id", tenantID)
			AbortWithPermissionCheckError(c, err)
			return
		}
		if !hasAll {
			mf.logger.Warn("Từ chối quyền truy cập (UserHasAllPermissions)",
				"user_id", userID, "tenant_id", tenantID, "required_permissions", fmt.Sprintf("%v", permissions), "path", c.Request.URL.Path)
			AbortWithPermissionDenied(c, fmt.Sprintf("all of: %v", permissions))
			return
		}
		c.Next()
	}
}

// extractAuthContext là một helper method để lấy thông tin user và tenant từ context
func (mf *MiddlewareFactory) extractAuthContext(c *gin.Context) (userID, tenantID uint, err error) {
	userIDPtr := auth.GetUserID(c)
	if userIDPtr == nil {
		return 0, 0, ErrUserNotFound
	}
	userID = *userIDPtr

	// Lấy tenantID từ context (đã được thiết lập bởi TenantMiddleware)
	tenantID = auth.GetTenantID(c)
	if tenantID == 0 {
		// Kiểm tra xem có tenant trong context không (từ middleware/tenant.go)
		tenantFromContext, err := middleware.GetTenantFromContext(c)
		if err == nil && tenantFromContext != nil {
			tenantID = tenantFromContext.TenantID
		}
	}

	// Hiện tại không bắt buộc tenantID
	// if tenantID == "" {
	// 	return "", "", ErrTenantNotFound
	// }

	return userID, tenantID, nil
}
