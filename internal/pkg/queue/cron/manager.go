package cron

import (
	"context"
	"fmt"
	"log/slog"

	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/queue"
	"wnapi/internal/pkg/queue/cron/handlers"
	"wnapi/internal/pkg/queue/types"

	"gorm.io/gorm"
)

// CronManager quản lý toàn bộ hệ thống cron
type CronManager struct {
	scheduler *CronScheduler
	registry  *HandlerRegistry
	builder   *CronJobBuilder
	config    config.Config
	logger    *slog.Logger
	db        *gorm.DB
}

// NewCronManager tạo CronManager mới
func NewCronManager(queueMgr *queue.QueueManager, config config.Config, logger *slog.Logger, db *gorm.DB) *CronManager {
	registry := NewHandlerRegistry(logger)
	scheduler := NewCronScheduler(queueMgr, config, logger)
	builder := NewCronJobBuilder(registry, config, logger)

	return &CronManager{
		scheduler: scheduler,
		registry:  registry,
		builder:   builder,
		config:    config,
		logger:    logger,
		db:        db,
	}
}

// Initialize khởi tạo cron manager vớ<PERSON> tất cả handlers và jobs
func (m *CronManager) Initialize() error {
	cronConfig := m.config.GetCronConfig()

	if !cronConfig.Enabled {
		m.logger.Info("Cron system is disabled")
		return nil
	}

	m.logger.Info("Initializing cron manager")

	// Register system handlers
	if err := m.registerSystemHandlers(); err != nil {
		return fmt.Errorf("failed to register system handlers: %w", err)
	}

	// Register module handlers
	if err := m.registerModuleHandlers(); err != nil {
		return fmt.Errorf("failed to register module handlers: %w", err)
	}

	// Build and register jobs from config
	if err := m.registerJobsFromConfig(); err != nil {
		return fmt.Errorf("failed to register jobs from config: %w", err)
	}

	m.logger.Info("Cron manager initialized successfully")
	return nil
}

// Start khởi động cron scheduler
func (m *CronManager) Start() error {
	cronConfig := m.config.GetCronConfig()

	if !cronConfig.Enabled {
		m.logger.Info("Cron system is disabled, not starting")
		return nil
	}

	m.logger.Info("Starting cron manager")
	return m.scheduler.Start()
}

// Stop dừng cron scheduler
func (m *CronManager) Stop() error {
	m.logger.Info("Stopping cron manager")
	return m.scheduler.Stop()
}

// GetScheduler trả về scheduler instance
func (m *CronManager) GetScheduler() *CronScheduler {
	return m.scheduler
}

// GetRegistry trả về handler registry
func (m *CronManager) GetRegistry() *HandlerRegistry {
	return m.registry
}

// RegisterCustomHandler đăng ký custom handler
func (m *CronManager) RegisterCustomHandler(handler CronHandler) error {
	return m.registry.RegisterHandler(handler)
}

// RegisterCustomJob đăng ký custom job
func (m *CronManager) RegisterCustomJob(job *CronJob) error {
	return m.scheduler.RegisterJob(job)
}

// registerSystemHandlers đăng ký các system handlers
func (m *CronManager) registerSystemHandlers() error {
	m.logger.Debug("Registering system cron handlers")

	// System cleanup handler
	cleanupHandler := handlers.NewSystemCleanupHandler(m.db, m.config, m.logger)
	if err := m.registry.RegisterHandler(cleanupHandler); err != nil {
		return fmt.Errorf("failed to register system cleanup handler: %w", err)
	}

	// System health check handler
	healthCheckHandler := handlers.NewSystemHealthCheckHandler(m.db, m.config, m.logger)
	if err := m.registry.RegisterHandler(healthCheckHandler); err != nil {
		return fmt.Errorf("failed to register system health check handler: %w", err)
	}

	// System backup handler
	backupHandler := handlers.NewSystemBackupHandler(m.db, m.config, m.logger)
	if err := m.registry.RegisterHandler(backupHandler); err != nil {
		return fmt.Errorf("failed to register system backup handler: %w", err)
	}

	// System demo handler
	demoHandler := handlers.NewSystemDemoHandler(m.db, m.config, m.logger)
	if err := m.registry.RegisterHandler(demoHandler); err != nil {
		return fmt.Errorf("failed to register system demo handler: %w", err)
	}

	m.logger.Debug("System cron handlers registered successfully")
	return nil
}

// registerModuleHandlers đăng ký các module handlers
func (m *CronManager) registerModuleHandlers() error {
	m.logger.Debug("Registering module cron handlers")

	// Check which modules are enabled
	enabledModules := m.config.GetStringSlice("MODULES_ENABLED")

	for _, module := range enabledModules {
		switch module {
		case "auth":
			if err := m.registerAuthHandlers(); err != nil {
				m.logger.Warn("Failed to register auth cron handlers", "error", err)
			}
		case "media":
			if err := m.registerMediaHandlers(); err != nil {
				m.logger.Warn("Failed to register media cron handlers", "error", err)
			}
		case "blog":
			if err := m.registerBlogHandlers(); err != nil {
				m.logger.Warn("Failed to register blog cron handlers", "error", err)
			}
			// Add more modules as needed
		}
	}

	m.logger.Debug("Module cron handlers registered successfully")
	return nil
}

// registerAuthHandlers đăng ký auth module handlers
func (m *CronManager) registerAuthHandlers() error {
	// Note: This requires importing the auth module handlers
	// For now, we'll create placeholder handlers here

	// Auth session cleanup handler
	sessionCleanupHandler := &AuthSessionCleanupHandler{
		db:     m.db,
		config: m.config,
		logger: m.logger,
	}
	if err := m.registry.RegisterHandler(sessionCleanupHandler); err != nil {
		return fmt.Errorf("failed to register auth session cleanup handler: %w", err)
	}

	// Auth password expiry handler
	passwordExpiryHandler := &AuthPasswordExpiryHandler{
		db:     m.db,
		config: m.config,
		logger: m.logger,
	}
	if err := m.registry.RegisterHandler(passwordExpiryHandler); err != nil {
		return fmt.Errorf("failed to register auth password expiry handler: %w", err)
	}

	return nil
}

// registerBlogHandlers đăng ký blog module handlers
func (m *CronManager) registerBlogHandlers() error {
	// Note: This requires importing the blog module handlers
	// For now, we'll create placeholder handlers here

	// Blog process pending schedules handler
	processPendingHandler := &BlogProcessPendingSchedulesHandler{
		db:     m.db,
		config: m.config,
		logger: m.logger,
	}
	if err := m.registry.RegisterHandler(processPendingHandler); err != nil {
		return fmt.Errorf("failed to register blog process pending schedules handler: %w", err)
	}

	// Blog cleanup old schedules handler
	cleanupOldHandler := &BlogCleanupOldSchedulesHandler{
		db:     m.db,
		config: m.config,
		logger: m.logger,
	}
	if err := m.registry.RegisterHandler(cleanupOldHandler); err != nil {
		return fmt.Errorf("failed to register blog cleanup old schedules handler: %w", err)
	}

	// Blog publish scheduled posts handler
	publishScheduledHandler := &BlogPublishScheduledPostsHandler{
		db:     m.db,
		config: m.config,
		logger: m.logger,
	}
	if err := m.registry.RegisterHandler(publishScheduledHandler); err != nil {
		return fmt.Errorf("failed to register blog publish scheduled posts handler: %w", err)
	}

	return nil
}

// registerMediaHandlers đăng ký media module handlers
func (m *CronManager) registerMediaHandlers() error {
	// Note: This requires importing the media module handlers
	// For now, we'll create placeholder handlers here

	// Media image optimization handler
	imageOptimizationHandler := &MediaImageOptimizationHandler{
		db:     m.db,
		config: m.config,
		logger: m.logger,
	}
	if err := m.registry.RegisterHandler(imageOptimizationHandler); err != nil {
		return fmt.Errorf("failed to register media image optimization handler: %w", err)
	}

	// Media temp cleanup handler
	tempCleanupHandler := &MediaTempCleanupHandler{
		db:     m.db,
		config: m.config,
		logger: m.logger,
	}
	if err := m.registry.RegisterHandler(tempCleanupHandler); err != nil {
		return fmt.Errorf("failed to register media temp cleanup handler: %w", err)
	}

	return nil
}

// registerJobsFromConfig đăng ký jobs từ config
func (m *CronManager) registerJobsFromConfig() error {
	m.logger.Debug("Building and registering cron jobs from config")

	// Build all jobs from config
	jobs, err := m.builder.BuildAllJobs()
	if err != nil {
		return fmt.Errorf("failed to build jobs from config: %w", err)
	}

	// Register each job
	for _, job := range jobs {
		if err := m.scheduler.RegisterJob(job); err != nil {
			m.logger.Warn("Failed to register cron job",
				"job", job.Name,
				"error", err)
			continue
		}
	}

	m.logger.Info("Registered cron jobs from config", "count", len(jobs))
	return nil
}

// Placeholder handlers - these should be replaced with actual imports from modules

// AuthSessionCleanupHandler placeholder
type AuthSessionCleanupHandler struct {
	db     *gorm.DB
	config config.Config
	logger *slog.Logger
}

func (h *AuthSessionCleanupHandler) GetTaskType() types.CronTaskType {
	return types.CronTaskAuthSessionCleanup
}

func (h *AuthSessionCleanupHandler) GetDescription() string {
	return "Clean up expired and inactive user sessions"
}

func (h *AuthSessionCleanupHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
	// Placeholder implementation
	h.logger.Info("Auth session cleanup handler - placeholder implementation")
	return &types.CronTaskResult{
		TaskType:    types.CronTaskAuthSessionCleanup,
		Success:     true,
		Message:     "Placeholder implementation",
		ExecutedAt:  payload.(*types.AuthSessionCleanupPayload).ExecutedAt,
		CompletedAt: payload.(*types.AuthSessionCleanupPayload).ExecutedAt,
	}, nil
}

// AuthPasswordExpiryHandler placeholder
type AuthPasswordExpiryHandler struct {
	db     *gorm.DB
	config config.Config
	logger *slog.Logger
}

func (h *AuthPasswordExpiryHandler) GetTaskType() types.CronTaskType {
	return types.CronTaskAuthPasswordExpiry
}

func (h *AuthPasswordExpiryHandler) GetDescription() string {
	return "Send password expiry notifications to users"
}

func (h *AuthPasswordExpiryHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
	// Placeholder implementation
	h.logger.Info("Auth password expiry handler - placeholder implementation")
	return &types.CronTaskResult{
		TaskType:    types.CronTaskAuthPasswordExpiry,
		Success:     true,
		Message:     "Placeholder implementation",
		ExecutedAt:  payload.(*types.AuthPasswordExpiryPayload).ExecutedAt,
		CompletedAt: payload.(*types.AuthPasswordExpiryPayload).ExecutedAt,
	}, nil
}

// MediaImageOptimizationHandler placeholder
type MediaImageOptimizationHandler struct {
	db     *gorm.DB
	config config.Config
	logger *slog.Logger
}

func (h *MediaImageOptimizationHandler) GetTaskType() types.CronTaskType {
	return types.CronTaskMediaImageOptimization
}

func (h *MediaImageOptimizationHandler) GetDescription() string {
	return "Optimize images to reduce file size and improve performance"
}

func (h *MediaImageOptimizationHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
	// Placeholder implementation
	h.logger.Info("Media image optimization handler - placeholder implementation")
	return &types.CronTaskResult{
		TaskType:    types.CronTaskMediaImageOptimization,
		Success:     true,
		Message:     "Placeholder implementation",
		ExecutedAt:  payload.(*types.MediaImageOptimizationPayload).ExecutedAt,
		CompletedAt: payload.(*types.MediaImageOptimizationPayload).ExecutedAt,
	}, nil
}

// MediaTempCleanupHandler placeholder
type MediaTempCleanupHandler struct {
	db     *gorm.DB
	config config.Config
	logger *slog.Logger
}

func (h *MediaTempCleanupHandler) GetTaskType() types.CronTaskType {
	return types.CronTaskMediaTempCleanup
}

func (h *MediaTempCleanupHandler) GetDescription() string {
	return "Clean up temporary media files and uploads"
}

func (h *MediaTempCleanupHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
	// Placeholder implementation
	h.logger.Info("Media temp cleanup handler - placeholder implementation")
	return &types.CronTaskResult{
		TaskType:    types.CronTaskMediaTempCleanup,
		Success:     true,
		Message:     "Placeholder implementation",
		ExecutedAt:  payload.(*types.MediaTempCleanupPayload).ExecutedAt,
		CompletedAt: payload.(*types.MediaTempCleanupPayload).ExecutedAt,
	}, nil
}

// BlogProcessPendingSchedulesHandler placeholder
type BlogProcessPendingSchedulesHandler struct {
	db     *gorm.DB
	config config.Config
	logger *slog.Logger
}

func (h *BlogProcessPendingSchedulesHandler) GetTaskType() types.CronTaskType {
	return types.CronTaskBlogProcessPendingSchedules
}

func (h *BlogProcessPendingSchedulesHandler) GetDescription() string {
	return "Process pending blog schedules and publish ready posts"
}

func (h *BlogProcessPendingSchedulesHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
	// Placeholder implementation
	h.logger.Info("Blog process pending schedules handler - placeholder implementation")
	return &types.CronTaskResult{
		TaskType:    types.CronTaskBlogProcessPendingSchedules,
		Success:     true,
		Message:     "Placeholder implementation",
		ExecutedAt:  payload.(*types.BlogProcessPendingSchedulesPayload).ExecutedAt,
		CompletedAt: payload.(*types.BlogProcessPendingSchedulesPayload).ExecutedAt,
	}, nil
}

// BlogCleanupOldSchedulesHandler placeholder
type BlogCleanupOldSchedulesHandler struct {
	db     *gorm.DB
	config config.Config
	logger *slog.Logger
}

func (h *BlogCleanupOldSchedulesHandler) GetTaskType() types.CronTaskType {
	return types.CronTaskBlogCleanupOldSchedules
}

func (h *BlogCleanupOldSchedulesHandler) GetDescription() string {
	return "Clean up old completed blog schedules"
}

func (h *BlogCleanupOldSchedulesHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
	// Placeholder implementation
	h.logger.Info("Blog cleanup old schedules handler - placeholder implementation")
	return &types.CronTaskResult{
		TaskType:    types.CronTaskBlogCleanupOldSchedules,
		Success:     true,
		Message:     "Placeholder implementation",
		ExecutedAt:  payload.(*types.BlogCleanupOldSchedulesPayload).ExecutedAt,
		CompletedAt: payload.(*types.BlogCleanupOldSchedulesPayload).ExecutedAt,
	}, nil
}

// BlogPublishScheduledPostsHandler placeholder
type BlogPublishScheduledPostsHandler struct {
	db     *gorm.DB
	config config.Config
	logger *slog.Logger
}

func (h *BlogPublishScheduledPostsHandler) GetTaskType() types.CronTaskType {
	return types.CronTaskBlogPublishScheduledPosts
}

func (h *BlogPublishScheduledPostsHandler) GetDescription() string {
	return "Publish scheduled blog posts that are ready"
}

func (h *BlogPublishScheduledPostsHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
	// Placeholder implementation
	h.logger.Info("Blog publish scheduled posts handler - placeholder implementation")
	return &types.CronTaskResult{
		TaskType:    types.CronTaskBlogPublishScheduledPosts,
		Success:     true,
		Message:     "Placeholder implementation",
		ExecutedAt:  payload.(*types.BlogPublishScheduledPostsPayload).ExecutedAt,
		CompletedAt: payload.(*types.BlogPublishScheduledPostsPayload).ExecutedAt,
	}, nil
}
