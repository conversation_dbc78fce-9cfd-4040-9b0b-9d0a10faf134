package cron

import (
	"fmt"
	"log/slog"
	"sync"

	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/queue/types"
)

// HandlerRegistry quản lý việc đăng ký và tìm kiếm cron handlers
type HandlerRegistry struct {
	handlers map[types.CronTaskType]CronHandler
	mu       sync.RWMutex
	logger   *slog.Logger
}

// NewHandlerRegistry tạo một HandlerRegistry mới
func NewHandlerRegistry(logger *slog.Logger) *HandlerRegistry {
	return &HandlerRegistry{
		handlers: make(map[types.CronTaskType]CronHandler),
		logger:   logger,
	}
}

// RegisterHandler đăng ký một cron handler
func (r *HandlerRegistry) RegisterHandler(handler CronHandler) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	taskType := handler.GetTaskType()

	if _, exists := r.handlers[taskType]; exists {
		return fmt.Errorf("handler for task type '%s' already registered", taskType)
	}

	r.handlers[taskType] = handler
	r.logger.Info("Registered cron handler",
		"task_type", taskType,
		"description", handler.GetDescription())

	return nil
}

// GetHandler lấy handler cho một task type
func (r *HandlerRegistry) GetHandler(taskType types.CronTaskType) (CronHandler, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	handler, exists := r.handlers[taskType]
	if !exists {
		return nil, fmt.Errorf("no handler registered for task type '%s'", taskType)
	}

	return handler, nil
}

// GetAllHandlers trả về tất cả handlers đã đăng ký
func (r *HandlerRegistry) GetAllHandlers() map[types.CronTaskType]CronHandler {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[types.CronTaskType]CronHandler)
	for taskType, handler := range r.handlers {
		result[taskType] = handler
	}

	return result
}

// UnregisterHandler hủy đăng ký một handler
func (r *HandlerRegistry) UnregisterHandler(taskType types.CronTaskType) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.handlers[taskType]; !exists {
		return fmt.Errorf("no handler registered for task type '%s'", taskType)
	}

	delete(r.handlers, taskType)
	r.logger.Info("Unregistered cron handler", "task_type", taskType)

	return nil
}

// HasHandler kiểm tra xem có handler cho task type không
func (r *HandlerRegistry) HasHandler(taskType types.CronTaskType) bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	_, exists := r.handlers[taskType]
	return exists
}

// GetRegisteredTaskTypes trả về danh sách tất cả task types đã đăng ký
func (r *HandlerRegistry) GetRegisteredTaskTypes() []types.CronTaskType {
	r.mu.RLock()
	defer r.mu.RUnlock()

	taskTypes := make([]types.CronTaskType, 0, len(r.handlers))
	for taskType := range r.handlers {
		taskTypes = append(taskTypes, taskType)
	}

	return taskTypes
}

// CronJobBuilder giúp tạo cron jobs từ config
type CronJobBuilder struct {
	registry *HandlerRegistry
	config   config.Config
	logger   *slog.Logger
}

// NewCronJobBuilder tạo một CronJobBuilder mới
func NewCronJobBuilder(registry *HandlerRegistry, config config.Config, logger *slog.Logger) *CronJobBuilder {
	return &CronJobBuilder{
		registry: registry,
		config:   config,
		logger:   logger,
	}
}

// BuildSystemJobs tạo các system cron jobs từ config
func (b *CronJobBuilder) BuildSystemJobs() ([]*CronJob, error) {
	cronConfig := b.config.GetCronConfig()
	var jobs []*CronJob

	// System cleanup job
	if cronConfig.SystemJobs.Cleanup.Enabled {
		handler, err := b.registry.GetHandler(types.CronTaskSystemCleanup)
		if err != nil {
			b.logger.Warn("System cleanup handler not found", "error", err)
		} else {
			job := &CronJob{
				ID:          "system_cleanup",
				Name:        "System Cleanup",
				TaskType:    types.CronTaskSystemCleanup,
				Schedule:    cronConfig.SystemJobs.Cleanup.Schedule,
				Enabled:     true,
				Handler:     handler,
				Description: "Clean up system logs, temp files, and cache",
			}
			jobs = append(jobs, job)
		}
	}

	// System health check job
	if cronConfig.SystemJobs.HealthCheck.Enabled {
		handler, err := b.registry.GetHandler(types.CronTaskSystemHealthCheck)
		if err != nil {
			b.logger.Warn("System health check handler not found", "error", err)
		} else {
			job := &CronJob{
				ID:          "system_health_check",
				Name:        "System Health Check",
				TaskType:    types.CronTaskSystemHealthCheck,
				Schedule:    cronConfig.SystemJobs.HealthCheck.Schedule,
				Enabled:     true,
				Handler:     handler,
				Description: "Check system health and notify on issues",
			}
			jobs = append(jobs, job)
		}
	}

	// System backup job
	if cronConfig.SystemJobs.Backup.Enabled {
		handler, err := b.registry.GetHandler(types.CronTaskSystemBackup)
		if err != nil {
			b.logger.Warn("System backup handler not found", "error", err)
		} else {
			job := &CronJob{
				ID:          "system_backup",
				Name:        "System Backup",
				TaskType:    types.CronTaskSystemBackup,
				Schedule:    cronConfig.SystemJobs.Backup.Schedule,
				Enabled:     true,
				Handler:     handler,
				Description: "Backup system data and configuration",
			}
			jobs = append(jobs, job)
		}
	}

	// System demo job
	if cronConfig.SystemJobs.Demo.Enabled {
		handler, err := b.registry.GetHandler(types.CronTaskSystemDemo)
		if err != nil {
			b.logger.Warn("System demo handler not found", "error", err)
		} else {
			job := &CronJob{
				ID:          "system_demo",
				Name:        "System Demo",
				TaskType:    types.CronTaskSystemDemo,
				Schedule:    cronConfig.SystemJobs.Demo.Schedule,
				Enabled:     true,
				Handler:     handler,
				Description: "Demo cron task that runs every minute for demonstration purposes",
			}
			jobs = append(jobs, job)
		}
	}

	return jobs, nil
}

// BuildAuthJobs tạo các auth module cron jobs từ config
func (b *CronJobBuilder) BuildAuthJobs() ([]*CronJob, error) {
	cronConfig := b.config.GetCronConfig()
	var jobs []*CronJob

	// Auth session cleanup job
	if cronConfig.ModuleJobs.Auth.SessionCleanup.Enabled {
		handler, err := b.registry.GetHandler(types.CronTaskAuthSessionCleanup)
		if err != nil {
			b.logger.Warn("Auth session cleanup handler not found", "error", err)
		} else {
			job := &CronJob{
				ID:          "auth_session_cleanup",
				Name:        "Auth Session Cleanup",
				TaskType:    types.CronTaskAuthSessionCleanup,
				Schedule:    cronConfig.ModuleJobs.Auth.SessionCleanup.Schedule,
				Enabled:     true,
				Handler:     handler,
				Description: "Clean up expired and inactive user sessions",
			}
			jobs = append(jobs, job)
		}
	}

	// Auth password expiry job
	if cronConfig.ModuleJobs.Auth.PasswordExpiry.Enabled {
		handler, err := b.registry.GetHandler(types.CronTaskAuthPasswordExpiry)
		if err != nil {
			b.logger.Warn("Auth password expiry handler not found", "error", err)
		} else {
			job := &CronJob{
				ID:          "auth_password_expiry",
				Name:        "Auth Password Expiry Notification",
				TaskType:    types.CronTaskAuthPasswordExpiry,
				Schedule:    cronConfig.ModuleJobs.Auth.PasswordExpiry.Schedule,
				Enabled:     true,
				Handler:     handler,
				Description: "Send password expiry notifications to users",
			}
			jobs = append(jobs, job)
		}
	}

	return jobs, nil
}

// BuildMediaJobs tạo các media module cron jobs từ config
func (b *CronJobBuilder) BuildMediaJobs() ([]*CronJob, error) {
	cronConfig := b.config.GetCronConfig()
	var jobs []*CronJob

	// Media image optimization job
	if cronConfig.ModuleJobs.Media.ImageOptimization.Enabled {
		handler, err := b.registry.GetHandler(types.CronTaskMediaImageOptimization)
		if err != nil {
			b.logger.Warn("Media image optimization handler not found", "error", err)
		} else {
			job := &CronJob{
				ID:          "media_image_optimization",
				Name:        "Media Image Optimization",
				TaskType:    types.CronTaskMediaImageOptimization,
				Schedule:    cronConfig.ModuleJobs.Media.ImageOptimization.Schedule,
				Enabled:     true,
				Handler:     handler,
				Description: "Optimize images to reduce file size and improve performance",
			}
			jobs = append(jobs, job)
		}
	}

	// Media temp cleanup job
	if cronConfig.ModuleJobs.Media.TempCleanup.Enabled {
		handler, err := b.registry.GetHandler(types.CronTaskMediaTempCleanup)
		if err != nil {
			b.logger.Warn("Media temp cleanup handler not found", "error", err)
		} else {
			job := &CronJob{
				ID:          "media_temp_cleanup",
				Name:        "Media Temp Cleanup",
				TaskType:    types.CronTaskMediaTempCleanup,
				Schedule:    cronConfig.ModuleJobs.Media.TempCleanup.Schedule,
				Enabled:     true,
				Handler:     handler,
				Description: "Clean up temporary media files and uploads",
			}
			jobs = append(jobs, job)
		}
	}

	return jobs, nil
}

// BuildBlogJobs tạo các blog module cron jobs từ config
func (b *CronJobBuilder) BuildBlogJobs() ([]*CronJob, error) {
	cronConfig := b.config.GetCronConfig()
	var jobs []*CronJob

	// Blog process pending schedules job
	if cronConfig.ModuleJobs.Blog.ProcessPendingSchedules.Enabled {
		handler, err := b.registry.GetHandler(types.CronTaskBlogProcessPendingSchedules)
		if err != nil {
			b.logger.Warn("Blog process pending schedules handler not found", "error", err)
		} else {
			job := &CronJob{
				ID:          "blog_process_pending_schedules",
				Name:        "Blog Process Pending Schedules",
				TaskType:    types.CronTaskBlogProcessPendingSchedules,
				Schedule:    cronConfig.ModuleJobs.Blog.ProcessPendingSchedules.Schedule,
				Enabled:     true,
				Handler:     handler,
				Description: "Process pending blog schedules and publish ready posts",
			}
			jobs = append(jobs, job)
		}
	}

	// Blog cleanup old schedules job
	if cronConfig.ModuleJobs.Blog.CleanupOldSchedules.Enabled {
		handler, err := b.registry.GetHandler(types.CronTaskBlogCleanupOldSchedules)
		if err != nil {
			b.logger.Warn("Blog cleanup old schedules handler not found", "error", err)
		} else {
			job := &CronJob{
				ID:          "blog_cleanup_old_schedules",
				Name:        "Blog Cleanup Old Schedules",
				TaskType:    types.CronTaskBlogCleanupOldSchedules,
				Schedule:    cronConfig.ModuleJobs.Blog.CleanupOldSchedules.Schedule,
				Enabled:     true,
				Handler:     handler,
				Description: "Clean up old completed blog schedules",
			}
			jobs = append(jobs, job)
		}
	}

	// Blog publish scheduled posts job
	if cronConfig.ModuleJobs.Blog.PublishScheduledPosts.Enabled {
		handler, err := b.registry.GetHandler(types.CronTaskBlogPublishScheduledPosts)
		if err != nil {
			b.logger.Warn("Blog publish scheduled posts handler not found", "error", err)
		} else {
			job := &CronJob{
				ID:          "blog_publish_scheduled_posts",
				Name:        "Blog Publish Scheduled Posts",
				TaskType:    types.CronTaskBlogPublishScheduledPosts,
				Schedule:    cronConfig.ModuleJobs.Blog.PublishScheduledPosts.Schedule,
				Enabled:     true,
				Handler:     handler,
				Description: "Publish scheduled blog posts that are ready",
			}
			jobs = append(jobs, job)
		}
	}

	return jobs, nil
}

// BuildAllJobs tạo tất cả cron jobs từ config
func (b *CronJobBuilder) BuildAllJobs() ([]*CronJob, error) {
	var allJobs []*CronJob

	// Build system jobs
	systemJobs, err := b.BuildSystemJobs()
	if err != nil {
		return nil, fmt.Errorf("failed to build system jobs: %w", err)
	}
	allJobs = append(allJobs, systemJobs...)

	// Build auth jobs
	authJobs, err := b.BuildAuthJobs()
	if err != nil {
		return nil, fmt.Errorf("failed to build auth jobs: %w", err)
	}
	allJobs = append(allJobs, authJobs...)

	// Build media jobs
	mediaJobs, err := b.BuildMediaJobs()
	if err != nil {
		return nil, fmt.Errorf("failed to build media jobs: %w", err)
	}
	allJobs = append(allJobs, mediaJobs...)

	// Build blog jobs
	blogJobs, err := b.BuildBlogJobs()
	if err != nil {
		return nil, fmt.Errorf("failed to build blog jobs: %w", err)
	}
	allJobs = append(allJobs, blogJobs...)

	b.logger.Info("Built cron jobs from config", "total_jobs", len(allJobs))

	return allJobs, nil
}
