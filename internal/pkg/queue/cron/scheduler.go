package cron

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/queue"
	"wnapi/internal/pkg/queue/types"

	"github.com/robfig/cron/v3"
)

// CronScheduler quản lý việc lập lịch và thực thi các cron jobs
type CronScheduler struct {
	cron     *cron.Cron
	queueMgr *queue.QueueManager
	config   config.Config
	logger   *slog.Logger
	jobs     map[string]*CronJob
	mu       sync.RWMutex
	ctx      context.Context
	cancel   context.CancelFunc
	started  bool
}

// CronJob đại diện cho một cron job
type CronJob struct {
	ID          string
	Name        string
	TaskType    types.CronTaskType
	Schedule    string
	Enabled     bool
	Handler     CronHandler
	LastRun     *time.Time
	NextRun     *time.Time
	LastResult  *types.CronTaskResult
	Description string
	cronID      cron.EntryID
}

// CronHandler interface cho các handler xử lý cron jobs
type CronHandler interface {
	Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error)
	GetTaskType() types.CronTaskType
	GetDescription() string
}

// NewCronScheduler tạo một CronScheduler mới
func NewCronScheduler(queueMgr *queue.QueueManager, config config.Config, logger *slog.Logger) *CronScheduler {
	ctx, cancel := context.WithCancel(context.Background())

	// Tạo cron với timezone từ config
	cronConfig := config.GetCronConfig()
	location, err := time.LoadLocation(cronConfig.TimeZone)
	if err != nil {
		logger.Warn("Invalid timezone, using UTC", "timezone", cronConfig.TimeZone, "error", err)
		location = time.UTC
	}

	c := cron.New(cron.WithLocation(location))

	return &CronScheduler{
		cron:     c,
		queueMgr: queueMgr,
		config:   config,
		logger:   logger,
		jobs:     make(map[string]*CronJob),
		ctx:      ctx,
		cancel:   cancel,
	}
}

// RegisterJob đăng ký một cron job
func (s *CronScheduler) RegisterJob(job *CronJob) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !job.Enabled {
		s.logger.Debug("Skipping disabled cron job", "job", job.Name)
		return nil
	}

	// Validate cron schedule
	if _, err := cron.ParseStandard(job.Schedule); err != nil {
		return fmt.Errorf("invalid cron schedule '%s' for job '%s': %w", job.Schedule, job.Name, err)
	}

	// Tạo wrapper function để thực thi job
	jobFunc := func() {
		s.executeJob(job)
	}

	// Thêm job vào cron scheduler
	cronID, err := s.cron.AddFunc(job.Schedule, jobFunc)
	if err != nil {
		return fmt.Errorf("failed to add cron job '%s': %w", job.Name, err)
	}

	job.cronID = cronID
	s.jobs[job.ID] = job

	// Cập nhật next run time
	if s.started {
		entries := s.cron.Entries()
		for _, entry := range entries {
			if entry.ID == cronID {
				nextRun := entry.Next
				job.NextRun = &nextRun
				break
			}
		}
	}

	s.logger.Info("Registered cron job",
		"job", job.Name,
		"schedule", job.Schedule,
		"task_type", job.TaskType,
		"description", job.Description)

	return nil
}

// UnregisterJob hủy đăng ký một cron job
func (s *CronScheduler) UnregisterJob(jobID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	job, exists := s.jobs[jobID]
	if !exists {
		return fmt.Errorf("job with ID '%s' not found", jobID)
	}

	s.cron.Remove(job.cronID)
	delete(s.jobs, jobID)

	s.logger.Info("Unregistered cron job", "job", job.Name, "id", jobID)
	return nil
}

// Start khởi động cron scheduler
func (s *CronScheduler) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.started {
		return fmt.Errorf("cron scheduler already started")
	}

	s.cron.Start()
	s.started = true

	// Cập nhật next run times cho tất cả jobs
	s.updateNextRunTimes()

	s.logger.Info("Cron scheduler started", "jobs_count", len(s.jobs))
	return nil
}

// Stop dừng cron scheduler
func (s *CronScheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.started {
		return nil
	}

	s.cancel()
	ctx := s.cron.Stop()
	<-ctx.Done()
	s.started = false

	s.logger.Info("Cron scheduler stopped")
	return nil
}

// GetJobs trả về danh sách tất cả jobs
func (s *CronScheduler) GetJobs() []*CronJob {
	s.mu.RLock()
	defer s.mu.RUnlock()

	jobs := make([]*CronJob, 0, len(s.jobs))
	for _, job := range s.jobs {
		jobs = append(jobs, job)
	}
	return jobs
}

// GetJob trả về thông tin của một job cụ thể
func (s *CronScheduler) GetJob(jobID string) (*CronJob, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	job, exists := s.jobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job with ID '%s' not found", jobID)
	}
	return job, nil
}

// executeJob thực thi một cron job
func (s *CronScheduler) executeJob(job *CronJob) {
	startTime := time.Now()

	s.logger.Info("Executing cron job", "job", job.Name, "task_type", job.TaskType)

	// Cập nhật last run time
	s.mu.Lock()
	job.LastRun = &startTime
	s.mu.Unlock()

	// Tạo payload cho job
	payload, err := s.createPayloadForJob(job)
	if err != nil {
		s.logger.Error("Failed to create payload for cron job",
			"job", job.Name,
			"error", err)
		s.recordJobResult(job, false, fmt.Sprintf("Failed to create payload: %v", err), 0, 1, time.Since(startTime))
		return
	}

	// Thực thi job handler
	result, err := job.Handler.Handle(s.ctx, payload)
	if err != nil {
		s.logger.Error("Cron job execution failed",
			"job", job.Name,
			"error", err)
		s.recordJobResult(job, false, fmt.Sprintf("Execution failed: %v", err), 0, 1, time.Since(startTime))
		return
	}

	// Ghi lại kết quả
	s.recordJobResult(job, result.Success, result.Message, result.ProcessedCount, result.ErrorCount, time.Since(startTime))

	s.logger.Info("Cron job completed",
		"job", job.Name,
		"success", result.Success,
		"processed", result.ProcessedCount,
		"errors", result.ErrorCount,
		"duration", time.Since(startTime))
}

// createPayloadForJob tạo payload phù hợp cho job
func (s *CronScheduler) createPayloadForJob(job *CronJob) (types.CronTaskPayload, error) {
	cronConfig := s.config.GetCronConfig()

	switch job.TaskType {
	case types.CronTaskSystemCleanup:
		return types.NewSystemCleanupPayload(
			"24h", // Default max age
			[]string{"logs", "temp_files", "cache"},
			false, // Not dry run
		), nil

	case types.CronTaskSystemHealthCheck:
		return types.NewSystemHealthCheckPayload(
			[]string{"database", "redis", "storage"},
			"30s", // Default timeout
			true,  // Notify on error
		), nil

	case types.CronTaskSystemBackup:
		return types.NewSystemBackupPayload(
			[]string{"database", "config"},
			"/backup", // Default destination
			true,      // Compression
			"30d",     // Retention
		), nil

	case types.CronTaskSystemDemo:
		return types.NewSystemDemoPayload(
			"Demo cron job executed automatically",
			int(time.Now().Unix()%1000), // Use timestamp as counter
		), nil

	case types.CronTaskAuthSessionCleanup:
		return types.NewAuthSessionCleanupPayload(
			cronConfig.ModuleJobs.Auth.SessionCleanup.MaxAge,
			[]string{"expired", "inactive"},
			100, // Default batch size
		), nil

	case types.CronTaskAuthPasswordExpiry:
		return types.NewAuthPasswordExpiryPayload(
			cronConfig.ModuleJobs.Auth.PasswordExpiry.NotifyDays,
			cronConfig.ModuleJobs.Auth.PasswordExpiry.TemplateID,
			50, // Default batch size
		), nil

	case types.CronTaskMediaImageOptimization:
		return types.NewMediaImageOptimizationPayload(
			cronConfig.ModuleJobs.Media.ImageOptimization.Quality,
			cronConfig.ModuleJobs.Media.ImageOptimization.BatchSize,
			10*1024*1024, // 10MB max file size
			[]string{"jpg", "jpeg", "png"},
		), nil

	case types.CronTaskMediaTempCleanup:
		return types.NewMediaTempCleanupPayload(
			cronConfig.ModuleJobs.Media.TempCleanup.MaxAge,
			[]string{"/tmp", "/uploads/temp"},
			[]string{"*.tmp", "*.temp"},
			false, // Not dry run
		), nil

	case types.CronTaskBlogProcessPendingSchedules:
		return types.NewBlogProcessPendingSchedulesPayload(
			0,  // All tenants
			50, // Batch size
		), nil

	case types.CronTaskBlogCleanupOldSchedules:
		return types.NewBlogCleanupOldSchedulesPayload(
			30,    // Retention days
			0,     // All tenants
			100,   // Batch size
			false, // Not dry run
		), nil

	case types.CronTaskBlogPublishScheduledPosts:
		return types.NewBlogPublishScheduledPostsPayload(
			0,  // All tenants
			20, // Batch size
		), nil

	default:
		return nil, fmt.Errorf("unknown task type: %s", job.TaskType)
	}
}

// recordJobResult ghi lại kết quả thực thi job
func (s *CronScheduler) recordJobResult(job *CronJob, success bool, message string, processedCount, errorCount int, duration time.Duration) {
	s.mu.Lock()
	defer s.mu.Unlock()

	result := &types.CronTaskResult{
		TaskType:       job.TaskType,
		Success:        success,
		Message:        message,
		ProcessedCount: processedCount,
		ErrorCount:     errorCount,
		Duration:       duration,
		ExecutedAt:     *job.LastRun,
		CompletedAt:    time.Now(),
	}

	job.LastResult = result
}

// updateNextRunTimes cập nhật next run times cho tất cả jobs
func (s *CronScheduler) updateNextRunTimes() {
	entries := s.cron.Entries()
	for _, job := range s.jobs {
		for _, entry := range entries {
			if entry.ID == job.cronID {
				nextRun := entry.Next
				job.NextRun = &nextRun
				break
			}
		}
	}
}

// IsStarted kiểm tra xem scheduler đã được khởi động chưa
func (s *CronScheduler) IsStarted() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.started
}
