package cron

import (
	"context"
	"log/slog"
	"os"
	"testing"
	"time"

	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/queue/types"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// SimpleConfig implements config.Config for testing
type SimpleConfig struct {
	cronConfig config.CronConfig
}

func (c *SimpleConfig) GetString(key string) string { return "" }
func (c *SimpleConfig) GetStringWithDefault(key string, defaultValue string) string {
	return defaultValue
}
func (c *SimpleConfig) GetInt(key string) int                                 { return 0 }
func (c *SimpleConfig) GetIntWithDefault(key string, defaultValue int) int    { return defaultValue }
func (c *SimpleConfig) GetBool(key string) bool                               { return false }
func (c *SimpleConfig) GetBoolWithDefault(key string, defaultValue bool) bool { return defaultValue }
func (c *SimpleConfig) GetFloat64(key string) float64                         { return 0 }
func (c *SimpleConfig) GetFloat64WithDefault(key string, defaultValue float64) float64 {
	return defaultValue
}
func (c *SimpleConfig) GetDuration(key string) time.Duration { return 0 }
func (c *SimpleConfig) GetDurationWithDefault(key string, defaultValue time.Duration) time.Duration {
	return defaultValue
}
func (c *SimpleConfig) GetStringSlice(key string) []string                         { return nil }
func (c *SimpleConfig) GetStringMap(key string) map[string]interface{}             { return nil }
func (c *SimpleConfig) IsSet(key string) bool                                      { return false }
func (c *SimpleConfig) AllSettings() map[string]interface{}                        { return nil }
func (c *SimpleConfig) Validate() error                                            { return nil }
func (c *SimpleConfig) GetModuleSettings(moduleName string) map[string]interface{} { return nil }
func (c *SimpleConfig) GetQueueConfig() config.QueueConfig                         { return config.QueueConfig{} }
func (c *SimpleConfig) GetCronConfig() config.CronConfig                           { return c.cronConfig }

// SimpleCronHandler implements CronHandler for testing
type SimpleCronHandler struct {
	taskType    types.CronTaskType
	description string
}

func (h *SimpleCronHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
	return &types.CronTaskResult{
		TaskType:    h.taskType,
		Success:     true,
		Message:     "Test execution completed",
		ExecutedAt:  time.Now(),
		CompletedAt: time.Now(),
		Duration:    time.Millisecond,
	}, nil
}

func (h *SimpleCronHandler) GetTaskType() types.CronTaskType {
	return h.taskType
}

func (h *SimpleCronHandler) GetDescription() string {
	return h.description
}

// MockCronHandler implements CronHandler for testing
type MockCronHandler struct {
	mock.Mock
	taskType types.CronTaskType
}

func (m *MockCronHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
	args := m.Called(ctx, payload)
	return args.Get(0).(*types.CronTaskResult), args.Error(1)
}

func (m *MockCronHandler) GetTaskType() types.CronTaskType {
	return m.taskType
}

func (m *MockCronHandler) GetDescription() string {
	args := m.Called()
	return args.String(0)
}

func TestNewCronScheduler(t *testing.T) {
	config := &SimpleConfig{
		cronConfig: config.CronConfig{
			Enabled:  true,
			TimeZone: "UTC",
		},
	}
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	scheduler := NewCronScheduler(nil, config, logger)

	assert.NotNil(t, scheduler)
	assert.NotNil(t, scheduler.cron)
	assert.NotNil(t, scheduler.jobs)
	assert.False(t, scheduler.started)
}

func TestCronScheduler_RegisterJob(t *testing.T) {
	config := &SimpleConfig{
		cronConfig: config.CronConfig{
			Enabled:  true,
			TimeZone: "UTC",
		},
	}
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))
	scheduler := NewCronScheduler(nil, config, logger)

	// Create a test handler
	handler := &SimpleCronHandler{
		taskType:    types.CronTaskSystemCleanup,
		description: "Test handler",
	}

	// Create a test job
	job := &CronJob{
		ID:          "test-job",
		Name:        "Test Job",
		TaskType:    types.CronTaskSystemCleanup,
		Schedule:    "0 2 * * *", // Daily at 2 AM
		Enabled:     true,
		Handler:     handler,
		Description: "Test job description",
	}

	// Register the job
	err := scheduler.RegisterJob(job)
	require.NoError(t, err)

	// Verify job was registered
	jobs := scheduler.GetJobs()
	assert.Len(t, jobs, 1)
	assert.Equal(t, "test-job", jobs[0].ID)
}

func TestCronScheduler_RegisterJob_InvalidSchedule(t *testing.T) {
	mockQueueMgr := &MockQueueManager{}
	mockConfig := &MockConfig{}
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Setup mock expectations
	mockConfig.On("GetCronConfig").Return(config.CronConfig{
		Enabled:  true,
		TimeZone: "UTC",
	})

	scheduler := NewCronScheduler(mockQueueMgr, mockConfig, logger)

	// Create a mock handler
	mockHandler := &MockCronHandler{
		taskType: types.CronTaskSystemCleanup,
	}

	// Create a test job with invalid schedule
	job := &CronJob{
		ID:          "test-job",
		Name:        "Test Job",
		TaskType:    types.CronTaskSystemCleanup,
		Schedule:    "invalid-schedule",
		Enabled:     true,
		Handler:     mockHandler,
		Description: "Test job description",
	}

	// Register the job should fail
	err := scheduler.RegisterJob(job)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid cron schedule")

	mockConfig.AssertExpectations(t)
}

func TestCronScheduler_RegisterJob_Disabled(t *testing.T) {
	mockQueueMgr := &MockQueueManager{}
	mockConfig := &MockConfig{}
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Setup mock expectations
	mockConfig.On("GetCronConfig").Return(config.CronConfig{
		Enabled:  true,
		TimeZone: "UTC",
	})

	scheduler := NewCronScheduler(mockQueueMgr, mockConfig, logger)

	// Create a mock handler
	mockHandler := &MockCronHandler{
		taskType: types.CronTaskSystemCleanup,
	}

	// Create a disabled test job
	job := &CronJob{
		ID:          "test-job",
		Name:        "Test Job",
		TaskType:    types.CronTaskSystemCleanup,
		Schedule:    "0 2 * * *",
		Enabled:     false, // Disabled
		Handler:     mockHandler,
		Description: "Test job description",
	}

	// Register the job
	err := scheduler.RegisterJob(job)
	require.NoError(t, err)

	// Verify no jobs were actually registered (since it's disabled)
	jobs := scheduler.GetJobs()
	assert.Len(t, jobs, 0)

	mockConfig.AssertExpectations(t)
}

func TestCronScheduler_StartStop(t *testing.T) {
	mockQueueMgr := &MockQueueManager{}
	mockConfig := &MockConfig{}
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Setup mock expectations
	mockConfig.On("GetCronConfig").Return(config.CronConfig{
		Enabled:  true,
		TimeZone: "UTC",
	})

	scheduler := NewCronScheduler(mockQueueMgr, mockConfig, logger)

	// Test start
	err := scheduler.Start()
	require.NoError(t, err)
	assert.True(t, scheduler.IsStarted())

	// Test start again (should fail)
	err = scheduler.Start()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "already started")

	// Test stop
	err = scheduler.Stop()
	require.NoError(t, err)
	assert.False(t, scheduler.IsStarted())

	// Test stop again (should not fail)
	err = scheduler.Stop()
	assert.NoError(t, err)

	mockConfig.AssertExpectations(t)
}

func TestCronScheduler_UnregisterJob(t *testing.T) {
	mockQueueMgr := &MockQueueManager{}
	mockConfig := &MockConfig{}
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Setup mock expectations
	mockConfig.On("GetCronConfig").Return(config.CronConfig{
		Enabled:  true,
		TimeZone: "UTC",
	})

	scheduler := NewCronScheduler(mockQueueMgr, mockConfig, logger)

	// Create and register a job
	mockHandler := &MockCronHandler{
		taskType: types.CronTaskSystemCleanup,
	}
	mockHandler.On("GetDescription").Return("Test handler")

	job := &CronJob{
		ID:          "test-job",
		Name:        "Test Job",
		TaskType:    types.CronTaskSystemCleanup,
		Schedule:    "0 2 * * *",
		Enabled:     true,
		Handler:     mockHandler,
		Description: "Test job description",
	}

	err := scheduler.RegisterJob(job)
	require.NoError(t, err)

	// Verify job was registered
	jobs := scheduler.GetJobs()
	assert.Len(t, jobs, 1)

	// Unregister the job
	err = scheduler.UnregisterJob("test-job")
	require.NoError(t, err)

	// Verify job was unregistered
	jobs = scheduler.GetJobs()
	assert.Len(t, jobs, 0)

	// Try to unregister non-existent job
	err = scheduler.UnregisterJob("non-existent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")

	mockConfig.AssertExpectations(t)
	mockHandler.AssertExpectations(t)
}

func TestCronScheduler_GetJob(t *testing.T) {
	mockQueueMgr := &MockQueueManager{}
	mockConfig := &MockConfig{}
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Setup mock expectations
	mockConfig.On("GetCronConfig").Return(config.CronConfig{
		Enabled:  true,
		TimeZone: "UTC",
	})

	scheduler := NewCronScheduler(mockQueueMgr, mockConfig, logger)

	// Create and register a job
	mockHandler := &MockCronHandler{
		taskType: types.CronTaskSystemCleanup,
	}
	mockHandler.On("GetDescription").Return("Test handler")

	job := &CronJob{
		ID:          "test-job",
		Name:        "Test Job",
		TaskType:    types.CronTaskSystemCleanup,
		Schedule:    "0 2 * * *",
		Enabled:     true,
		Handler:     mockHandler,
		Description: "Test job description",
	}

	err := scheduler.RegisterJob(job)
	require.NoError(t, err)

	// Get the job
	retrievedJob, err := scheduler.GetJob("test-job")
	require.NoError(t, err)
	assert.Equal(t, "test-job", retrievedJob.ID)
	assert.Equal(t, "Test Job", retrievedJob.Name)

	// Try to get non-existent job
	_, err = scheduler.GetJob("non-existent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")

	mockConfig.AssertExpectations(t)
	mockHandler.AssertExpectations(t)
}
