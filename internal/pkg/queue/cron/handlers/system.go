package handlers

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"strings"
	"time"

	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/queue/types"

	"gorm.io/gorm"
)

// SystemCleanupHandler xử lý system cleanup tasks
type SystemCleanupHandler struct {
	db     *gorm.DB
	config config.Config
	logger *slog.Logger
}

// NewSystemCleanupHandler tạo SystemCleanupHandler mới
func NewSystemCleanupHandler(db *gorm.DB, config config.Config, logger *slog.Logger) *SystemCleanupHandler {
	return &SystemCleanupHandler{
		db:     db,
		config: config,
		logger: logger,
	}
}

func (h *SystemCleanupHandler) GetTaskType() types.CronTaskType {
	return types.CronTaskSystemCleanup
}

func (h *SystemCleanupHandler) GetDescription() string {
	return "Clean up system logs, temporary files, and cache data"
}

func (h *SystemCleanupHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
	p, ok := payload.(*types.SystemCleanupPayload)
	if !ok {
		return nil, fmt.Errorf("invalid payload type for system cleanup")
	}

	startTime := time.Now()
	var processedCount, errorCount int
	var details = make(map[string]interface{})

	h.logger.Info("Starting system cleanup",
		"max_age", p.MaxAge,
		"cleanup_types", p.CleanupTypes,
		"dry_run", p.DryRun)

	maxAge, err := time.ParseDuration(p.MaxAge)
	if err != nil {
		return nil, fmt.Errorf("invalid max_age duration: %w", err)
	}

	cutoffTime := time.Now().Add(-maxAge)

	for _, cleanupType := range p.CleanupTypes {
		switch cleanupType {
		case "logs":
			count, err := h.cleanupLogs(ctx, cutoffTime, p.DryRun)
			if err != nil {
				h.logger.Error("Failed to cleanup logs", "error", err)
				errorCount++
				details["logs_error"] = err.Error()
			} else {
				processedCount += count
				details["logs_cleaned"] = count
			}

		case "temp_files":
			count, err := h.cleanupTempFiles(ctx, cutoffTime, p.DryRun)
			if err != nil {
				h.logger.Error("Failed to cleanup temp files", "error", err)
				errorCount++
				details["temp_files_error"] = err.Error()
			} else {
				processedCount += count
				details["temp_files_cleaned"] = count
			}

		case "cache":
			count, err := h.cleanupCache(ctx, cutoffTime, p.DryRun)
			if err != nil {
				h.logger.Error("Failed to cleanup cache", "error", err)
				errorCount++
				details["cache_error"] = err.Error()
			} else {
				processedCount += count
				details["cache_cleaned"] = count
			}

		default:
			h.logger.Warn("Unknown cleanup type", "type", cleanupType)
			errorCount++
		}
	}

	success := errorCount == 0
	message := fmt.Sprintf("System cleanup completed. Processed: %d, Errors: %d", processedCount, errorCount)
	if p.DryRun {
		message = "[DRY RUN] " + message
	}

	return &types.CronTaskResult{
		TaskType:       types.CronTaskSystemCleanup,
		Success:        success,
		Message:        message,
		ProcessedCount: processedCount,
		ErrorCount:     errorCount,
		Duration:       time.Since(startTime),
		Details:        details,
		ExecutedAt:     startTime,
		CompletedAt:    time.Now(),
	}, nil
}

func (h *SystemCleanupHandler) cleanupLogs(ctx context.Context, cutoffTime time.Time, dryRun bool) (int, error) {
	// Cleanup application logs (if stored in database)
	// This is a placeholder - implement based on your logging strategy

	logDirs := []string{
		"./logs",
		"/var/log/wnapi",
		"/tmp/wnapi-logs",
	}

	var totalCleaned int

	for _, logDir := range logDirs {
		if _, err := os.Stat(logDir); os.IsNotExist(err) {
			continue
		}

		cleaned, err := h.cleanupDirectory(logDir, cutoffTime, []string{"*.log", "*.log.*"}, dryRun)
		if err != nil {
			h.logger.Warn("Failed to cleanup log directory", "dir", logDir, "error", err)
			continue
		}
		totalCleaned += cleaned
	}

	return totalCleaned, nil
}

func (h *SystemCleanupHandler) cleanupTempFiles(ctx context.Context, cutoffTime time.Time, dryRun bool) (int, error) {
	tempDirs := []string{
		"./tmp",
		"/tmp/wnapi",
		"./uploads/temp",
	}

	var totalCleaned int

	for _, tempDir := range tempDirs {
		if _, err := os.Stat(tempDir); os.IsNotExist(err) {
			continue
		}

		cleaned, err := h.cleanupDirectory(tempDir, cutoffTime, []string{"*.tmp", "*.temp", "upload_*"}, dryRun)
		if err != nil {
			h.logger.Warn("Failed to cleanup temp directory", "dir", tempDir, "error", err)
			continue
		}
		totalCleaned += cleaned
	}

	return totalCleaned, nil
}

func (h *SystemCleanupHandler) cleanupCache(ctx context.Context, cutoffTime time.Time, dryRun bool) (int, error) {
	// Cleanup file-based cache
	cacheDirs := []string{
		"./cache",
		"./storage/cache",
	}

	var totalCleaned int

	for _, cacheDir := range cacheDirs {
		if _, err := os.Stat(cacheDir); os.IsNotExist(err) {
			continue
		}

		cleaned, err := h.cleanupDirectory(cacheDir, cutoffTime, []string{"*"}, dryRun)
		if err != nil {
			h.logger.Warn("Failed to cleanup cache directory", "dir", cacheDir, "error", err)
			continue
		}
		totalCleaned += cleaned
	}

	return totalCleaned, nil
}

func (h *SystemCleanupHandler) cleanupDirectory(dir string, cutoffTime time.Time, patterns []string, dryRun bool) (int, error) {
	var cleaned int

	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		// Check if file matches any pattern
		matched := false
		for _, pattern := range patterns {
			if matched, _ = filepath.Match(pattern, info.Name()); matched {
				break
			}
		}

		if !matched {
			return nil
		}

		// Check if file is older than cutoff time
		if info.ModTime().After(cutoffTime) {
			return nil
		}

		if dryRun {
			h.logger.Debug("Would delete file", "path", path, "size", info.Size(), "modified", info.ModTime())
		} else {
			if err := os.Remove(path); err != nil {
				h.logger.Warn("Failed to delete file", "path", path, "error", err)
				return nil // Continue with other files
			}
			h.logger.Debug("Deleted file", "path", path, "size", info.Size())
		}

		cleaned++
		return nil
	})

	return cleaned, err
}

// SystemHealthCheckHandler xử lý system health check tasks
type SystemHealthCheckHandler struct {
	db     *gorm.DB
	config config.Config
	logger *slog.Logger
}

// NewSystemHealthCheckHandler tạo SystemHealthCheckHandler mới
func NewSystemHealthCheckHandler(db *gorm.DB, config config.Config, logger *slog.Logger) *SystemHealthCheckHandler {
	return &SystemHealthCheckHandler{
		db:     db,
		config: config,
		logger: logger,
	}
}

func (h *SystemHealthCheckHandler) GetTaskType() types.CronTaskType {
	return types.CronTaskSystemHealthCheck
}

func (h *SystemHealthCheckHandler) GetDescription() string {
	return "Check system health including database, Redis, and external services"
}

func (h *SystemHealthCheckHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
	p, ok := payload.(*types.SystemHealthCheckPayload)
	if !ok {
		return nil, fmt.Errorf("invalid payload type for system health check")
	}

	startTime := time.Now()
	var processedCount, errorCount int
	var details = make(map[string]interface{})

	h.logger.Info("Starting system health check", "check_types", p.CheckTypes)

	// Set timeout if specified
	if p.Timeout != "" {
		timeout, err := time.ParseDuration(p.Timeout)
		if err != nil {
			return nil, fmt.Errorf("invalid timeout duration: %w", err)
		}
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	}

	for _, checkType := range p.CheckTypes {
		switch checkType {
		case "database":
			err := h.checkDatabase(ctx)
			if err != nil {
				h.logger.Error("Database health check failed", "error", err)
				errorCount++
				details["database_error"] = err.Error()
			} else {
				processedCount++
				details["database_status"] = "healthy"
			}

		case "redis":
			err := h.checkRedis(ctx)
			if err != nil {
				h.logger.Error("Redis health check failed", "error", err)
				errorCount++
				details["redis_error"] = err.Error()
			} else {
				processedCount++
				details["redis_status"] = "healthy"
			}

		case "storage":
			err := h.checkStorage(ctx)
			if err != nil {
				h.logger.Error("Storage health check failed", "error", err)
				errorCount++
				details["storage_error"] = err.Error()
			} else {
				processedCount++
				details["storage_status"] = "healthy"
			}

		case "external_apis":
			err := h.checkExternalAPIs(ctx)
			if err != nil {
				h.logger.Error("External APIs health check failed", "error", err)
				errorCount++
				details["external_apis_error"] = err.Error()
			} else {
				processedCount++
				details["external_apis_status"] = "healthy"
			}

		default:
			h.logger.Warn("Unknown health check type", "type", checkType)
			errorCount++
		}
	}

	success := errorCount == 0
	message := fmt.Sprintf("Health check completed. Healthy: %d, Issues: %d", processedCount, errorCount)

	// Send notification if there are errors and notification is enabled
	if !success && p.NotifyOnError {
		h.sendHealthCheckNotification(details)
	}

	return &types.CronTaskResult{
		TaskType:       types.CronTaskSystemHealthCheck,
		Success:        success,
		Message:        message,
		ProcessedCount: processedCount,
		ErrorCount:     errorCount,
		Duration:       time.Since(startTime),
		Details:        details,
		ExecutedAt:     startTime,
		CompletedAt:    time.Now(),
	}, nil
}

func (h *SystemHealthCheckHandler) checkDatabase(ctx context.Context) error {
	sqlDB, err := h.db.DB()
	if err != nil {
		return fmt.Errorf("failed to get database connection: %w", err)
	}

	if err := sqlDB.PingContext(ctx); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}

func (h *SystemHealthCheckHandler) checkRedis(ctx context.Context) error {
	// This is a placeholder - implement Redis health check based on your Redis client
	// For example, if using go-redis:
	// return h.redisClient.Ping(ctx).Err()

	h.logger.Debug("Redis health check - placeholder implementation")
	return nil
}

func (h *SystemHealthCheckHandler) checkStorage(ctx context.Context) error {
	// Check if storage directories are accessible and writable
	storagePaths := []string{
		"./uploads",
		"./storage",
		"./tmp",
	}

	for _, path := range storagePaths {
		if err := h.checkStoragePath(path); err != nil {
			return fmt.Errorf("storage path '%s' check failed: %w", path, err)
		}
	}

	return nil
}

func (h *SystemHealthCheckHandler) checkStoragePath(path string) error {
	// Check if directory exists
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return fmt.Errorf("directory does not exist: %s", path)
	}

	// Check if directory is writable
	testFile := filepath.Join(path, ".health_check_test")
	file, err := os.Create(testFile)
	if err != nil {
		return fmt.Errorf("directory is not writable: %s", path)
	}
	file.Close()
	os.Remove(testFile)

	return nil
}

func (h *SystemHealthCheckHandler) checkExternalAPIs(ctx context.Context) error {
	// This is a placeholder for checking external API dependencies
	// Implement based on your specific external services

	h.logger.Debug("External APIs health check - placeholder implementation")
	return nil
}

func (h *SystemHealthCheckHandler) sendHealthCheckNotification(details map[string]interface{}) {
	// This is a placeholder for sending health check notifications
	// Implement based on your notification system

	var issues []string
	for key, value := range details {
		if strings.HasSuffix(key, "_error") {
			issues = append(issues, fmt.Sprintf("%s: %v", key, value))
		}
	}

	h.logger.Warn("Health check issues detected", "issues", issues)
	// FUTURE: Send notification via email, Slack, etc.
	// Can integrate with notification module when needed
}

// SystemBackupHandler xử lý system backup tasks
type SystemBackupHandler struct {
	db     *gorm.DB
	config config.Config
	logger *slog.Logger
}

// NewSystemBackupHandler tạo SystemBackupHandler mới
func NewSystemBackupHandler(db *gorm.DB, config config.Config, logger *slog.Logger) *SystemBackupHandler {
	return &SystemBackupHandler{
		db:     db,
		config: config,
		logger: logger,
	}
}

func (h *SystemBackupHandler) GetTaskType() types.CronTaskType {
	return types.CronTaskSystemBackup
}

func (h *SystemBackupHandler) GetDescription() string {
	return "Backup system data including database and configuration files"
}

func (h *SystemBackupHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
	p, ok := payload.(*types.SystemBackupPayload)
	if !ok {
		return nil, fmt.Errorf("invalid payload type for system backup")
	}

	startTime := time.Now()
	var processedCount, errorCount int
	var details = make(map[string]interface{})

	h.logger.Info("Starting system backup",
		"backup_types", p.BackupTypes,
		"destination", p.Destination,
		"compression", p.Compression)

	// Create backup directory if it doesn't exist
	if err := os.MkdirAll(p.Destination, 0755); err != nil {
		return nil, fmt.Errorf("failed to create backup directory: %w", err)
	}

	timestamp := time.Now().Format("20060102_150405")

	for _, backupType := range p.BackupTypes {
		switch backupType {
		case "database":
			backupPath, err := h.backupDatabase(ctx, p.Destination, timestamp, p.Compression)
			if err != nil {
				h.logger.Error("Database backup failed", "error", err)
				errorCount++
				details["database_error"] = err.Error()
			} else {
				processedCount++
				details["database_backup"] = backupPath
			}

		case "files":
			backupPath, err := h.backupFiles(ctx, p.Destination, timestamp, p.Compression)
			if err != nil {
				h.logger.Error("Files backup failed", "error", err)
				errorCount++
				details["files_error"] = err.Error()
			} else {
				processedCount++
				details["files_backup"] = backupPath
			}

		case "config":
			backupPath, err := h.backupConfig(ctx, p.Destination, timestamp, p.Compression)
			if err != nil {
				h.logger.Error("Config backup failed", "error", err)
				errorCount++
				details["config_error"] = err.Error()
			} else {
				processedCount++
				details["config_backup"] = backupPath
			}

		default:
			h.logger.Warn("Unknown backup type", "type", backupType)
			errorCount++
		}
	}

	// Cleanup old backups based on retention policy
	if p.Retention != "" {
		if err := h.cleanupOldBackups(p.Destination, p.Retention); err != nil {
			h.logger.Warn("Failed to cleanup old backups", "error", err)
		}
	}

	success := errorCount == 0
	message := fmt.Sprintf("System backup completed. Success: %d, Errors: %d", processedCount, errorCount)

	return &types.CronTaskResult{
		TaskType:       types.CronTaskSystemBackup,
		Success:        success,
		Message:        message,
		ProcessedCount: processedCount,
		ErrorCount:     errorCount,
		Duration:       time.Since(startTime),
		Details:        details,
		ExecutedAt:     startTime,
		CompletedAt:    time.Now(),
	}, nil
}

func (h *SystemBackupHandler) backupDatabase(ctx context.Context, destination, timestamp string, compression bool) (string, error) {
	// This is a placeholder for database backup
	// Implement based on your database type (MySQL, PostgreSQL, etc.)

	backupFile := filepath.Join(destination, fmt.Sprintf("database_%s.sql", timestamp))
	if compression {
		backupFile += ".gz"
	}

	h.logger.Info("Creating database backup", "file", backupFile)

	// FUTURE: Implement actual database backup logic
	// For MySQL: mysqldump command
	// For PostgreSQL: pg_dump command
	// Currently creating placeholder file

	// Create a placeholder file for now
	file, err := os.Create(backupFile)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// Write some placeholder content
	_, err = file.WriteString(fmt.Sprintf("-- Database backup created at %s\n", time.Now().Format(time.RFC3339)))
	if err != nil {
		return "", err
	}

	return backupFile, nil
}

func (h *SystemBackupHandler) backupFiles(ctx context.Context, destination, timestamp string, compression bool) (string, error) {
	// Backup important application files
	backupFile := filepath.Join(destination, fmt.Sprintf("files_%s.tar", timestamp))
	if compression {
		backupFile += ".gz"
	}

	h.logger.Info("Creating files backup", "file", backupFile)

	// FUTURE: Implement file backup using tar/zip
	// Currently creating placeholder file

	file, err := os.Create(backupFile)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// Write placeholder content
	_, err = file.WriteString(fmt.Sprintf("Files backup created at %s\n", time.Now().Format(time.RFC3339)))
	if err != nil {
		return "", err
	}

	return backupFile, nil
}

func (h *SystemBackupHandler) backupConfig(ctx context.Context, destination, timestamp string, compression bool) (string, error) {
	// Backup configuration files
	backupFile := filepath.Join(destination, fmt.Sprintf("config_%s.tar", timestamp))
	if compression {
		backupFile += ".gz"
	}

	h.logger.Info("Creating config backup", "file", backupFile)

	// FUTURE: Implement config backup
	// Should backup .env files, config files, etc.
	// Currently creating placeholder file

	file, err := os.Create(backupFile)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// Write placeholder content
	_, err = file.WriteString(fmt.Sprintf("Config backup created at %s\n", time.Now().Format(time.RFC3339)))
	if err != nil {
		return "", err
	}

	return backupFile, nil
}

func (h *SystemBackupHandler) cleanupOldBackups(destination, retention string) error {
	retentionDuration, err := time.ParseDuration(retention)
	if err != nil {
		return fmt.Errorf("invalid retention duration: %w", err)
	}

	cutoffTime := time.Now().Add(-retentionDuration)

	return filepath.Walk(destination, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		// Check if file is a backup file and older than retention period
		if strings.Contains(info.Name(), "database_") ||
			strings.Contains(info.Name(), "files_") ||
			strings.Contains(info.Name(), "config_") {
			if info.ModTime().Before(cutoffTime) {
				h.logger.Info("Removing old backup", "file", path, "age", time.Since(info.ModTime()))
				return os.Remove(path)
			}
		}

		return nil
	})
}

// SystemDemoHandler xử lý demo tasks
type SystemDemoHandler struct {
	db     *gorm.DB
	config config.Config
	logger *slog.Logger
}

// NewSystemDemoHandler tạo SystemDemoHandler mới
func NewSystemDemoHandler(db *gorm.DB, config config.Config, logger *slog.Logger) *SystemDemoHandler {
	return &SystemDemoHandler{
		db:     db,
		config: config,
		logger: logger,
	}
}

func (h *SystemDemoHandler) GetTaskType() types.CronTaskType {
	return types.CronTaskSystemDemo
}

func (h *SystemDemoHandler) GetDescription() string {
	return "Demo cron task that runs every minute for demonstration purposes"
}

func (h *SystemDemoHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
	p, ok := payload.(*types.SystemDemoPayload)
	if !ok {
		return nil, fmt.Errorf("invalid payload type for system demo")
	}

	startTime := time.Now()
	var details = make(map[string]interface{})

	h.logger.Info("Demo cron job executed",
		"message", p.Message,
		"counter", p.Counter,
		"timestamp", startTime.Format("2006-01-02 15:04:05"))

	// Simulate some work
	time.Sleep(100 * time.Millisecond)

	details["message"] = p.Message
	details["counter"] = p.Counter
	details["execution_time"] = startTime.Format("2006-01-02 15:04:05")
	details["demo_data"] = map[string]interface{}{
		"random_number": startTime.Unix() % 1000,
		"status":        "completed",
	}

	return &types.CronTaskResult{
		TaskType:       types.CronTaskSystemDemo,
		Success:        true,
		Message:        fmt.Sprintf("Demo task completed successfully: %s", p.Message),
		ProcessedCount: 1,
		ErrorCount:     0,
		Duration:       time.Since(startTime),
		Details:        details,
		ExecutedAt:     startTime,
		CompletedAt:    time.Now(),
	}, nil
}
