package types

import (
	"encoding/json"
	"errors"
	"time"
)

// Error constants for cron tasks
var (
	ErrInvalidPayload  = errors.New("invalid payload")
	ErrInvalidTaskType = errors.New("invalid task type")
)

// CronTaskType định ngh<PERSON>a <PERSON> task cron
type CronTaskType string

const (
	// System cron tasks
	CronTaskSystemCleanup     CronTaskType = "system:cleanup"
	CronTaskSystemHealthCheck CronTaskType = "system:health_check"
	CronTaskSystemBackup      CronTaskType = "system:backup"
	CronTaskSystemDemo        CronTaskType = "system:demo"

	// Auth module cron tasks
	CronTaskAuthSessionCleanup CronTaskType = "auth:session_cleanup"
	CronTaskAuthPasswordExpiry CronTaskType = "auth:password_expiry"

	// Media module cron tasks
	CronTaskMediaImageOptimization CronTaskType = "media:image_optimization"
	CronTaskMediaTempCleanup       CronTaskType = "media:temp_cleanup"

	// Blog module cron tasks
	CronTaskBlogProcessPendingSchedules CronTaskType = "blog:process_pending_schedules"
	CronTaskBlogCleanupOldSchedules     CronTaskType = "blog:cleanup_old_schedules"
	CronTaskBlogPublishScheduledPosts   CronTaskType = "blog:publish_scheduled_posts"
)

// CronTaskPayload là interface chung cho tất cả cron task payloads
type CronTaskPayload interface {
	GetTaskType() CronTaskType
	Validate() error
}

// SystemCleanupPayload payload cho system cleanup task
type SystemCleanupPayload struct {
	TaskType     CronTaskType `json:"task_type"`
	MaxAge       string       `json:"max_age"`       // Duration string như "24h", "7d"
	CleanupTypes []string     `json:"cleanup_types"` // ["logs", "temp_files", "cache"]
	DryRun       bool         `json:"dry_run"`       // Chỉ kiểm tra, không thực sự xóa
	ExecutedAt   time.Time    `json:"executed_at"`
}

func (p SystemCleanupPayload) GetTaskType() CronTaskType {
	return CronTaskSystemCleanup
}

func (p SystemCleanupPayload) Validate() error {
	if p.MaxAge == "" {
		return ErrInvalidPayload
	}
	if _, err := time.ParseDuration(p.MaxAge); err != nil {
		return ErrInvalidPayload
	}
	return nil
}

// SystemHealthCheckPayload payload cho system health check task
type SystemHealthCheckPayload struct {
	TaskType      CronTaskType `json:"task_type"`
	CheckTypes    []string     `json:"check_types"` // ["database", "redis", "storage", "external_apis"]
	Timeout       string       `json:"timeout"`     // Duration string như "30s"
	NotifyOnError bool         `json:"notify_on_error"`
	ExecutedAt    time.Time    `json:"executed_at"`
}

func (p SystemHealthCheckPayload) GetTaskType() CronTaskType {
	return CronTaskSystemHealthCheck
}

func (p SystemHealthCheckPayload) Validate() error {
	if p.Timeout != "" {
		if _, err := time.ParseDuration(p.Timeout); err != nil {
			return ErrInvalidPayload
		}
	}
	return nil
}

// SystemBackupPayload payload cho system backup task
type SystemBackupPayload struct {
	TaskType    CronTaskType `json:"task_type"`
	BackupTypes []string     `json:"backup_types"` // ["database", "files", "config"]
	Destination string       `json:"destination"`  // Đường dẫn hoặc URL backup
	Compression bool         `json:"compression"`
	Retention   string       `json:"retention"` // Duration string như "30d"
	ExecutedAt  time.Time    `json:"executed_at"`
}

func (p SystemBackupPayload) GetTaskType() CronTaskType {
	return CronTaskSystemBackup
}

// SystemDemoPayload payload cho system demo task
type SystemDemoPayload struct {
	TaskType   CronTaskType `json:"task_type"`
	Message    string       `json:"message"`
	Counter    int          `json:"counter"`
	ExecutedAt time.Time    `json:"executed_at"`
}

func (p SystemDemoPayload) GetTaskType() CronTaskType {
	return CronTaskSystemDemo
}

func (p SystemDemoPayload) Validate() error {
	if p.Message == "" {
		p.Message = "Demo cron job executed"
	}
	return nil
}

func (p SystemBackupPayload) Validate() error {
	if p.Destination == "" {
		return ErrInvalidPayload
	}
	if p.Retention != "" {
		if _, err := time.ParseDuration(p.Retention); err != nil {
			return ErrInvalidPayload
		}
	}
	return nil
}

// AuthSessionCleanupPayload payload cho auth session cleanup task
type AuthSessionCleanupPayload struct {
	TaskType     CronTaskType `json:"task_type"`
	MaxAge       string       `json:"max_age"`       // Duration string như "24h"
	SessionTypes []string     `json:"session_types"` // ["expired", "inactive", "revoked"]
	BatchSize    int          `json:"batch_size"`
	ExecutedAt   time.Time    `json:"executed_at"`
}

func (p AuthSessionCleanupPayload) GetTaskType() CronTaskType {
	return CronTaskAuthSessionCleanup
}

func (p AuthSessionCleanupPayload) Validate() error {
	if p.MaxAge == "" {
		return ErrInvalidPayload
	}
	if _, err := time.ParseDuration(p.MaxAge); err != nil {
		return ErrInvalidPayload
	}
	if p.BatchSize <= 0 {
		p.BatchSize = 100 // Default batch size
	}
	return nil
}

// AuthPasswordExpiryPayload payload cho auth password expiry notification task
type AuthPasswordExpiryPayload struct {
	TaskType   CronTaskType `json:"task_type"`
	NotifyDays []int        `json:"notify_days"` // [7, 3, 1] - notify X days before expiry
	TemplateID string       `json:"template_id"` // Email template ID
	BatchSize  int          `json:"batch_size"`
	ExecutedAt time.Time    `json:"executed_at"`
}

func (p AuthPasswordExpiryPayload) GetTaskType() CronTaskType {
	return CronTaskAuthPasswordExpiry
}

func (p AuthPasswordExpiryPayload) Validate() error {
	if len(p.NotifyDays) == 0 {
		return ErrInvalidPayload
	}
	if p.TemplateID == "" {
		return ErrInvalidPayload
	}
	if p.BatchSize <= 0 {
		p.BatchSize = 50 // Default batch size
	}
	return nil
}

// MediaImageOptimizationPayload payload cho media image optimization task
type MediaImageOptimizationPayload struct {
	TaskType    CronTaskType `json:"task_type"`
	Quality     int          `json:"quality"`       // JPEG quality 1-100
	BatchSize   int          `json:"batch_size"`    // Number of images to process per batch
	MaxFileSize int64        `json:"max_file_size"` // Maximum file size to process (bytes)
	Formats     []string     `json:"formats"`       // ["jpg", "jpeg", "png", "webp"]
	ExecutedAt  time.Time    `json:"executed_at"`
}

func (p MediaImageOptimizationPayload) GetTaskType() CronTaskType {
	return CronTaskMediaImageOptimization
}

func (p MediaImageOptimizationPayload) Validate() error {
	if p.Quality < 1 || p.Quality > 100 {
		return ErrInvalidPayload
	}
	if p.BatchSize <= 0 {
		p.BatchSize = 100 // Default batch size
	}
	return nil
}

// MediaTempCleanupPayload payload cho media temp cleanup task
type MediaTempCleanupPayload struct {
	TaskType     CronTaskType `json:"task_type"`
	MaxAge       string       `json:"max_age"`       // Duration string như "24h"
	Directories  []string     `json:"directories"`   // Directories to clean
	FilePatterns []string     `json:"file_patterns"` // File patterns to match
	DryRun       bool         `json:"dry_run"`
	ExecutedAt   time.Time    `json:"executed_at"`
}

func (p MediaTempCleanupPayload) GetTaskType() CronTaskType {
	return CronTaskMediaTempCleanup
}

func (p MediaTempCleanupPayload) Validate() error {
	if p.MaxAge == "" {
		return ErrInvalidPayload
	}
	if _, err := time.ParseDuration(p.MaxAge); err != nil {
		return ErrInvalidPayload
	}
	return nil
}

// BlogProcessPendingSchedulesPayload payload cho blog process pending schedules task
type BlogProcessPendingSchedulesPayload struct {
	TaskType   CronTaskType `json:"task_type"`
	TenantID   uint         `json:"tenant_id,omitempty"` // 0 for all tenants
	BatchSize  int          `json:"batch_size"`
	ExecutedAt time.Time    `json:"executed_at"`
}

func (p BlogProcessPendingSchedulesPayload) GetTaskType() CronTaskType {
	return CronTaskBlogProcessPendingSchedules
}

func (p BlogProcessPendingSchedulesPayload) Validate() error {
	if p.BatchSize <= 0 {
		p.BatchSize = 50 // Default batch size
	}
	return nil
}

// BlogCleanupOldSchedulesPayload payload cho blog cleanup old schedules task
type BlogCleanupOldSchedulesPayload struct {
	TaskType      CronTaskType `json:"task_type"`
	RetentionDays int          `json:"retention_days"` // Keep schedules for X days
	TenantID      uint         `json:"tenant_id,omitempty"` // 0 for all tenants
	BatchSize     int          `json:"batch_size"`
	DryRun        bool         `json:"dry_run"`
	ExecutedAt    time.Time    `json:"executed_at"`
}

func (p BlogCleanupOldSchedulesPayload) GetTaskType() CronTaskType {
	return CronTaskBlogCleanupOldSchedules
}

func (p BlogCleanupOldSchedulesPayload) Validate() error {
	if p.RetentionDays <= 0 {
		p.RetentionDays = 30 // Default 30 days
	}
	if p.BatchSize <= 0 {
		p.BatchSize = 100 // Default batch size
	}
	return nil
}

// BlogPublishScheduledPostsPayload payload cho blog publish scheduled posts task
type BlogPublishScheduledPostsPayload struct {
	TaskType   CronTaskType `json:"task_type"`
	TenantID   uint         `json:"tenant_id,omitempty"` // 0 for all tenants
	BatchSize  int          `json:"batch_size"`
	ExecutedAt time.Time    `json:"executed_at"`
}

func (p BlogPublishScheduledPostsPayload) GetTaskType() CronTaskType {
	return CronTaskBlogPublishScheduledPosts
}

func (p BlogPublishScheduledPostsPayload) Validate() error {
	if p.BatchSize <= 0 {
		p.BatchSize = 20 // Default batch size
	}
	return nil
}

// CronTaskResult kết quả thực thi cron task
type CronTaskResult struct {
	TaskType       CronTaskType           `json:"task_type"`
	Success        bool                   `json:"success"`
	Message        string                 `json:"message"`
	ProcessedCount int                    `json:"processed_count"`
	ErrorCount     int                    `json:"error_count"`
	Duration       time.Duration          `json:"duration"`
	Details        map[string]interface{} `json:"details,omitempty"`
	ExecutedAt     time.Time              `json:"executed_at"`
	CompletedAt    time.Time              `json:"completed_at"`
}

// CronTaskInfo thông tin về cron task
type CronTaskInfo struct {
	Name        string          `json:"name"`
	TaskType    CronTaskType    `json:"task_type"`
	Schedule    string          `json:"schedule"` // Cron expression
	Enabled     bool            `json:"enabled"`
	LastRun     *time.Time      `json:"last_run,omitempty"`
	NextRun     *time.Time      `json:"next_run,omitempty"`
	LastResult  *CronTaskResult `json:"last_result,omitempty"`
	Description string          `json:"description"`
}

// Helper functions for creating payloads
func NewSystemCleanupPayload(maxAge string, cleanupTypes []string, dryRun bool) *SystemCleanupPayload {
	return &SystemCleanupPayload{
		TaskType:     CronTaskSystemCleanup,
		MaxAge:       maxAge,
		CleanupTypes: cleanupTypes,
		DryRun:       dryRun,
		ExecutedAt:   time.Now(),
	}
}

func NewSystemHealthCheckPayload(checkTypes []string, timeout string, notifyOnError bool) *SystemHealthCheckPayload {
	return &SystemHealthCheckPayload{
		TaskType:      CronTaskSystemHealthCheck,
		CheckTypes:    checkTypes,
		Timeout:       timeout,
		NotifyOnError: notifyOnError,
		ExecutedAt:    time.Now(),
	}
}

func NewSystemBackupPayload(backupTypes []string, destination string, compression bool, retention string) *SystemBackupPayload {
	return &SystemBackupPayload{
		TaskType:    CronTaskSystemBackup,
		BackupTypes: backupTypes,
		Destination: destination,
		Compression: compression,
		Retention:   retention,
		ExecutedAt:  time.Now(),
	}
}

func NewSystemDemoPayload(message string, counter int) *SystemDemoPayload {
	return &SystemDemoPayload{
		TaskType:   CronTaskSystemDemo,
		Message:    message,
		Counter:    counter,
		ExecutedAt: time.Now(),
	}
}

func NewAuthSessionCleanupPayload(maxAge string, sessionTypes []string, batchSize int) *AuthSessionCleanupPayload {
	return &AuthSessionCleanupPayload{
		TaskType:     CronTaskAuthSessionCleanup,
		MaxAge:       maxAge,
		SessionTypes: sessionTypes,
		BatchSize:    batchSize,
		ExecutedAt:   time.Now(),
	}
}

func NewAuthPasswordExpiryPayload(notifyDays []int, templateID string, batchSize int) *AuthPasswordExpiryPayload {
	return &AuthPasswordExpiryPayload{
		TaskType:   CronTaskAuthPasswordExpiry,
		NotifyDays: notifyDays,
		TemplateID: templateID,
		BatchSize:  batchSize,
		ExecutedAt: time.Now(),
	}
}

func NewMediaImageOptimizationPayload(quality, batchSize int, maxFileSize int64, formats []string) *MediaImageOptimizationPayload {
	return &MediaImageOptimizationPayload{
		TaskType:    CronTaskMediaImageOptimization,
		Quality:     quality,
		BatchSize:   batchSize,
		MaxFileSize: maxFileSize,
		Formats:     formats,
		ExecutedAt:  time.Now(),
	}
}

func NewMediaTempCleanupPayload(maxAge string, directories, filePatterns []string, dryRun bool) *MediaTempCleanupPayload {
	return &MediaTempCleanupPayload{
		TaskType:     CronTaskMediaTempCleanup,
		MaxAge:       maxAge,
		Directories:  directories,
		FilePatterns: filePatterns,
		DryRun:       dryRun,
		ExecutedAt:   time.Now(),
	}
}

func NewBlogProcessPendingSchedulesPayload(tenantID uint, batchSize int) *BlogProcessPendingSchedulesPayload {
	return &BlogProcessPendingSchedulesPayload{
		TaskType:   CronTaskBlogProcessPendingSchedules,
		TenantID:   tenantID,
		BatchSize:  batchSize,
		ExecutedAt: time.Now(),
	}
}

func NewBlogCleanupOldSchedulesPayload(retentionDays int, tenantID uint, batchSize int, dryRun bool) *BlogCleanupOldSchedulesPayload {
	return &BlogCleanupOldSchedulesPayload{
		TaskType:      CronTaskBlogCleanupOldSchedules,
		RetentionDays: retentionDays,
		TenantID:      tenantID,
		BatchSize:     batchSize,
		DryRun:        dryRun,
		ExecutedAt:    time.Now(),
	}
}

func NewBlogPublishScheduledPostsPayload(tenantID uint, batchSize int) *BlogPublishScheduledPostsPayload {
	return &BlogPublishScheduledPostsPayload{
		TaskType:   CronTaskBlogPublishScheduledPosts,
		TenantID:   tenantID,
		BatchSize:  batchSize,
		ExecutedAt: time.Now(),
	}
}

// ParseCronTaskPayload parses JSON data into appropriate payload type
func ParseCronTaskPayload(taskType CronTaskType, data []byte) (CronTaskPayload, error) {
	switch taskType {
	case CronTaskSystemCleanup:
		var payload SystemCleanupPayload
		if err := json.Unmarshal(data, &payload); err != nil {
			return nil, err
		}
		return payload, payload.Validate()

	case CronTaskSystemHealthCheck:
		var payload SystemHealthCheckPayload
		if err := json.Unmarshal(data, &payload); err != nil {
			return nil, err
		}
		return payload, payload.Validate()

	case CronTaskSystemBackup:
		var payload SystemBackupPayload
		if err := json.Unmarshal(data, &payload); err != nil {
			return nil, err
		}
		return payload, payload.Validate()

	case CronTaskSystemDemo:
		var payload SystemDemoPayload
		if err := json.Unmarshal(data, &payload); err != nil {
			return nil, err
		}
		return payload, payload.Validate()

	case CronTaskAuthSessionCleanup:
		var payload AuthSessionCleanupPayload
		if err := json.Unmarshal(data, &payload); err != nil {
			return nil, err
		}
		return payload, payload.Validate()

	case CronTaskAuthPasswordExpiry:
		var payload AuthPasswordExpiryPayload
		if err := json.Unmarshal(data, &payload); err != nil {
			return nil, err
		}
		return payload, payload.Validate()

	case CronTaskMediaImageOptimization:
		var payload MediaImageOptimizationPayload
		if err := json.Unmarshal(data, &payload); err != nil {
			return nil, err
		}
		return payload, payload.Validate()

	case CronTaskMediaTempCleanup:
		var payload MediaTempCleanupPayload
		if err := json.Unmarshal(data, &payload); err != nil {
			return nil, err
		}
		return payload, payload.Validate()

	case CronTaskBlogProcessPendingSchedules:
		var payload BlogProcessPendingSchedulesPayload
		if err := json.Unmarshal(data, &payload); err != nil {
			return nil, err
		}
		return payload, payload.Validate()

	case CronTaskBlogCleanupOldSchedules:
		var payload BlogCleanupOldSchedulesPayload
		if err := json.Unmarshal(data, &payload); err != nil {
			return nil, err
		}
		return payload, payload.Validate()

	case CronTaskBlogPublishScheduledPosts:
		var payload BlogPublishScheduledPostsPayload
		if err := json.Unmarshal(data, &payload); err != nil {
			return nil, err
		}
		return payload, payload.Validate()

	default:
		return nil, ErrInvalidTaskType
	}
}
