package pagination

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"time"
	"wnapi/internal/pkg/errors"

	"github.com/gin-gonic/gin"
)

const (
	DefaultLimit = 10
	MaxLimit     = 100
)

// CursorType định nghĩa loại cursor
type CursorType string

const (
	CursorTypeID        CursorType = "id"
	CursorTypeCreatedAt CursorType = "created_at"
	CursorTypeName      CursorType = "name"
	CursorTypeCode      CursorType = "code"
	CursorTypeUpdatedAt CursorType = "updated_at"
)

// Params holds the pagination parameters
type Params struct {
	Cursor string
	Limit  int
}

// CursorInfo holds cursor-based pagination information
type CursorInfo struct {
	NextCursor string `json:"next_cursor"`
	HasMore    bool   `json:"has_more"`
}

// Cursor đại diện cho cursor pagination với type safety
type Cursor struct {
	Type      CursorType  `json:"type"`
	Value     interface{} `json:"value"`
	Direction string      `json:"direction"` // "next" hoặc "prev"
}

// ParseFromRequest extracts pagination parameters from the request
func ParseFromRequest(c *gin.Context) Params {
	cursor := c.Query("cursor")
	limitStr := c.Query("limit")

	limit := DefaultLimit
	if limitStr != "" {
		parsedLimit, err := strconv.Atoi(limitStr)
		if err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	if limit > MaxLimit {
		limit = MaxLimit
	}

	return Params{
		Cursor: cursor,
		Limit:  limit,
	}
}

// EncodeCursor encodes a cursor value (typically an ID or timestamp)
func EncodeCursor(value string) string {
	return base64.StdEncoding.EncodeToString([]byte(value))
}

// DecodeSimpleCursor decodes a simple cursor back to its original value (legacy)
func DecodeSimpleCursor(cursor string) (string, error) {
	decoded, err := base64.StdEncoding.DecodeString(cursor)
	if err != nil {
		return "", errors.New(errors.ErrCodeInvalidInput, "Invalid cursor format", err)
	}
	return string(decoded), nil
}

// CreateNextCursor creates a next cursor based on the last item in the result set
// The idExtractor function should extract the ID or value to be used as cursor from the last item
func CreateNextCursor(results []interface{}, idExtractor func(interface{}) string) string {
	if len(results) == 0 {
		return ""
	}

	lastItem := results[len(results)-1]
	id := idExtractor(lastItem)
	return EncodeCursor(id)
}

// ===== NEW CURSOR METHODS =====

// IDCursor tạo cursor dựa trên ID
func IDCursor(id uint, direction string) *Cursor {
	return &Cursor{
		Type:      CursorTypeID,
		Value:     id,
		Direction: direction,
	}
}

// CreatedAtCursor tạo cursor dựa trên created_at
func CreatedAtCursor(createdAt time.Time, direction string) *Cursor {
	return &Cursor{
		Type:      CursorTypeCreatedAt,
		Value:     createdAt.Unix(),
		Direction: direction,
	}
}

// NameCursor tạo cursor dựa trên name
func NameCursor(name string, direction string) *Cursor {
	return &Cursor{
		Type:      CursorTypeName,
		Value:     name,
		Direction: direction,
	}
}

// CodeCursor tạo cursor dựa trên code
func CodeCursor(code string, direction string) *Cursor {
	return &Cursor{
		Type:      CursorTypeCode,
		Value:     code,
		Direction: direction,
	}
}

// Encode mã hóa cursor thành string
func (c *Cursor) Encode() (string, error) {
	if c == nil {
		return "", nil
	}

	data, err := json.Marshal(c)
	if err != nil {
		return "", fmt.Errorf("failed to marshal cursor: %w", err)
	}

	return base64.URLEncoding.EncodeToString(data), nil
}

// DecodeCursor giải mã cursor từ string
func DecodeCursor(encoded string) (*Cursor, error) {
	if encoded == "" {
		return nil, nil
	}

	data, err := base64.URLEncoding.DecodeString(encoded)
	if err != nil {
		return nil, fmt.Errorf("failed to decode cursor: %w", err)
	}

	var cursor Cursor
	if err := json.Unmarshal(data, &cursor); err != nil {
		return nil, fmt.Errorf("failed to unmarshal cursor: %w", err)
	}

	return &cursor, nil
}

// GetIDValue trả về giá trị ID từ cursor
func (c *Cursor) GetIDValue() (uint, error) {
	if c == nil || c.Type != CursorTypeID {
		return 0, fmt.Errorf("cursor is not ID type")
	}

	switch v := c.Value.(type) {
	case float64:
		return uint(v), nil
	case uint:
		return v, nil
	case int:
		return uint(v), nil
	case string:
		id, err := strconv.ParseUint(v, 10, 32)
		return uint(id), err
	default:
		return 0, fmt.Errorf("invalid ID value type: %T", v)
	}
}

// GetCreatedAtValue trả về giá trị created_at từ cursor
func (c *Cursor) GetCreatedAtValue() (time.Time, error) {
	if c == nil || c.Type != CursorTypeCreatedAt {
		return time.Time{}, fmt.Errorf("cursor is not created_at type")
	}

	switch v := c.Value.(type) {
	case float64:
		return time.Unix(int64(v), 0), nil
	case int64:
		return time.Unix(v, 0), nil
	case string:
		timestamp, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			return time.Time{}, err
		}
		return time.Unix(timestamp, 0), nil
	default:
		return time.Time{}, fmt.Errorf("invalid created_at value type: %T", v)
	}
}

// GetStringValue trả về giá trị string từ cursor
func (c *Cursor) GetStringValue() (string, error) {
	if c == nil || (c.Type != CursorTypeName && c.Type != CursorTypeCode) {
		return "", fmt.Errorf("cursor is not string type")
	}

	switch v := c.Value.(type) {
	case string:
		return v, nil
	default:
		return "", fmt.Errorf("invalid string value type: %T", v)
	}
}

// IsNext kiểm tra cursor có phải direction "next"
func (c *Cursor) IsNext() bool {
	return c != nil && c.Direction == "next"
}

// IsPrev kiểm tra cursor có phải direction "prev"
func (c *Cursor) IsPrev() bool {
	return c != nil && c.Direction == "prev"
}

// Request đại diện cho pagination request với cursor parsing
type Request struct {
	CursorToken string `form:"cursor" binding:"omitempty" json:"cursor,omitempty"`
	Limit       int    `form:"limit" binding:"omitempty,min=1,max=100" json:"limit,omitempty"`

	// Parsed cursor (không bind từ form)
	Cursor *Cursor `json:"-"`
}

// ParseCursor giải mã cursor từ token
func (r *Request) ParseCursor() error {
	if r.CursorToken == "" {
		r.Cursor = nil
		return nil
	}

	cursor, err := DecodeCursor(r.CursorToken)
	if err != nil {
		return err
	}

	r.Cursor = cursor
	return nil
}

// SetDefaults thiết lập giá trị mặc định
func (r *Request) SetDefaults() {
	if r.Limit <= 0 {
		r.Limit = DefaultLimit
	}
	if r.Limit > MaxLimit {
		r.Limit = MaxLimit
	}
}
