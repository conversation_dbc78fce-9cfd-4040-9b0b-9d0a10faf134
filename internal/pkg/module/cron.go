package module

import (
	"context"

	"wnapi/internal/pkg/queue/cron"

	"wnapi/internal/pkg/logger"
)

// CronModule interface cho các module hỗ trợ cron functionality
type CronModule interface {
	// RegisterCronHandlers đăng ký các cron handlers của module
	RegisterCronHandlers(registry *cron.HandlerRegistry) error

	// GetCronJobs trả về danh sách cron jobs của module
	GetCronJobs() ([]*cron.CronJob, error)

	// OnCronEnabled được gọi khi cron system được bật
	OnCronEnabled(ctx context.Context) error

	// OnCronDisabled được gọi khi cron system được tắt
	OnCronDisabled(ctx context.Context) error
}

// CronModuleBase cung cấp implementation mặc định cho CronModule
type CronModuleBase struct{}

func (m *CronModuleBase) RegisterCronHandlers(registry *cron.HandlerRegistry) error {
	// Default implementation - no handlers to register
	return nil
}

func (m *CronModuleBase) GetCronJobs() ([]*cron.CronJob, error) {
	// Default implementation - no jobs
	return []*cron.CronJob{}, nil
}

func (m *CronModuleBase) OnCronEnabled(ctx context.Context) error {
	// Default implementation - do nothing
	return nil
}

func (m *CronModuleBase) OnCronDisabled(ctx context.Context) error {
	// Default implementation - do nothing
	return nil
}

// CronModuleManager quản lý cron functionality cho tất cả modules
type CronModuleManager struct {
	modules  map[string]CronModule
	registry *cron.HandlerRegistry
	logger   logger.Logger
}

// NewCronModuleManager tạo CronModuleManager mới
func NewCronModuleManager(registry *cron.HandlerRegistry, logger logger.Logger) *CronModuleManager {
	return &CronModuleManager{
		modules:  make(map[string]CronModule),
		registry: registry,
		logger:   logger,
	}
}

// RegisterModule đăng ký một module với cron functionality
func (m *CronModuleManager) RegisterModule(name string, module CronModule) error {
	m.modules[name] = module

	// Register cron handlers
	if err := module.RegisterCronHandlers(m.registry); err != nil {
		return err
	}

	return nil
}

// GetModule trả về module theo tên
func (m *CronModuleManager) GetModule(name string) (CronModule, bool) {
	module, exists := m.modules[name]
	return module, exists
}

// GetAllModules trả về tất cả modules
func (m *CronModuleManager) GetAllModules() map[string]CronModule {
	result := make(map[string]CronModule)
	for name, module := range m.modules {
		result[name] = module
	}
	return result
}

// GetAllCronJobs trả về tất cả cron jobs từ tất cả modules
func (m *CronModuleManager) GetAllCronJobs() ([]*cron.CronJob, error) {
	var allJobs []*cron.CronJob

	for _, module := range m.modules {
		jobs, err := module.GetCronJobs()
		if err != nil {
			return nil, err
		}
		allJobs = append(allJobs, jobs...)
	}

	return allJobs, nil
}

// OnCronEnabled thông báo tất cả modules khi cron được bật
func (m *CronModuleManager) OnCronEnabled(ctx context.Context) error {
	for name, module := range m.modules {
		if err := module.OnCronEnabled(ctx); err != nil {
			return err
		}
		// Log success for each module
		m.logger.Info("Module cron enabled", "module", name)
	}
	return nil
}

// OnCronDisabled thông báo tất cả modules khi cron được tắt
func (m *CronModuleManager) OnCronDisabled(ctx context.Context) error {
	for name, module := range m.modules {
		if err := module.OnCronDisabled(ctx); err != nil {
			return err
		}
		// Log success for each module
		m.logger.Info("Module cron disabled", "module", name)
	}
	return nil
}
