// internal/pkg/config/viperconfig/viper.go
package viperconfig

import (
	"fmt"
	"strings"
	"time"
	"wnapi/internal/pkg/config" // Import interface Config

	"github.com/spf13/viper"
)

// ConfigLoader là trình nạp cấu hình sử dụng viper
type ConfigLoader struct {
}

// NewConfigLoader tạo một instance mới của ConfigLoader
func NewConfigLoader() *ConfigLoader {
	return &ConfigLoader{}
}

// Load nạp cấu hình từ file và biến môi trường.
// Đây là hàm được gọi duy nhất từ lớp ứng dụng chính (ví dụ: cmd/server/main.go).
func (l *ConfigLoader) Load(path string) (config.Config, error) {
	v := viper.New()

	// 1. Đặt TẤT CẢ các giá trị mặc định cho toàn bộ ứng dụng trước tiên.
	// Thứ tự này đảm bảo rằng các giá trị mặc định luôn là tầng thấp nhất,
	// và sẽ bị ghi đè bởi file cấu hình hoặc biến môi trường.
	setGlobalDefaultConfig(v)

	// 2. Cấu hình để tự động đọc biến môi trường.
	// Viper sẽ tự động chuyển đổi biến môi trường dạng UPPER_CASE_UNDERSCORE (ví dụ: DB_HOST)
	// thành key nội bộ của Viper (db.host).
	v.AutomaticEnv()
	// Nếu bạn muốn chỉ đọc các biến có tiền tố cụ thể ở cấp ứng dụng, bạn có thể dùng v.SetEnvPrefix("APP").
	// Tuy nhiên, với việc các module có tiền tố riêng (TENANT_*, AUTH_*),
	// việc SetEnvPrefix ở đây có thể gây nhầm lẫn hoặc cần thêm BindEnv phức tạp.
	// Giữ AutomaticEnv() đơn giản là đủ để đọc mọi biến.

	configFileProvided := false
	if path != "" {
		// Xử lý path bắt đầu bằng '@' (dùng cho .env file)
		if strings.HasPrefix(path, "@") {
			path = strings.TrimPrefix(path, "@")
			v.SetConfigFile(path) // Đặt đường dẫn file cấu hình cụ thể
			configFileProvided = true
		} else {
			// Cấu hình các đường dẫn và tên file mặc định để tìm kiếm (ví dụ: config.yaml, config.json)
			v.AddConfigPath(".")
			v.AddConfigPath("./config") // Đường dẫn tương đối cho các thư mục config phổ biến
			v.AddConfigPath("../config")
			v.AddConfigPath("../../config")
			v.SetConfigName("config") // Tên file không có phần mở rộng (Viper sẽ tìm config.yaml, config.json, v.v.)
			v.SetConfigType("yaml")   // Kiểu mặc định nếu tên file không có phần mở rộng
		}
	} else {
		// Nếu không có path được cung cấp, cố gắng tìm file mặc định trong các đường dẫn đã cấu hình
		v.AddConfigPath(".")
		v.AddConfigPath("./config")
		v.AddConfigPath("../config")
		v.AddConfigPath("../../config")
		v.SetConfigName("config")
		v.SetConfigType("yaml")
	}

	// 3. Đọc cấu hình từ file (nếu tồn tại và tìm thấy).
	// ReadInConfig sẽ ghi đè lên các giá trị mặc định đã đặt.
	if configFileProvided {
		if err := v.ReadInConfig(); err != nil {
			// Chỉ là cảnh báo nếu file cấu hình không tìm thấy.
			// Ứng dụng sẽ tiếp tục với biến môi trường và giá trị mặc định.
			if _, ok := err.(viper.ConfigFileNotFoundError); ok {
				fmt.Printf("Warning: File cấu hình '%s' không tìm thấy. Sử dụng biến môi trường và giá trị mặc định.\n", path)
			} else {
				// Đây là lỗi nghiêm trọng hơn khi file tồn tại nhưng không thể đọc/parse.
				return nil, fmt.Errorf("không thể đọc file cấu hình '%s': %w", path, err)
			}
		} else {
			fmt.Printf("Config đã nạp từ: %s\n", v.ConfigFileUsed())
		}
	} else {
		// Try to read config file if found
		if err := v.ReadInConfig(); err != nil {
			if _, ok := err.(viper.ConfigFileNotFoundError); ok {
				fmt.Println("Không có file cấu hình nào được tìm thấy hoặc chỉ định. Chỉ sử dụng biến môi trường và giá trị mặc định.")
			} else {
				return nil, fmt.Errorf("không thể đọc file cấu hình: %w", err)
			}
		} else {
			fmt.Printf("Config đã nạp từ: %s\n", v.ConfigFileUsed())
		}
	}

	// 4. Trả về đối tượng ViperConfig, nó implement interface config.Config
	return &ViperConfig{
		viper: v,
	}, nil
}

// setGlobalDefaultConfig định nghĩa TẤT CẢ các giá trị cấu hình mặc định cho toàn bộ ứng dụng.
// Đây là nơi duy nhất bạn nên định nghĩa giá trị mặc định cho bất kỳ cấu hình nào.
func setGlobalDefaultConfig(v *viper.Viper) {
	// Cấu hình Server
	v.SetDefault("SERVER_ADDR", ":8080")
	v.SetDefault("SERVER_SHUTDOWN_TIMEOUT", 10*time.Second)
	v.SetDefault("GIN_MODE", "debug")
	v.SetDefault("WEB_URL", "http://localhost:9200")
	v.SetDefault("APP_ENV", "development")

	// Cấu hình Database
	v.SetDefault("DB_TYPE", "mysql")
	v.SetDefault("DB_HOST", "localhost")
	v.SetDefault("DB_PORT", 3307)
	v.SetDefault("DB_USERNAME", "root")
	v.SetDefault("DB_PASSWORD", "") // Để trống cho dev
	v.SetDefault("DB_DATABASE", "wnapi")
	v.SetDefault("DB_MAX_OPEN_CONNS", 25)
	v.SetDefault("DB_MAX_IDLE_CONNS", 5)
	v.SetDefault("DB_CONN_MAX_LIFETIME", 5*time.Minute)
	v.SetDefault("DB_MIGRATION_PATH", "./migrations")

	// Cấu hình Logger
	v.SetDefault("LOG_LEVEL", "info")        // debug, info, warn, error, fatal
	v.SetDefault("LOGGER_FORMAT", "console") // console, json
	v.SetDefault("LOGGER_FILE", "")          // Đường dẫn file log

	// Cấu hình RBAC
	v.SetDefault("RBAC_CACHE_PERMISSION_TTL", 5*time.Minute)

	// Cấu hình Queue
	v.SetDefault("QUEUE_ENABLED", true) // Mặc định bật queue để email handler có thể hoạt động
	v.SetDefault("QUEUE_BACKEND", "asynq")
	v.SetDefault("QUEUE_REDIS_ADDR", "localhost:6379")
	v.SetDefault("QUEUE_REDIS_PASSWORD", "")
	v.SetDefault("QUEUE_REDIS_DB", 0)
	v.SetDefault("QUEUE_REDIS_KEY_PREFIX", "wnapi:queue:")
	v.SetDefault("QUEUE_WORKER_CONCURRENCY", 10)
	v.SetDefault("QUEUE_WORKER_SHUTDOWN_TIMEOUT", 30*time.Second)
	v.SetDefault("QUEUE_DEFAULT_QUEUE_NAME", "default")
	v.SetDefault("QUEUE_DEFAULT_QUEUE_PRIORITY", 10)
	v.SetDefault("QUEUE_DEFAULT_QUEUE_MAX_RETRY", 3)
	v.SetDefault("QUEUE_SCHEDULER_ENABLED", true)
	v.SetDefault("QUEUE_SCHEDULER_CHECK_INTERVAL", 1*time.Minute)
	v.SetDefault("QUEUE_SCHEDULER_TIME_ZONE", "Asia/Ho_Chi_Minh")
	v.SetDefault("QUEUE_START_WORKER", true) // Mặc định tự động khởi động worker nếu queue enabled

	// Cấu hình Cron Jobs
	v.SetDefault("CRON_ENABLED", false) // Mặc định tắt cron jobs
	v.SetDefault("CRON_TIME_ZONE", "Asia/Ho_Chi_Minh")

	// System cron jobs
	v.SetDefault("CRON_SYSTEM_CLEANUP_ENABLED", true)
	v.SetDefault("CRON_SYSTEM_CLEANUP_SCHEDULE", "0 2 * * *") // 2 AM daily
	v.SetDefault("CRON_SYSTEM_HEALTH_CHECK_ENABLED", true)
	v.SetDefault("CRON_SYSTEM_HEALTH_CHECK_SCHEDULE", "*/15 * * * *") // Every 15 minutes
	v.SetDefault("CRON_SYSTEM_BACKUP_ENABLED", false)
	v.SetDefault("CRON_SYSTEM_BACKUP_SCHEDULE", "0 3 * * 0") // 3 AM every Sunday
	v.SetDefault("CRON_SYSTEM_DEMO_ENABLED", true)
	v.SetDefault("CRON_SYSTEM_DEMO_SCHEDULE", "* * * * *") // Every minute

	// Auth module cron jobs
	v.SetDefault("CRON_AUTH_SESSION_CLEANUP_ENABLED", true)
	v.SetDefault("CRON_AUTH_SESSION_CLEANUP_SCHEDULE", "0 2 * * *") // 2 AM daily
	v.SetDefault("CRON_AUTH_SESSION_CLEANUP_MAX_AGE", "24h")
	v.SetDefault("CRON_AUTH_PASSWORD_EXPIRY_ENABLED", true)
	v.SetDefault("CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE", "0 9 * * *") // 9 AM daily
	v.SetDefault("CRON_AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS", "7,3,1")
	v.SetDefault("CRON_AUTH_PASSWORD_EXPIRY_TEMPLATE_ID", "password_expiry")

	// Media module cron jobs
	v.SetDefault("CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED", true)
	v.SetDefault("CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE", "0 3 * * *") // 3 AM daily
	v.SetDefault("CRON_MEDIA_IMAGE_OPTIMIZATION_QUALITY", 85)
	v.SetDefault("CRON_MEDIA_IMAGE_OPTIMIZATION_BATCH_SIZE", 100)
	v.SetDefault("CRON_MEDIA_TEMP_CLEANUP_ENABLED", true)
	v.SetDefault("CRON_MEDIA_TEMP_CLEANUP_SCHEDULE", "0 1 * * *") // 1 AM daily
	v.SetDefault("CRON_MEDIA_TEMP_CLEANUP_MAX_AGE", "24h")

	// Cấu hình CLI Mode
	v.SetDefault("CLI_MODE", false) // Mặc định không phải CLI mode

	// Cấu hình Event System
	v.SetDefault("EVENT_ENABLED", true) // Mặc định bật Event System
	v.SetDefault("EVENT_REDIS_HOST", "localhost")
	v.SetDefault("EVENT_REDIS_PORT", 6379)
	v.SetDefault("EVENT_REDIS_DB", 0)
	v.SetDefault("EVENT_REDIS_PASSWORD", "")
	v.SetDefault("EVENT_PUBLISHER_MAX_LEN", 10000)
	v.SetDefault("EVENT_DEBUG", false)
	v.SetDefault("EVENT_CONSUMER_GROUP", "wnapi_consumers")
	v.SetDefault("EVENT_CONSUMER_ID", "wnapi_consumer_1")
	v.SetDefault("EVENT_ROUTER_CLOSE_TIMEOUT", 30*time.Second)
	v.SetDefault("EVENT_ROUTER_MIDDLEWARE_TIMEOUT", 30*time.Second)
	v.SetDefault("EVENT_RETRY_MAX_ATTEMPTS", 3)
	v.SetDefault("EVENT_RETRY_INITIAL_INTERVAL", 1*time.Second)
	v.SetDefault("EVENT_RETRY_MAX_INTERVAL", 30*time.Second)
	v.SetDefault("EVENT_LOGGING_ENABLED", true)
	v.SetDefault("EVENT_METRICS_ENABLED", true)

	// Cấu hình JWT (Auth module)
	// Các key này có thể được override bởi biến môi trường hoặc config file của ứng dụng.
	v.SetDefault("JWT_ACCESS_SIGNING_KEY", "super_secret_access_key_change_me_in_production")
	v.SetDefault("JWT_REFRESH_SIGNING_KEY", "super_secret_refresh_key_change_me_in_production")
	v.SetDefault("JWT_ACCESS_TOKEN_EXPIRATION", 15*time.Minute) // 15m
	v.SetDefault("JWT_REFRESH_TOKEN_EXPIRATION", 168*time.Hour) // 168h (7 days)
	v.SetDefault("JWT_ISSUER", "wnapi")

	// Cấu hình Auth module
	v.SetDefault("AUTH_MESSAGE", "Xin chào từ module Auth!")
	v.SetDefault("AUTH_MAX_SESSIONS_PER_USER", 10)

	// Cấu hình Modules (sử dụng tiền tố module và tên key)
	v.SetDefault("MODULES_ENABLED", "hello,auth,rbac,notification,product,tenant") // Mặc định các module được enable

	// Cấu hình cụ thể cho từng module (tiền tố MODULE_NAME_KEY_NAME)
	// Các module sẽ đọc chúng bằng `appConfig.GetString("TENANT_MAX_USERS_PER_TENANT")`
	v.SetDefault("TENANT_MAX_USERS_PER_TENANT", 100)
	v.SetDefault("TENANT_MAX_TENANTS_PER_ACCOUNT", 5)
	v.SetDefault("TENANT_DEFAULT_PLAN_ID", 1)
	v.SetDefault("TENANT_ENABLE_MULTI_TENANCY", true)
	v.SetDefault("TENANT_DEFAULT_PAGE_SIZE", 10)
	v.SetDefault("TENANT_MAX_PAGE_SIZE", 100)
	v.SetDefault("TENANT_DEFAULT_SORT_FIELD", "created_at")
	v.SetDefault("TENANT_DEFAULT_SORT_ORDER", "desc")
	v.SetDefault("TENANT_DEFAULT_TENANT_TIMEZONE", "Asia/Ho_Chi_Minh")
	v.SetDefault("TENANT_MESSAGE", "Xin chào từ module Tenant!")

	// JWT Configuration - Using global JWT settings instead of auth-specific ones
	v.SetDefault("JWT_ACCESS_SIGNING_KEY", "super_secret_access_key_change_me_in_production")
	v.SetDefault("JWT_REFRESH_SIGNING_KEY", "super_secret_refresh_key_change_me_in_production")
	v.SetDefault("JWT_ACCESS_TOKEN_EXPIRATION", 15*time.Minute)
	v.SetDefault("JWT_REFRESH_TOKEN_EXPIRATION", 168*time.Hour)
	v.SetDefault("JWT_ISSUER", "wnapi")

	v.SetDefault("NOTIFICATION_EMAIL_HOST", "localhost")
	v.SetDefault("NOTIFICATION_EMAIL_PORT", 1025) // Default for Mailhog/Mailcatcher
	v.SetDefault("NOTIFICATION_EMAIL_USERNAME", "")
	v.SetDefault("NOTIFICATION_EMAIL_PASSWORD", "")
	v.SetDefault("NOTIFICATION_EMAIL_FROM", "<EMAIL>")
	v.SetDefault("NOTIFICATION_SMS_PROVIDER", "twilio")
	v.SetDefault("NOTIFICATION_SMS_ENABLED", false)
	v.SetDefault("NOTIFICATION_PUSH_ENABLED", false)
	v.SetDefault("NOTIFICATION_CACHE_ENABLED", false)
	v.SetDefault("NOTIFICATION_CACHE_TTL", 300)
	v.SetDefault("NOTIFICATION_RETRY_ATTEMPTS", 3)
	v.SetDefault("NOTIFICATION_RETRY_DELAY", 5*time.Second)
	v.SetDefault("NOTIFICATION_WORKER_ENABLED", true)
	v.SetDefault("NOTIFICATION_MESSAGE", "Xin chào từ module Notification!")

	// Media Module defaults
	v.SetDefault("MEDIA_STORAGE_TYPE", "local")
	v.SetDefault("MEDIA_MAX_FILE_SIZE", 10*1024*1024) // 10MB
	v.SetDefault("MEDIA_IMAGE_QUALITY", 85)
	v.SetDefault("STORAGE_LOCAL_PATH", "./uploads")
	v.SetDefault("STORAGE_LOCAL_URL", "http://localhost:8080/uploads")
	v.SetDefault("MINIO_ENDPOINT", "localhost:9000")
	v.SetDefault("MINIO_ACCESS_KEY", "minioadmin")
	v.SetDefault("MINIO_SECRET_KEY", "minioadmin")
	v.SetDefault("MINIO_BUCKET", "media")
	v.SetDefault("MINIO_USE_SSL", false)
	v.SetDefault("MINIO_REGION", "us-east-1")
	v.SetDefault("CDN_DOMAIN", "")

	// Tracing defaults
	v.SetDefault("TRACING_ENABLED", false)
	v.SetDefault("TRACING_SERVICE_NAME", "wnapi")
	v.SetDefault("TRACING_EXPORTER_TYPE", "signoz")
	v.SetDefault("TRACING_SAMPLE_RATIO", 1.0)
	v.SetDefault("SIGNOZ_ENDPOINT", "")
	v.SetDefault("JAEGER_AGENT_HOST", "")
	v.SetDefault("JAEGER_AGENT_PORT", "")

	// Seed defaults
	v.SetDefault("SEED_ENVIRONMENT", "dev")
	v.SetDefault("SEED_DATA_PATH", ".")
	v.SetDefault("SEED_BATCH_SIZE", 50)
	v.SetDefault("SEED_SKIP_EXISTS", true)
	v.SetDefault("SEED_DRY_RUN", false)
	v.SetDefault("SEED_VERBOSE", false)
	v.SetDefault("SEED_TENANT_ID", 1)
	v.SetDefault("SEED_USER_ID", 1)

	v.SetDefault("PRODUCT_DEFAULT_PAGE_SIZE", 10)
	v.SetDefault("PRODUCT_MAX_PAGE_SIZE", 100)
	v.SetDefault("PRODUCT_MAX_TITLE_LENGTH", 200)
	v.SetDefault("PRODUCT_MAX_CONTENT_LENGTH", 50000)
	v.SetDefault("PRODUCT_DEFAULT_SORT_FIELD", "created_at")
	v.SetDefault("PRODUCT_DEFAULT_SORT_ORDER", "desc")
	v.SetDefault("PRODUCT_ALLOWED_SORT_FIELDS", "id,name,base_price,created_at,updated_at")

	v.SetDefault("SEO_MESSAGE", "Xin chào từ module SEO!")
	v.SetDefault("SEO_DEBUG", false)

	v.SetDefault("BLOG_MAX_TITLE_LENGTH", 200)
	v.SetDefault("BLOG_MAX_CONTENT_LENGTH", 50000)
	v.SetDefault("BLOG_DEFAULT_PAGE_SIZE", 10)
	v.SetDefault("BLOG_MAX_PAGE_SIZE", 100)
	v.SetDefault("BLOG_DEFAULT_SORT_FIELD", "created_at")
	v.SetDefault("BLOG_DEFAULT_SORT_ORDER", "desc")
	v.SetDefault("BLOG_ALLOWED_SORT_FIELDS", "id,title,created_at,updated_at")
	v.SetDefault("BLOG_MESSAGE", "Xin chào từ module Blog!")

	v.SetDefault("WEBSITE_SITE_NAME", "My Website")
	v.SetDefault("WEBSITE_SITE_DESCRIPTION", "Website description")
	v.SetDefault("WEBSITE_CONTACT_EMAIL", "<EMAIL>")
	v.SetDefault("WEBSITE_MAX_MENU_ITEMS", 10)
	v.SetDefault("WEBSITE_MAX_BANNERS", 5)
	v.SetDefault("WEBSITE_ENABLE_CONTACT_FORM", true)
	v.SetDefault("WEBSITE_CACHE_TIMEOUT_MINUTES", 10)
	v.SetDefault("WEBSITE_MAX_PAGE_SIZE", 100)
	v.SetDefault("WEBSITE_DEFAULT_PAGE_SIZE", 10)
	v.SetDefault("WEBSITE_MESSAGE", "Xin chào từ module Website!")
	v.SetDefault("WEBSITE_TRACING_ENABLED", false)
	v.SetDefault("WEBSITE_TRACING_JAEGER_HOST", "localhost")
	v.SetDefault("WEBSITE_TRACING_JAEGER_PORT", 14268)
	v.SetDefault("WEBSITE_TRACING_SIGNOZ_ENDPOINT", "http://localhost:4317")
	// ... và các cấu hình mặc định khác cho toàn bộ ứng dụng
}

// ViperConfig là một triển khai của Config sử dụng viper
type ViperConfig struct {
	viper *viper.Viper
}

// Triển khai các phương thức của interface Config.
// Các phương thức này trực tiếp gọi các phương thức của Viper.
// Viper sẽ tự động xử lý thứ tự ưu tiên: biến môi trường > file cấu hình > giá trị mặc định (SetDefault).

func (c *ViperConfig) GetString(key string) string {
	return c.viper.GetString(key)
}

func (c *ViperConfig) GetStringWithDefault(key string, defaultValue string) string {
	// Viper.GetString sẽ trả về rỗng nếu key không tồn tại.
	// `IsSet` kiểm tra xem key có được thiết lập từ bất kỳ nguồn nào (env, file, SetDefault).
	// Nếu key không được set, chúng ta trả về defaultValue.
	if !c.viper.IsSet(key) {
		return defaultValue
	}
	return c.viper.GetString(key)
}

func (c *ViperConfig) GetInt(key string) int {
	return c.viper.GetInt(key)
}

func (c *ViperConfig) GetIntWithDefault(key string, defaultValue int) int {
	if !c.viper.IsSet(key) {
		return defaultValue
	}
	return c.viper.GetInt(key)
}

func (c *ViperConfig) GetBool(key string) bool {
	return c.viper.GetBool(key)
}

func (c *ViperConfig) GetBoolWithDefault(key string, defaultValue bool) bool {
	if !c.viper.IsSet(key) {
		return defaultValue
	}
	return c.viper.GetBool(key)
}

func (c *ViperConfig) GetDuration(key string) time.Duration {
	return c.viper.GetDuration(key)
}

func (c *ViperConfig) GetDurationWithDefault(key string, defaultValue time.Duration) time.Duration {
	if !c.viper.IsSet(key) {
		return defaultValue
	}
	return c.viper.GetDuration(key)
}

func (c *ViperConfig) GetFloat64(key string) float64 {
	return c.viper.GetFloat64(key)
}

func (c *ViperConfig) GetFloat64WithDefault(key string, defaultValue float64) float64 {
	if !c.viper.IsSet(key) {
		return defaultValue
	}
	return c.viper.GetFloat64(key)
}

// GetStringSlice lấy danh sách chuỗi từ cấu hình.
// Hàm này xử lý cả trường hợp slice thực sự và string cần phân tách bằng dấu phẩy.
func (c *ViperConfig) GetStringSlice(key string) []string {
	// Thử lấy slice trực tiếp từ Viper trước
	slice := c.viper.GetStringSlice(key)

	// Nếu có slice và có nhiều hơn 1 phần tử, hoặc phần tử đầu tiên không chứa dấu phẩy
	// thì đây là slice thực sự từ config file (YAML/JSON)
	if len(slice) > 1 || (len(slice) == 1 && !strings.Contains(slice[0], ",")) {
		return slice
	}

	// Nếu có 1 phần tử và chứa dấu phẩy, hoặc slice rỗng,
	// thì thử lấy giá trị string và phân tách
	value := c.viper.GetString(key)
	if value == "" {
		return []string{}
	}

	// Phân tách chuỗi bằng dấu phẩy
	parts := strings.Split(value, ",")
	var result []string
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part != "" {
			result = append(result, part)
		}
	}

	return result
}

// GetStringMap lấy map cấu hình từ tiền tố.
// Ví dụ: GetStringMap("database.mysql") sẽ trả về một map với các key như "host", "port"
// nếu cấu hình có dạng:
// database:
//
//	mysql:
//	  host: ...
//	  port: ...
func (c *ViperConfig) GetStringMap(prefix string) map[string]interface{} {
	// Viper hỗ trợ GetStringMap cho các cấu trúc nested trong file config.
	// Đối với biến môi trường có tiền tố (ví dụ: `MODULE_NAME_KEY`),
	// Viper sẽ tự động ánh xạ chúng khi `v.AutomaticEnv()` được gọi.
	// `GetStringMap` có thể lấy các giá trị này nếu prefix phù hợp.
	return c.viper.GetStringMap(prefix)
}

// GetModuleSettings lấy cấu hình cho một module cụ thể.
// Ví dụ: GetModuleSettings("tenant") sẽ trả về một map chứa các cấu hình
// như "max_users_per_tenant", "max_tenants_per_account", v.v.,
// dựa trên cách chúng được định nghĩa (ví dụ: `TENANT_MAX_USERS_PER_TENANT` trong env
// hoặc `tenant.max_users_per_tenant` trong file).
func (c *ViperConfig) GetModuleSettings(moduleName string) map[string]interface{} {
	moduleKey := strings.ToLower(moduleName) // Chuyển tên module sang lowercase để match Viper's internal key format
	return c.viper.GetStringMap(moduleKey)
}

// GetQueueConfig lấy cấu hình queue từ các biến môi trường và file config
func (c *ViperConfig) GetQueueConfig() config.QueueConfig {
	return config.QueueConfig{
		Enabled:     c.viper.GetBool("QUEUE_ENABLED"),
		Backend:     c.viper.GetString("QUEUE_BACKEND"),
		StartWorker: c.viper.GetBool("QUEUE_START_WORKER"),
		Redis: struct {
			Addr     string `env:"QUEUE_REDIS_ADDR" envDefault:"localhost:6379"`
			Password string `env:"QUEUE_REDIS_PASSWORD"`
			DB       int    `env:"QUEUE_REDIS_DB" envDefault:"0"`
			PoolSize int    `env:"QUEUE_REDIS_POOL_SIZE" envDefault:"10"`
		}{
			Addr:     c.viper.GetString("QUEUE_REDIS_ADDR"),
			Password: c.viper.GetString("QUEUE_REDIS_PASSWORD"),
			DB:       c.viper.GetInt("QUEUE_REDIS_DB"),
			PoolSize: c.viper.GetInt("QUEUE_REDIS_POOL_SIZE"),
		},
		Scheduler: struct {
			Enabled  bool   `env:"QUEUE_SCHEDULER_ENABLED" envDefault:"false"`
			TimeZone string `env:"QUEUE_SCHEDULER_TIME_ZONE" envDefault:"UTC"`
		}{
			Enabled:  c.viper.GetBool("QUEUE_SCHEDULER_ENABLED"),
			TimeZone: c.viper.GetString("QUEUE_SCHEDULER_TIME_ZONE"),
		},
		Worker: struct {
			Concurrency    int      `env:"QUEUE_WORKER_CONCURRENCY" envDefault:"10"`
			Queues         []string `env:"QUEUE_WORKER_QUEUES" envSeparator:","`
			StrictPriority bool     `env:"QUEUE_WORKER_STRICT_PRIORITY" envDefault:"false"`
		}{
			Concurrency:    c.viper.GetInt("QUEUE_WORKER_CONCURRENCY"),
			Queues:         c.viper.GetStringSlice("QUEUE_WORKER_QUEUES"),
			StrictPriority: c.viper.GetBool("QUEUE_WORKER_STRICT_PRIORITY"),
		},
	}
}

// GetCronConfig lấy cấu hình cron từ các biến môi trường và file config
func (c *ViperConfig) GetCronConfig() config.CronConfig {
	return config.CronConfig{
		Enabled:  c.viper.GetBool("CRON_ENABLED"),
		TimeZone: c.viper.GetString("CRON_TIME_ZONE"),
		SystemJobs: struct {
			Cleanup struct {
				Enabled  bool   `env:"CRON_SYSTEM_CLEANUP_ENABLED" envDefault:"true"`
				Schedule string `env:"CRON_SYSTEM_CLEANUP_SCHEDULE" envDefault:"0 2 * * *"`
			}
			HealthCheck struct {
				Enabled  bool   `env:"CRON_SYSTEM_HEALTH_CHECK_ENABLED" envDefault:"true"`
				Schedule string `env:"CRON_SYSTEM_HEALTH_CHECK_SCHEDULE" envDefault:"*/15 * * * *"`
			}
			Backup struct {
				Enabled  bool   `env:"CRON_SYSTEM_BACKUP_ENABLED" envDefault:"false"`
				Schedule string `env:"CRON_SYSTEM_BACKUP_SCHEDULE" envDefault:"0 3 * * 0"`
			}
			Demo struct {
				Enabled  bool   `env:"CRON_SYSTEM_DEMO_ENABLED" envDefault:"true"`
				Schedule string `env:"CRON_SYSTEM_DEMO_SCHEDULE" envDefault:"* * * * *"`
			}
		}{
			Cleanup: struct {
				Enabled  bool   `env:"CRON_SYSTEM_CLEANUP_ENABLED" envDefault:"true"`
				Schedule string `env:"CRON_SYSTEM_CLEANUP_SCHEDULE" envDefault:"0 2 * * *"`
			}{
				Enabled:  c.viper.GetBool("CRON_SYSTEM_CLEANUP_ENABLED"),
				Schedule: c.viper.GetString("CRON_SYSTEM_CLEANUP_SCHEDULE"),
			},
			HealthCheck: struct {
				Enabled  bool   `env:"CRON_SYSTEM_HEALTH_CHECK_ENABLED" envDefault:"true"`
				Schedule string `env:"CRON_SYSTEM_HEALTH_CHECK_SCHEDULE" envDefault:"*/15 * * * *"`
			}{
				Enabled:  c.viper.GetBool("CRON_SYSTEM_HEALTH_CHECK_ENABLED"),
				Schedule: c.viper.GetString("CRON_SYSTEM_HEALTH_CHECK_SCHEDULE"),
			},
			Backup: struct {
				Enabled  bool   `env:"CRON_SYSTEM_BACKUP_ENABLED" envDefault:"false"`
				Schedule string `env:"CRON_SYSTEM_BACKUP_SCHEDULE" envDefault:"0 3 * * 0"`
			}{
				Enabled:  c.viper.GetBool("CRON_SYSTEM_BACKUP_ENABLED"),
				Schedule: c.viper.GetString("CRON_SYSTEM_BACKUP_SCHEDULE"),
			},
			Demo: struct {
				Enabled  bool   `env:"CRON_SYSTEM_DEMO_ENABLED" envDefault:"true"`
				Schedule string `env:"CRON_SYSTEM_DEMO_SCHEDULE" envDefault:"* * * * *"`
			}{
				Enabled:  c.viper.GetBool("CRON_SYSTEM_DEMO_ENABLED"),
				Schedule: c.viper.GetString("CRON_SYSTEM_DEMO_SCHEDULE"),
			},
		},
		ModuleJobs: struct {
			Auth struct {
				SessionCleanup struct {
					Enabled  bool   `env:"CRON_AUTH_SESSION_CLEANUP_ENABLED" envDefault:"true"`
					Schedule string `env:"CRON_AUTH_SESSION_CLEANUP_SCHEDULE" envDefault:"0 2 * * *"`
					MaxAge   string `env:"CRON_AUTH_SESSION_CLEANUP_MAX_AGE" envDefault:"24h"`
				}
				PasswordExpiry struct {
					Enabled    bool   `env:"CRON_AUTH_PASSWORD_EXPIRY_ENABLED" envDefault:"true"`
					Schedule   string `env:"CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE" envDefault:"0 9 * * *"`
					NotifyDays []int  `env:"CRON_AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS" envSeparator:","`
					TemplateID string `env:"CRON_AUTH_PASSWORD_EXPIRY_TEMPLATE_ID"`
				}
			}
			Media struct {
				ImageOptimization struct {
					Enabled   bool   `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED" envDefault:"true"`
					Schedule  string `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE" envDefault:"0 3 * * *"`
					Quality   int    `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_QUALITY" envDefault:"85"`
					BatchSize int    `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_BATCH_SIZE" envDefault:"100"`
				}
				TempCleanup struct {
					Enabled  bool   `env:"CRON_MEDIA_TEMP_CLEANUP_ENABLED" envDefault:"true"`
					Schedule string `env:"CRON_MEDIA_TEMP_CLEANUP_SCHEDULE" envDefault:"0 1 * * *"`
					MaxAge   string `env:"CRON_MEDIA_TEMP_CLEANUP_MAX_AGE" envDefault:"24h"`
				}
			}
			Blog struct {
				ProcessPendingSchedules struct {
					Enabled   bool   `env:"CRON_BLOG_PROCESS_PENDING_SCHEDULES_ENABLED" envDefault:"true"`
					Schedule  string `env:"CRON_BLOG_PROCESS_PENDING_SCHEDULES_SCHEDULE" envDefault:"*/5 * * * *"`
					BatchSize int    `env:"CRON_BLOG_PROCESS_PENDING_SCHEDULES_BATCH_SIZE" envDefault:"50"`
				}
				CleanupOldSchedules struct {
					Enabled       bool   `env:"CRON_BLOG_CLEANUP_OLD_SCHEDULES_ENABLED" envDefault:"true"`
					Schedule      string `env:"CRON_BLOG_CLEANUP_OLD_SCHEDULES_SCHEDULE" envDefault:"0 2 * * *"`
					RetentionDays int    `env:"CRON_BLOG_CLEANUP_OLD_SCHEDULES_RETENTION_DAYS" envDefault:"30"`
				}
				PublishScheduledPosts struct {
					Enabled   bool   `env:"CRON_BLOG_PUBLISH_SCHEDULED_POSTS_ENABLED" envDefault:"true"`
					Schedule  string `env:"CRON_BLOG_PUBLISH_SCHEDULED_POSTS_SCHEDULE" envDefault:"*/1 * * * *"`
					BatchSize int    `env:"CRON_BLOG_PUBLISH_SCHEDULED_POSTS_BATCH_SIZE" envDefault:"20"`
				}
			}
		}{
			Auth: struct {
				SessionCleanup struct {
					Enabled  bool   `env:"CRON_AUTH_SESSION_CLEANUP_ENABLED" envDefault:"true"`
					Schedule string `env:"CRON_AUTH_SESSION_CLEANUP_SCHEDULE" envDefault:"0 2 * * *"`
					MaxAge   string `env:"CRON_AUTH_SESSION_CLEANUP_MAX_AGE" envDefault:"24h"`
				}
				PasswordExpiry struct {
					Enabled    bool   `env:"CRON_AUTH_PASSWORD_EXPIRY_ENABLED" envDefault:"true"`
					Schedule   string `env:"CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE" envDefault:"0 9 * * *"`
					NotifyDays []int  `env:"CRON_AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS" envSeparator:","`
					TemplateID string `env:"CRON_AUTH_PASSWORD_EXPIRY_TEMPLATE_ID"`
				}
			}{
				SessionCleanup: struct {
					Enabled  bool   `env:"CRON_AUTH_SESSION_CLEANUP_ENABLED" envDefault:"true"`
					Schedule string `env:"CRON_AUTH_SESSION_CLEANUP_SCHEDULE" envDefault:"0 2 * * *"`
					MaxAge   string `env:"CRON_AUTH_SESSION_CLEANUP_MAX_AGE" envDefault:"24h"`
				}{
					Enabled:  c.viper.GetBool("CRON_AUTH_SESSION_CLEANUP_ENABLED"),
					Schedule: c.viper.GetString("CRON_AUTH_SESSION_CLEANUP_SCHEDULE"),
					MaxAge:   c.viper.GetString("CRON_AUTH_SESSION_CLEANUP_MAX_AGE"),
				},
				PasswordExpiry: struct {
					Enabled    bool   `env:"CRON_AUTH_PASSWORD_EXPIRY_ENABLED" envDefault:"true"`
					Schedule   string `env:"CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE" envDefault:"0 9 * * *"`
					NotifyDays []int  `env:"CRON_AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS" envSeparator:","`
					TemplateID string `env:"CRON_AUTH_PASSWORD_EXPIRY_TEMPLATE_ID"`
				}{
					Enabled:    c.viper.GetBool("CRON_AUTH_PASSWORD_EXPIRY_ENABLED"),
					Schedule:   c.viper.GetString("CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE"),
					NotifyDays: c.viper.GetIntSlice("CRON_AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS"),
					TemplateID: c.viper.GetString("CRON_AUTH_PASSWORD_EXPIRY_TEMPLATE_ID"),
				},
			},
			Media: struct {
				ImageOptimization struct {
					Enabled   bool   `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED" envDefault:"true"`
					Schedule  string `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE" envDefault:"0 3 * * *"`
					Quality   int    `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_QUALITY" envDefault:"85"`
					BatchSize int    `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_BATCH_SIZE" envDefault:"100"`
				}
				TempCleanup struct {
					Enabled  bool   `env:"CRON_MEDIA_TEMP_CLEANUP_ENABLED" envDefault:"true"`
					Schedule string `env:"CRON_MEDIA_TEMP_CLEANUP_SCHEDULE" envDefault:"0 1 * * *"`
					MaxAge   string `env:"CRON_MEDIA_TEMP_CLEANUP_MAX_AGE" envDefault:"24h"`
				}
			}{
				ImageOptimization: struct {
					Enabled   bool   `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED" envDefault:"true"`
					Schedule  string `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE" envDefault:"0 3 * * *"`
					Quality   int    `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_QUALITY" envDefault:"85"`
					BatchSize int    `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_BATCH_SIZE" envDefault:"100"`
				}{
					Enabled:   c.viper.GetBool("CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED"),
					Schedule:  c.viper.GetString("CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE"),
					Quality:   c.viper.GetInt("CRON_MEDIA_IMAGE_OPTIMIZATION_QUALITY"),
					BatchSize: c.viper.GetInt("CRON_MEDIA_IMAGE_OPTIMIZATION_BATCH_SIZE"),
				},
				TempCleanup: struct {
					Enabled  bool   `env:"CRON_MEDIA_TEMP_CLEANUP_ENABLED" envDefault:"true"`
					Schedule string `env:"CRON_MEDIA_TEMP_CLEANUP_SCHEDULE" envDefault:"0 1 * * *"`
					MaxAge   string `env:"CRON_MEDIA_TEMP_CLEANUP_MAX_AGE" envDefault:"24h"`
				}{
					Enabled:  c.viper.GetBool("CRON_MEDIA_TEMP_CLEANUP_ENABLED"),
					Schedule: c.viper.GetString("CRON_MEDIA_TEMP_CLEANUP_SCHEDULE"),
					MaxAge:   c.viper.GetString("CRON_MEDIA_TEMP_CLEANUP_MAX_AGE"),
				},
			},
			Blog: struct {
				ProcessPendingSchedules struct {
					Enabled   bool   `env:"CRON_BLOG_PROCESS_PENDING_SCHEDULES_ENABLED" envDefault:"true"`
					Schedule  string `env:"CRON_BLOG_PROCESS_PENDING_SCHEDULES_SCHEDULE" envDefault:"*/5 * * * *"`
					BatchSize int    `env:"CRON_BLOG_PROCESS_PENDING_SCHEDULES_BATCH_SIZE" envDefault:"50"`
				}
				CleanupOldSchedules struct {
					Enabled       bool   `env:"CRON_BLOG_CLEANUP_OLD_SCHEDULES_ENABLED" envDefault:"true"`
					Schedule      string `env:"CRON_BLOG_CLEANUP_OLD_SCHEDULES_SCHEDULE" envDefault:"0 2 * * *"`
					RetentionDays int    `env:"CRON_BLOG_CLEANUP_OLD_SCHEDULES_RETENTION_DAYS" envDefault:"30"`
				}
				PublishScheduledPosts struct {
					Enabled   bool   `env:"CRON_BLOG_PUBLISH_SCHEDULED_POSTS_ENABLED" envDefault:"true"`
					Schedule  string `env:"CRON_BLOG_PUBLISH_SCHEDULED_POSTS_SCHEDULE" envDefault:"*/1 * * * *"`
					BatchSize int    `env:"CRON_BLOG_PUBLISH_SCHEDULED_POSTS_BATCH_SIZE" envDefault:"20"`
				}
			}{
				ProcessPendingSchedules: struct {
					Enabled   bool   `env:"CRON_BLOG_PROCESS_PENDING_SCHEDULES_ENABLED" envDefault:"true"`
					Schedule  string `env:"CRON_BLOG_PROCESS_PENDING_SCHEDULES_SCHEDULE" envDefault:"*/5 * * * *"`
					BatchSize int    `env:"CRON_BLOG_PROCESS_PENDING_SCHEDULES_BATCH_SIZE" envDefault:"50"`
				}{
					Enabled:   c.viper.GetBool("CRON_BLOG_PROCESS_PENDING_SCHEDULES_ENABLED"),
					Schedule:  c.viper.GetString("CRON_BLOG_PROCESS_PENDING_SCHEDULES_SCHEDULE"),
					BatchSize: c.viper.GetInt("CRON_BLOG_PROCESS_PENDING_SCHEDULES_BATCH_SIZE"),
				},
				CleanupOldSchedules: struct {
					Enabled       bool   `env:"CRON_BLOG_CLEANUP_OLD_SCHEDULES_ENABLED" envDefault:"true"`
					Schedule      string `env:"CRON_BLOG_CLEANUP_OLD_SCHEDULES_SCHEDULE" envDefault:"0 2 * * *"`
					RetentionDays int    `env:"CRON_BLOG_CLEANUP_OLD_SCHEDULES_RETENTION_DAYS" envDefault:"30"`
				}{
					Enabled:       c.viper.GetBool("CRON_BLOG_CLEANUP_OLD_SCHEDULES_ENABLED"),
					Schedule:      c.viper.GetString("CRON_BLOG_CLEANUP_OLD_SCHEDULES_SCHEDULE"),
					RetentionDays: c.viper.GetInt("CRON_BLOG_CLEANUP_OLD_SCHEDULES_RETENTION_DAYS"),
				},
				PublishScheduledPosts: struct {
					Enabled   bool   `env:"CRON_BLOG_PUBLISH_SCHEDULED_POSTS_ENABLED" envDefault:"true"`
					Schedule  string `env:"CRON_BLOG_PUBLISH_SCHEDULED_POSTS_SCHEDULE" envDefault:"*/1 * * * *"`
					BatchSize int    `env:"CRON_BLOG_PUBLISH_SCHEDULED_POSTS_BATCH_SIZE" envDefault:"20"`
				}{
					Enabled:   c.viper.GetBool("CRON_BLOG_PUBLISH_SCHEDULED_POSTS_ENABLED"),
					Schedule:  c.viper.GetString("CRON_BLOG_PUBLISH_SCHEDULED_POSTS_SCHEDULE"),
					BatchSize: c.viper.GetInt("CRON_BLOG_PUBLISH_SCHEDULED_POSTS_BATCH_SIZE"),
				},
			},
		},
	}
}
