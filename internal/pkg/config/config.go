// internal/pkg/config/config.go
package config

import (
	"time"
)

// Config định nghĩa interface cho cấu hình ứng dụng
type Config interface {
	// GetString lấy giá trị chuỗi từ cấu hình
	GetString(key string) string

	// GetStringWithDefault lấy giá trị chuỗi từ cấu hình, nếu không có thì trả về giá trị mặc định
	GetStringWithDefault(key string, defaultValue string) string

	// GetInt lấy giá trị số nguyên từ cấu hình
	GetInt(key string) int

	// GetIntWithDefault lấy giá trị số nguyên từ cấu hình, nếu không có thì trả về giá trị mặc định
	GetIntWithDefault(key string, defaultValue int) int

	// GetBool lấy giá trị boolean từ cấu hình
	<PERSON>ool(key string) bool

	// GetBoolWithDefault lấy giá trị boolean từ cấu hình, nếu không có thì trả về giá trị mặc định
	GetBoolWithDefault(key string, defaultValue bool) bool

	// GetDuration lấy giá trị duration từ cấu hình
	GetDuration(key string) time.Duration

	// GetDurationWithDefault lấy giá trị duration từ cấu hình, nếu không có thì trả về giá trị mặc định
	GetDurationWithDefault(key string, defaultValue time.Duration) time.Duration

	// GetFloat64 lấy giá trị float64 từ cấu hình
	GetFloat64(key string) float64

	// GetFloat64WithDefault lấy giá trị float64 từ cấu hình, nếu không có thì trả về giá trị mặc định
	GetFloat64WithDefault(key string, defaultValue float64) float64

	// GetStringSlice lấy danh sách chuỗi từ cấu hình
	GetStringSlice(key string) []string

	// GetStringMap lấy map cấu hình từ tiền tố
	GetStringMap(prefix string) map[string]interface{}

	// GetModuleSettings lấy cấu hình cho module từ cấu hình
	GetModuleSettings(moduleName string) map[string]interface{}

	// GetQueueConfig lấy cấu hình queue
	GetQueueConfig() QueueConfig

	// GetCronConfig lấy cấu hình cron
	GetCronConfig() CronConfig
}

// QueueConfig represents queue system configuration
type QueueConfig struct {
	Enabled     bool   `env:"QUEUE_ENABLED" envDefault:"false"`
	Backend     string `env:"QUEUE_BACKEND" envDefault:"asynq"`
	StartWorker bool   `env:"QUEUE_START_WORKER" envDefault:"true"`

	Redis struct {
		Addr     string `env:"QUEUE_REDIS_ADDR" envDefault:"localhost:6379"`
		Password string `env:"QUEUE_REDIS_PASSWORD"`
		DB       int    `env:"QUEUE_REDIS_DB" envDefault:"0"`
		PoolSize int    `env:"QUEUE_REDIS_POOL_SIZE" envDefault:"10"`
	}

	Scheduler struct {
		Enabled  bool   `env:"QUEUE_SCHEDULER_ENABLED" envDefault:"false"`
		TimeZone string `env:"QUEUE_SCHEDULER_TIME_ZONE" envDefault:"UTC"`
	}

	Worker struct {
		Concurrency    int      `env:"QUEUE_WORKER_CONCURRENCY" envDefault:"10"`
		Queues         []string `env:"QUEUE_WORKER_QUEUES" envSeparator:","`
		StrictPriority bool     `env:"QUEUE_WORKER_STRICT_PRIORITY" envDefault:"false"`
	}
}

// CronConfig represents cron system configuration
type CronConfig struct {
	Enabled  bool   `env:"CRON_ENABLED" envDefault:"false"`
	TimeZone string `env:"CRON_TIME_ZONE" envDefault:"Asia/Ho_Chi_Minh"`

	SystemJobs struct {
		Cleanup struct {
			Enabled  bool   `env:"CRON_SYSTEM_CLEANUP_ENABLED" envDefault:"true"`
			Schedule string `env:"CRON_SYSTEM_CLEANUP_SCHEDULE" envDefault:"0 2 * * *"`
		}
		HealthCheck struct {
			Enabled  bool   `env:"CRON_SYSTEM_HEALTH_CHECK_ENABLED" envDefault:"true"`
			Schedule string `env:"CRON_SYSTEM_HEALTH_CHECK_SCHEDULE" envDefault:"*/15 * * * *"`
		}
		Backup struct {
			Enabled  bool   `env:"CRON_SYSTEM_BACKUP_ENABLED" envDefault:"false"`
			Schedule string `env:"CRON_SYSTEM_BACKUP_SCHEDULE" envDefault:"0 3 * * 0"`
		}
		Demo struct {
			Enabled  bool   `env:"CRON_SYSTEM_DEMO_ENABLED" envDefault:"true"`
			Schedule string `env:"CRON_SYSTEM_DEMO_SCHEDULE" envDefault:"* * * * *"`
		}
	}

	ModuleJobs struct {
		Auth struct {
			SessionCleanup struct {
				Enabled  bool   `env:"CRON_AUTH_SESSION_CLEANUP_ENABLED" envDefault:"true"`
				Schedule string `env:"CRON_AUTH_SESSION_CLEANUP_SCHEDULE" envDefault:"0 2 * * *"`
				MaxAge   string `env:"CRON_AUTH_SESSION_CLEANUP_MAX_AGE" envDefault:"24h"`
			}
			PasswordExpiry struct {
				Enabled    bool   `env:"CRON_AUTH_PASSWORD_EXPIRY_ENABLED" envDefault:"true"`
				Schedule   string `env:"CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE" envDefault:"0 9 * * *"`
				NotifyDays []int  `env:"CRON_AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS" envSeparator:","`
				TemplateID string `env:"CRON_AUTH_PASSWORD_EXPIRY_TEMPLATE_ID"`
			}
		}
		Media struct {
			ImageOptimization struct {
				Enabled   bool   `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED" envDefault:"true"`
				Schedule  string `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE" envDefault:"0 3 * * *"`
				Quality   int    `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_QUALITY" envDefault:"85"`
				BatchSize int    `env:"CRON_MEDIA_IMAGE_OPTIMIZATION_BATCH_SIZE" envDefault:"100"`
			}
			TempCleanup struct {
				Enabled  bool   `env:"CRON_MEDIA_TEMP_CLEANUP_ENABLED" envDefault:"true"`
				Schedule string `env:"CRON_MEDIA_TEMP_CLEANUP_SCHEDULE" envDefault:"0 1 * * *"`
				MaxAge   string `env:"CRON_MEDIA_TEMP_CLEANUP_MAX_AGE" envDefault:"24h"`
			}
		}
		Blog struct {
			ProcessPendingSchedules struct {
				Enabled   bool   `env:"CRON_BLOG_PROCESS_PENDING_SCHEDULES_ENABLED" envDefault:"true"`
				Schedule  string `env:"CRON_BLOG_PROCESS_PENDING_SCHEDULES_SCHEDULE" envDefault:"*/5 * * * *"`
				BatchSize int    `env:"CRON_BLOG_PROCESS_PENDING_SCHEDULES_BATCH_SIZE" envDefault:"50"`
			}
			CleanupOldSchedules struct {
				Enabled       bool   `env:"CRON_BLOG_CLEANUP_OLD_SCHEDULES_ENABLED" envDefault:"true"`
				Schedule      string `env:"CRON_BLOG_CLEANUP_OLD_SCHEDULES_SCHEDULE" envDefault:"0 2 * * *"`
				RetentionDays int    `env:"CRON_BLOG_CLEANUP_OLD_SCHEDULES_RETENTION_DAYS" envDefault:"30"`
			}
			PublishScheduledPosts struct {
				Enabled   bool   `env:"CRON_BLOG_PUBLISH_SCHEDULED_POSTS_ENABLED" envDefault:"true"`
				Schedule  string `env:"CRON_BLOG_PUBLISH_SCHEDULED_POSTS_SCHEDULE" envDefault:"*/1 * * * *"`
				BatchSize int    `env:"CRON_BLOG_PUBLISH_SCHEDULED_POSTS_BATCH_SIZE" envDefault:"20"`
			}
		}
	}
}
