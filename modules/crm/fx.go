package crm

import (
	"go.uber.org/fx"

	"wnapi/internal/fx/modules"
)

func init() {
	modules.GlobalRegistry.Register(&CRMModule{})
}

// CRMModule implements the FX module interface
type CRMModule struct{}

// Name returns the module name
func (m *CRMModule) Name() string {
	return "crm"
}

// Dependencies returns module dependencies
func (m *CRMModule) Dependencies() []string {
	return []string{"tenant", "auth"}
}

// Priority returns module loading priority
func (m *CRMModule) Priority() int {
	return 100 // Load after core modules
}

// Enabled returns whether the module is enabled
func (m *CRMModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default to enabled
}

// Module returns FX options for the module
func (m *CRMModule) Module() fx.Option {
	return fx.Module("crm",
		// Providers
		fx.Provide(
			// Configuration
			NewCRMConfig,

			// Simple route registration
			NewCRMHandler,
		),

		// Route registration
		fx.Invoke(RegisterCRMRoutes),
	)
}
