package crm

import (
	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/crm/internal"
)

// CRMHandler simple handler for crm module
type CRMHandler struct {
	config *internal.CRMConfig
	logger logger.Logger
}

// NewCRMConfig creates crm configuration
func NewCRMConfig(cfg config.Config) (*internal.CRMConfig, error) {
	return internal.LoadCRMConfig()
}

// NewCRMHandler creates crm handler
func NewCRMHandler(config *internal.CRMConfig, log logger.Logger) *CRMHandler {
	return &CRMHandler{
		config: config,
		logger: log,
	}
}

// RegisterCRMRoutes registers crm routes with gin.Engine
func RegisterCRMRoutes(handler *CRMHandler, engine *gin.Engine) error {
	// For now, just register a simple health check route
	// TODO: Implement full route registration when crm module is fully migrated to FX
	
	crmGroup := engine.Group("/api/v1/crm")
	crmGroup.GET("/healthy", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"module":  "crm",
			"message": "CRM module is running (FX mode)",
		})
	})
	
	return nil
}
