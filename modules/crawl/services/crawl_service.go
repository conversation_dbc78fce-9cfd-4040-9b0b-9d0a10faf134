package services

import (
	"context"
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/gocolly/colly/v2"
	"github.com/gocolly/colly/v2/debug"

	"wnapi/modules/crawl/dto/request"
	"wnapi/modules/crawl/dto/response"
	"wnapi/modules/crawl/internal"
	"wnapi/modules/crawl/models"
	"wnapi/modules/crawl/repository"
	"wnapi/modules/crawl/utils"
)

// CrawlService handles web crawling using Colly
type CrawlService struct {
	crawlJobRepo     repository.CrawlJobRepository
	crawlArticleRepo repository.CrawlArticleRepository
	config           *internal.CrawlConfig
}

// NewCrawlService creates a new crawl service
func NewCrawlService(
	crawlJobRepo repository.CrawlJobRepository,
	crawlArticleRepo repository.CrawlArticleRepository,
	config *internal.CrawlConfig,
) *CrawlService {
	return &CrawlService{
		crawlJobRepo:     crawlJobRepo,
		crawlArticleRepo: crawlArticleRepo,
		config:           config,
	}
}

// CrawlResult represents the result of a crawl operation
type CrawlResult struct {
	PagesProcessed  int
	ArticlesFound   int
	ArticlesCreated int
	Errors          []string
	Duration        time.Duration
}

// StartCrawl starts crawling for a specific job
func (s *CrawlService) StartCrawl(ctx context.Context, tenantID, jobID uint) (*CrawlResult, error) {
	// Get crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, jobID)
	if err != nil {
		return nil, fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return nil, fmt.Errorf("crawl job not found")
	}

	// Update job status to running
	err = s.crawlJobRepo.UpdateStatus(ctx, tenantID, jobID, string(internal.CrawlJobStatusRunning))
	if err != nil {
		return nil, fmt.Errorf("failed to update job status: %w", err)
	}

	startTime := time.Now()
	result := &CrawlResult{
		Errors: []string{},
	}

	// Create Colly collector
	c := s.createCollector(crawlJob)

	// Set up progress tracking
	progress := internal.CrawlProgress{
		PagesProcessed:    0,
		ArticlesFound:     0,
		ArticlesProcessed: 0,
		ErrorsCount:       0,
		StartedAt:         startTime,
	}

	// Configure callbacks
	s.setupCallbacks(c, crawlJob, &progress, result)

	// Start crawling
	err = c.Visit(crawlJob.StartURL)
	if err != nil {
		fmt.Printf("Failed to start crawling: %v\n", err)
		result.Errors = append(result.Errors, fmt.Sprintf("Failed to start crawling: %v", err))
	}

	// Wait for completion
	c.Wait()

	result.Duration = time.Since(startTime)

	// Update final job status
	finalStatus := internal.CrawlJobStatusCompleted
	if len(result.Errors) > 0 {
		finalStatus = internal.CrawlJobStatusFailed
	}

	err = s.crawlJobRepo.UpdateStatus(ctx, tenantID, jobID, string(finalStatus))
	if err != nil {
		// Log error but don't fail
		fmt.Printf("Failed to update final job status: %v\n", err)
	}

	// Update final progress
	now := time.Now()
	progress.CompletedAt = &now
	err = s.crawlJobRepo.UpdateProgress(ctx, tenantID, jobID, &progress)
	if err != nil {
		fmt.Printf("Failed to update final progress: %v\n", err)
	}

	fmt.Printf("Crawl job completed: job_id=%d, pages_processed=%d, articles_found=%d, duration=%v\n",
		jobID, result.PagesProcessed, result.ArticlesFound, result.Duration)

	return result, nil
}

// TestCrawl tests a crawl configuration without saving results
func (s *CrawlService) TestCrawl(ctx context.Context, req request.TestCrawlJobRequest) (*response.CrawlTestResponse, error) {
	fmt.Printf("Testing crawl configuration for URL: %s\n", req.StartURL)

	startTime := time.Now()
	response := &response.CrawlTestResponse{
		Success:        true,
		Errors:         []string{},
		SampleArticles: []*response.CrawlTestArticle{},
	}

	// Create temporary crawl job for testing
	testJob := &models.CrawlJob{
		StartURL: req.StartURL,
		Rules:    models.CrawlRuleJSON(req.Rules),
		Type:     internal.CrawlJobTypeGeneral,
	}

	// Create Colly collector with limited pages
	c := s.createCollector(testJob)
	c.Limit(&colly.LimitRule{
		DomainGlob:  "*",
		Parallelism: 1,
		Delay:       time.Duration(s.config.Delay) * time.Millisecond,
	})

	pagesVisited := 0
	maxPages := req.MaxPages
	if maxPages == 0 {
		maxPages = 5
	}

	// Set up test callbacks
	c.OnHTML("a[href]", func(e *colly.HTMLElement) {
		if pagesVisited >= maxPages {
			return
		}
		link := e.Attr("href")
		if s.shouldFollowLink(link, testJob) {
			e.Request.Visit(link)
		}
	})

	c.OnHTML("html", func(e *colly.HTMLElement) {
		if pagesVisited >= maxPages {
			return
		}
		pagesVisited++
		response.PagesFound++

		// Try to extract article content
		article := s.extractArticleContent(e, testJob)
		if article != nil {
			response.ArticlesFound++
			if len(response.SampleArticles) < 3 { // Limit sample articles
				response.SampleArticles = append(response.SampleArticles, &response.CrawlTestArticle{
					Title:    article.Title,
					URL:      article.URL,
					Content:  utils.TruncateString(article.Content, 200),
					ImageURL: article.ImageURL,
					Author:   article.Author,
				})
			}
		}
	})

	c.OnError(func(r *colly.Response, err error) {
		response.Errors = append(response.Errors, fmt.Sprintf("Error on %s: %v", r.Request.URL, err))
	})

	// Start test crawl
	err := c.Visit(req.StartURL)
	if err != nil {
		response.Success = false
		response.Errors = append(response.Errors, fmt.Sprintf("Failed to start test crawl: %v", err))
	}

	c.Wait()

	response.Duration = time.Since(startTime)

	if len(response.Errors) > 0 {
		response.Success = false
		response.Message = fmt.Sprintf("Test completed with %d errors", len(response.Errors))
	} else {
		response.Message = "Test completed successfully"
	}

	fmt.Printf("Test crawl completed: success=%t, pages_found=%d, articles_found=%d\n",
		response.Success, response.PagesFound, response.ArticlesFound)

	return response, nil
}

// createCollector creates a configured Colly collector
func (s *CrawlService) createCollector(job *models.CrawlJob) *colly.Collector {
	c := colly.NewCollector(
		colly.Debugger(&debug.LogDebugger{}),
	)

	// Configure collector
	c.UserAgent = s.config.UserAgent
	c.SetRequestTimeout(time.Duration(s.config.Timeout) * time.Second)

	// Set up limits
	c.Limit(&colly.LimitRule{
		DomainGlob:  "*",
		Parallelism: s.config.Concurrency,
		Delay:       time.Duration(s.config.Delay) * time.Millisecond,
	})

	// Configure allowed domains if specified
	rules := internal.CrawlRule(job.Rules)
	if len(rules.AllowedDomains) > 0 {
		c.AllowedDomains = rules.AllowedDomains
	}

	// Set max depth
	if rules.MaxDepth > 0 {
		c.MaxDepth = rules.MaxDepth
	} else if s.config.MaxDepth > 0 {
		c.MaxDepth = s.config.MaxDepth
	}

	return c
}

// setupCallbacks sets up Colly callbacks for crawling
func (s *CrawlService) setupCallbacks(c *colly.Collector, job *models.CrawlJob, progress *internal.CrawlProgress, result *CrawlResult) {
	// Follow links
	c.OnHTML("a[href]", func(e *colly.HTMLElement) {
		link := e.Attr("href")
		if s.shouldFollowLink(link, job) {
			e.Request.Visit(link)
		}
	})

	// Extract content from pages
	c.OnHTML("html", func(e *colly.HTMLElement) {
		progress.PagesProcessed++
		result.PagesProcessed++

		// Try to extract article content
		article := s.extractArticleContent(e, job)
		if article != nil {
			progress.ArticlesFound++
			result.ArticlesFound++

			// Save article to database
			article.TenantID = job.TenantID
			article.CrawlJobID = job.ID
			err := s.crawlArticleRepo.Create(context.Background(), job.TenantID, article)
			if err != nil {
				fmt.Printf("Failed to save article: %v\n", err)
				progress.ErrorsCount++
				result.Errors = append(result.Errors, fmt.Sprintf("Failed to save article: %v", err))
			} else {
				progress.ArticlesProcessed++
				result.ArticlesCreated++
			}
		}

		// Update progress periodically
		if progress.PagesProcessed%10 == 0 {
			err := s.crawlJobRepo.UpdateProgress(context.Background(), job.TenantID, job.ID, progress)
			if err != nil {
				fmt.Printf("Failed to update progress: %v\n", err)
			}
		}
	})

	// Handle errors
	c.OnError(func(r *colly.Response, err error) {
		fmt.Printf("Crawl error on %s: %v\n", r.Request.URL.String(), err)
		progress.ErrorsCount++
		result.Errors = append(result.Errors, fmt.Sprintf("Error on %s: %v", r.Request.URL, err))
	})

	// Log requests
	c.OnRequest(func(r *colly.Request) {
		fmt.Printf("Visiting: %s\n", r.URL.String())
	})

	// Log responses
	c.OnResponse(func(r *colly.Response) {
		fmt.Printf("Response received from %s: status=%d\n", r.Request.URL.String(), r.StatusCode)
	})
}

// shouldFollowLink determines if a link should be followed
func (s *CrawlService) shouldFollowLink(link string, job *models.CrawlJob) bool {
	if link == "" {
		return false
	}

	// Parse URL
	u, err := url.Parse(link)
	if err != nil {
		return false
	}

	// Skip non-HTTP(S) links
	if u.Scheme != "" && u.Scheme != "http" && u.Scheme != "https" {
		return false
	}

	rules := internal.CrawlRule(job.Rules)

	// Check disallowed paths
	for _, pattern := range rules.DisallowedPaths {
		matched, _ := regexp.MatchString(pattern, u.Path)
		if matched {
			return false
		}
	}

	// Check if robots.txt should be respected
	if rules.RespectRobots {
		// TODO: Implement robots.txt checking
	}

	return true
}

// extractArticleContent extracts article content from HTML
func (s *CrawlService) extractArticleContent(e *colly.HTMLElement, job *models.CrawlJob) *models.CrawlArticle {
	rules := internal.CrawlRule(job.Rules)

	// Extract title
	title := ""
	if rules.Selectors.Title != "" {
		title = strings.TrimSpace(e.ChildText(rules.Selectors.Title))
	} else {
		// Default title selectors
		title = strings.TrimSpace(e.ChildText("h1, .title, .post-title, .article-title, title"))
	}

	if title == "" {
		return nil // No title found, probably not an article
	}

	// Extract content
	content := ""
	if rules.Selectors.Content != "" {
		content = strings.TrimSpace(e.ChildText(rules.Selectors.Content))
	} else {
		// Default content selectors
		content = strings.TrimSpace(e.ChildText(".content, .post-content, .article-content, .entry-content, main, article"))
	}

	if content == "" {
		return nil // No content found
	}

	// Extract other fields
	author := ""
	if rules.Selectors.Author != "" {
		author = strings.TrimSpace(e.ChildText(rules.Selectors.Author))
	} else {
		author = strings.TrimSpace(e.ChildText(".author, .post-author, .article-author, .byline"))
	}

	imageURL := ""
	if rules.Selectors.Image != "" {
		imageURL = e.ChildAttr(rules.Selectors.Image, "src")
	} else {
		imageURL = e.ChildAttr(".featured-image img, .post-image img, .article-image img, img", "src")
	}

	// Make image URL absolute
	if imageURL != "" {
		imageURL = e.Request.AbsoluteURL(imageURL)
	}

	// Extract summary
	summary := ""
	if rules.Selectors.Summary != "" {
		summary = strings.TrimSpace(e.ChildText(rules.Selectors.Summary))
	} else {
		// Generate summary from content
		summary = utils.TruncateString(content, 200)
	}

	// Calculate word count
	wordCount := len(strings.Fields(content))

	// Generate content hash
	hash := utils.GenerateHash(content)

	// Create article
	article := &models.CrawlArticle{
		TenantID:    job.TenantID,
		WebsiteID:   job.WebsiteID,
		CrawlJobID:  job.ID,
		Title:       title,
		Slug:        utils.GenerateSlug(title),
		Content:     content,
		Summary:     summary,
		URL:         e.Request.URL.String(),
		ImageURL:    imageURL,
		Author:      author,
		Status:      internal.CrawlArticleStatusPending,
		WordCount:   wordCount,
		Language:    "en", // TODO: Detect language
		HashContent: hash,
		RetryCount:  0,
	}

	return article
}

// StopCrawl stops a running crawl job
func (s *CrawlService) StopCrawl(ctx context.Context, jobID uint) error {
	fmt.Printf("Stopping crawl job: %d\n", jobID)

	// TODO: Implement crawl stopping mechanism
	// This would typically involve:
	// 1. Setting a stop flag that the crawler checks
	// 2. Canceling the context
	// 3. Updating job status

	return nil
}

// GetCrawlProgress gets the current progress of a crawl job
func (s *CrawlService) GetCrawlProgress(ctx context.Context, tenantID, jobID uint) (*response.CrawlProgressResponse, error) {
	// Get crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, jobID)
	if err != nil {
		return nil, fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return nil, fmt.Errorf("crawl job not found")
	}

	progress := internal.CrawlProgress(crawlJob.Progress)

	// Calculate progress percentage
	progressPercentage := crawlJob.GetProgressPercentage()

	response := &response.CrawlProgressResponse{
		JobID:             jobID,
		Status:            string(crawlJob.Status),
		Progress:          progressPercentage,
		PagesProcessed:    progress.PagesProcessed,
		ArticlesFound:     progress.ArticlesFound,
		ArticlesProcessed: progress.ArticlesProcessed,
		ErrorsCount:       progress.ErrorsCount,
		StartedAt:         progress.StartedAt,
	}

	// Estimate finish time if job is running
	if crawlJob.Status == internal.CrawlJobStatusRunning && progress.PagesProcessed > 0 {
		elapsed := time.Since(progress.StartedAt)
		if progressPercentage > 0 {
			totalEstimated := time.Duration(float64(elapsed) / (progressPercentage / 100))
			finishTime := progress.StartedAt.Add(totalEstimated)
			response.EstimatedFinish = &finishTime
		}
	}

	return response, nil
}
