-- Remove website_id column from notification_user_preferences table
ALTER TABLE notification_user_preferences 
DROP INDEX unique_user_website_type_channel,
DROP INDEX idx_tenant_website,
DROP INDEX idx_website_id,
DROP COLUMN website_id;

-- Restore original unique constraint
ALTER TABLE notification_user_preferences 
ADD UNIQUE KEY unique_user_type_channel (user_id, notification_type, channel_code);
