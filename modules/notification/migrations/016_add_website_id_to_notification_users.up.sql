-- Add website_id column to notification_users table
ALTER TABLE notification_users 
ADD COLUMN website_id INT UNSIGNED AFTER tenant_id;

-- Update existing data with default website for each tenant
UPDATE notification_users nu 
JOIN (
    SELECT w.tenant_id, MIN(w.website_id) as default_website_id
    FROM site_websites w 
    WHERE w.status = 'active'
    GROUP BY w.tenant_id
) dw ON nu.tenant_id = dw.tenant_id
SET nu.website_id = dw.default_website_id;

-- For tenants without active websites, use the first available website
UPDATE notification_users nu 
JOIN (
    SELECT w.tenant_id, MIN(w.website_id) as default_website_id
    FROM site_websites w 
    GROUP BY w.tenant_id
) dw ON nu.tenant_id = dw.tenant_id
SET nu.website_id = dw.default_website_id
WHERE nu.website_id IS NULL;

-- Add NOT NULL constraint after data population
ALTER TABLE notification_users 
MODIFY COLUMN website_id INT UNSIGNED NOT NULL;

-- Update existing index to include website_id
ALTER TABLE notification_users 
DROP INDEX idx_tenant_user,
ADD INDEX idx_tenant_website_user (tenant_id, website_id, user_id),
ADD INDEX idx_website_id (website_id);
