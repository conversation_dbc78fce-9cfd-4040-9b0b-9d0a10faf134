CREATE TABLE notification_channels (
  channel_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  website_id INT UNSIGNED NOT NULL,
  channel_code VARCHAR(50) NOT NULL COMMENT 'Channel code (e.g., email, push, sms, in_app)',
  channel_name VARCHAR(100) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_website_id (website_id),
  INDEX idx_tenant_website (tenant_id, website_id),
  UNIQUE KEY unique_tenant_website_channel_code (tenant_id, website_id, channel_code)
);