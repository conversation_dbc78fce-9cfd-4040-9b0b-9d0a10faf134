CREATE TABLE notification_templates (
    template_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    template_code VARCHAR(100) NOT NULL,
    title_template TEXT NOT NULL COMMENT 'Template for notification title, supports placeholders',
    content_template TEXT NOT NULL COMMENT 'Template for notification content, supports placeholders',
    notification_type VARCHAR(50) NOT NULL COMMENT 'Type of notification (e.g., system, account, content)',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_template_code (tenant_id, website_id, template_code),
    INDEX idx_notification_type (notification_type),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_website_id (website_id),
    INDEX idx_tenant_website (tenant_id, website_id),
    UNIQUE KEY unique_tenant_website_template_code (tenant_id, website_id, template_code)
);