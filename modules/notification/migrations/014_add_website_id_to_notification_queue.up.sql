-- Add website_id column to notification_queue table
ALTER TABLE notification_queue 
ADD COLUMN website_id INT UNSIGNED AFTER tenant_id;

-- Update existing data with default website for each tenant
UPDATE notification_queue nq 
JOIN (
    SELECT w.tenant_id, MIN(w.website_id) as default_website_id
    FROM site_websites w 
    WHERE w.status = 'active'
    GROUP BY w.tenant_id
) dw ON nq.tenant_id = dw.tenant_id
SET nq.website_id = dw.default_website_id;

-- For tenants without active websites, use the first available website
UPDATE notification_queue nq 
JOIN (
    SELECT w.tenant_id, MIN(w.website_id) as default_website_id
    FROM site_websites w 
    GROUP BY w.tenant_id
) dw ON nq.tenant_id = dw.tenant_id
SET nq.website_id = dw.default_website_id
WHERE nq.website_id IS NULL;

-- Add NOT NULL constraint after data population
ALTER TABLE notification_queue 
MODIFY COLUMN website_id INT UNSIGNED NOT NULL;

-- Add indexes for performance
ALTER TABLE notification_queue 
ADD INDEX idx_website_id (website_id),
ADD INDEX idx_tenant_website (tenant_id, website_id);
