-- Add website_id column to notification_user_preferences table
ALTER TABLE notification_user_preferences 
ADD COLUMN website_id INT UNSIGNED AFTER tenant_id;

-- Update existing data with default website for each tenant
UPDATE notification_user_preferences nup 
JOIN (
    SELECT w.tenant_id, MIN(w.website_id) as default_website_id
    FROM site_websites w 
    WHERE w.status = 'active'
    GROUP BY w.tenant_id
) dw ON nup.tenant_id = dw.tenant_id
SET nup.website_id = dw.default_website_id;

-- For tenants without active websites, use the first available website
UPDATE notification_user_preferences nup 
JOIN (
    SELECT w.tenant_id, MIN(w.website_id) as default_website_id
    FROM site_websites w 
    GROUP BY w.tenant_id
) dw ON nup.tenant_id = dw.tenant_id
SET nup.website_id = dw.default_website_id
WHERE nup.website_id IS NULL;

-- Add NOT NULL constraint after data population
ALTER TABLE notification_user_preferences 
MODIFY COLUMN website_id INT UNSIGNED NOT NULL;

-- Drop existing unique constraint and recreate with website_id
ALTER TABLE notification_user_preferences 
DROP INDEX unique_user_type_channel;

-- Add new indexes for performance
ALTER TABLE notification_user_preferences 
ADD INDEX idx_website_id (website_id),
ADD INDEX idx_tenant_website (tenant_id, website_id),
ADD UNIQUE KEY unique_user_website_type_channel (user_id, website_id, notification_type, channel_code);
