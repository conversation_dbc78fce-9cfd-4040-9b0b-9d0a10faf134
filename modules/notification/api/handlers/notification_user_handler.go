package handlers

import (
	"strconv"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
	"wnapi/modules/notification/dto"
	"wnapi/modules/notification/internal"

	"github.com/gin-gonic/gin"
)

// NotificationUserHandler handles HTTP requests for notification users
type NotificationUserHandler struct {
	service internal.NotificationUserService
}

// getTenantFromContext extracts tenant ID from context (compatible with simple tenant middleware)
func (h *NotificationUserHandler) getTenantFromContext(c *gin.Context) uint {
	return auth.GetTenantID(c)
}

// getWebsiteFromContext extracts website ID from context
func (h *NotificationUserHandler) getWebsiteFromContext(c *gin.Context) uint {
	return auth.GetWebsiteID(c)
}

// NewNotificationUserHandler creates a new notification user handler
func NewNotificationUserHandler(service internal.NotificationUserService) *NotificationUserHandler {
	return &NotificationUserHandler{
		service: service,
	}
}

// GetUserNotifications gets notifications for a user
func (h *NotificationUserHandler) GetUserNotifications(c *gin.Context) {
	// Check if user is authenticated
	userID, exists := auth.GetUserIDFromContext(c)
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Get tenant ID from context
	tenantID := h.getTenantFromContext(c)

	var isRead *bool
	isReadStr := c.Query("is_read")
	if isReadStr != "" {
		isReadBool, err := strconv.ParseBool(isReadStr)
		if err == nil {
			isRead = &isReadBool
		}
	}

	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 20
	}

	// Convert userID to *uint for passing to service
	userIDPtr := &userID
	notifications, nextCursor, err := h.service.GetUserNotifications(c.Request.Context(), tenantID, isRead, cursor, limit, userIDPtr)
	if err != nil {
		response.InternalServerError(c, "Failed to get users notifications")
		return
	}

	// Convert to response DTOs
	notificationResponses := make([]dto.NotificationUserResponse, len(notifications))
	for i, notification := range notifications {
		notificationResponses[i] = dto.NotificationUserResponse{
			NotificationID: notification.NotificationID,
			TenantID:       notification.TenantID,
			UserID:         notification.UserID,
			Title:          notification.Title,
			Content:        notification.Content,
			IsRead:         notification.IsRead,
			CreatedAt:      notification.CreatedAt,
			UpdatedAt:      notification.UpdatedAt,
		}
	}

	meta := response.Meta{
		NextCursor: nextCursor,
		HasMore:    nextCursor != "",
	}

	response.Success(c, notificationResponses, &meta)
}

// GetNotificationByID gets a notification by ID
func (h *NotificationUserHandler) GetNotificationByID(c *gin.Context) {
	notificationIDStr := c.Param("id")
	notificationID, err := strconv.ParseUint(notificationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "Invalid notification ID", "INVALID_NOTIFICATION_ID", nil)
		return
	}

	notification, err := h.service.GetNotificationUserByID(c.Request.Context(), uint(notificationID))
	if err != nil {
		response.NotFound(c, "Notification not found")
		return
	}

	notificationResponse := dto.NotificationUserResponse{
		NotificationID: notification.NotificationID,
		TenantID:       notification.TenantID,
		UserID:         notification.UserID,
		Title:          notification.Title,
		Content:        notification.Content,
		IsRead:         notification.IsRead,
		CreatedAt:      notification.CreatedAt,
		UpdatedAt:      notification.UpdatedAt,
	}

	response.Success(c, notificationResponse, nil)
}

// CreateNotification creates a new notification
func (h *NotificationUserHandler) CreateNotification(c *gin.Context) {
	var req dto.CreateNotificationUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", "INVALID_REQUEST_BODY", nil)
		return
	}

	notification, err := h.service.CreateNotificationUser(c.Request.Context(), &req)
	if err != nil {
		response.InternalServerError(c, "Failed to create notification")
		return
	}

	notificationResponse := dto.NotificationUserResponse{
		NotificationID: notification.NotificationID,
		TenantID:       notification.TenantID,
		UserID:         notification.UserID,
		Title:          notification.Title,
		Content:        notification.Content,
		IsRead:         notification.IsRead,
		CreatedAt:      notification.CreatedAt,
		UpdatedAt:      notification.UpdatedAt,
	}

	response.Created(c, notificationResponse, nil)
}

// MarkAsRead marks a notification as read
// MarkAsRead marks a notification as read
func (h *NotificationUserHandler) MarkAsRead(c *gin.Context) {
	notificationIDStr := c.Param("id")
	notificationID, err := strconv.ParseUint(notificationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "Invalid notification ID", "INVALID_NOTIFICATION_ID", nil)
		return
	}

	err = h.service.MarkAsRead(c.Request.Context(), uint(notificationID))
	if err != nil {
		response.InternalServerError(c, "Failed to mark notification as read")
		return
	}

	response.Success(c, "Notification marked as read", nil)
}

// MarkAllAsRead marks all notifications for a user as read
func (h *NotificationUserHandler) MarkAllAsRead(c *gin.Context) {
	// Get user ID from JWT token
	userID, exists := auth.GetUserIDFromContext(c)
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Get tenant ID from context
	tenantID := h.getTenantFromContext(c)

	err := h.service.MarkAllAsRead(c.Request.Context(), userID, tenantID)
	if err != nil {
		response.InternalServerError(c, "Failed to mark all notifications as read")
		return
	}

	response.Success(c, "All notifications marked as read", nil)
}

// GetUnreadCount gets the count of unread notifications for a user
func (h *NotificationUserHandler) GetUnreadCount(c *gin.Context) {
	// Get user ID from JWT token
	userID, exists := auth.GetUserIDFromContext(c)
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Get tenant ID from context
	tenantID := h.getTenantFromContext(c)

	count, err := h.service.GetUnreadCount(c.Request.Context(), userID, tenantID)
	if err != nil {
		response.InternalServerError(c, "Failed to get unread count")
		return
	}

	response.Success(c, map[string]int{"count": count}, nil)
}

// UpdateNotification updates a notification
func (h *NotificationUserHandler) UpdateNotification(c *gin.Context) {
	notificationIDStr := c.Param("id")
	notificationID, err := strconv.ParseUint(notificationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "Invalid notification ID", "INVALID_NOTIFICATION_ID", nil)
		return
	}

	var req dto.UpdateNotificationUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", "INVALID_REQUEST_BODY", nil)
		return
	}

	notification, err := h.service.UpdateNotificationUser(c.Request.Context(), uint(notificationID), &req)
	if err != nil {
		response.InternalServerError(c, "Failed to update notification")
		return
	}

	notificationResponse := dto.NotificationUserResponse{
		NotificationID: notification.NotificationID,
		TenantID:       notification.TenantID,
		UserID:         notification.UserID,
		Title:          notification.Title,
		Content:        notification.Content,
		IsRead:         notification.IsRead,
		CreatedAt:      notification.CreatedAt,
		UpdatedAt:      notification.UpdatedAt,
	}

	response.Success(c, notificationResponse, nil)
}

// DeleteNotification deletes a notification
func (h *NotificationUserHandler) DeleteNotification(c *gin.Context) {
	notificationIDStr := c.Param("id")
	notificationID, err := strconv.ParseUint(notificationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "Invalid notification ID", "INVALID_NOTIFICATION_ID", nil)
		return
	}

	err = h.service.DeleteNotificationUser(c.Request.Context(), uint(notificationID))
	if err != nil {
		response.InternalServerError(c, "Failed to delete notification")
		return
	}

	response.Success(c, "Notification deleted successfully", nil)
}
