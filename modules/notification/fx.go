package notification

import (
	"go.uber.org/fx"

	"wnapi/internal/fx/modules"
	"wnapi/modules/notification/internal"
	"wnapi/modules/notification/repository"
	"wnapi/modules/notification/repository/mysql"
	"wnapi/modules/notification/service"
)

func init() {
	modules.GlobalRegistry.Register(&NotificationModule{})
}

// NotificationModule implements the FX module interface
type NotificationModule struct{}

// Name returns the module name
func (m *NotificationModule) Name() string {
	return "notification"
}

// Dependencies returns module dependencies
func (m *NotificationModule) Dependencies() []string {
	return []string{"tenant"} // Remove auth and rbac dependencies to break circular dependency
}

// Priority returns module loading priority
func (m *NotificationModule) Priority() int {
	return 30 // Load after tenant (1), auth (10), and rbac (20)
}

// Enabled returns whether the module is enabled
func (m *NotificationModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default to enabled
}

// GetMigrationPath returns path to module migrations
func (m *NotificationModule) GetMigrationPath() string {
	return "modules/notification/migrations"
}

// GetMigrationOrder returns migration priority order
func (m *NotificationModule) GetMigrationOrder() int {
	return 20 // Notification module runs after tenant
}

// Module returns FX options for the module
func (m *NotificationModule) Module() fx.Option {
	return fx.Module("notification",
		// Providers
		fx.Provide(
			// Configuration
			NewNotificationConfig,

			// Repositories
			fx.Annotate(
				mysql.NewNotificationModelsRepository,
				fx.As(new(repository.NotificationRepository)),
			),
			fx.Annotate(
				mysql.NewChannelRepository,
				fx.As(new(repository.ChannelRepository)),
			),
			fx.Annotate(
				mysql.NewTemplateRepository,
				fx.As(new(repository.TemplateRepository)),
			),
			fx.Annotate(
				mysql.NewNotificationUserRepository,
				fx.As(new(repository.NotificationUserRepository)),
			),

			// Services
			fx.Annotate(
				NewNotificationServiceProvider,
				fx.As(new(internal.NotificationService)),
			),
			fx.Annotate(
				service.NewNotificationUserService,
				fx.As(new(internal.NotificationUserService)),
			),
			NewChannelService,
			NewTemplateService,
			NewEmailService,
			NewQueueEmailService, // Queue-based email service for auth integration

			// Worker Service (uses global queue config)
			NewWorkerServiceProvider,

			// API Handler (uses shared JWT service)
			NewNotificationHandler,
		),

		// Route registration
		fx.Invoke(RegisterNotificationRoutes),

		// Queue handler registration
		fx.Invoke(RegisterNotificationQueueHandlersProvider),

		// Worker service startup
		fx.Invoke(StartWorkerServiceProvider),
	)
}
