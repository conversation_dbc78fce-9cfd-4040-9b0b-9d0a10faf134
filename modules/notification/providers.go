package notification

import (
	"context"
	"fmt"

	"github.com/gin-gonic/gin"

	"wnapi/internal/core"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/internal/pkg/queue"
	"wnapi/modules/notification/api"
	"wnapi/modules/notification/internal"
	"wnapi/modules/notification/repository"
	"wnapi/modules/notification/service"
	"wnapi/modules/notification/service/delivery"
	"wnapi/modules/notification/types"
	notificationQueue "wnapi/modules/notification/queue"
	tenantSvc "wnapi/modules/tenant/service"
)

// serverWrapper wraps gin.Engine to implement core.Server interface
type serverWrapper struct {
	engine *gin.Engine
}

func (s *serverWrapper) GetRouter() *gin.Engine {
	return s.engine
}

func (s *serverWrapper) Group(path string) *gin.RouterGroup {
	return s.engine.Group(path)
}

func (s *serverWrapper) GetAppBootstrap() *core.AppBootstrap {
	return nil // Not needed for route registration
}

func (s *serverWrapper) Start() error {
	return nil // Not needed for route registration
}

func (s *serverWrapper) Shutdown(ctx context.Context) error {
	return nil // Not needed for route registration
}

// NewNotificationConfig creates notification configuration
func NewNotificationConfig(cfg config.Config) *internal.NotificationConfig {
	fmt.Printf("=== NewNotificationConfig called ===\n")
	fmt.Printf("DEBUG: NewNotificationConfig - cfg=%v\n", cfg != nil)

	if cfg == nil {
		fmt.Printf("ERROR: NewNotificationConfig - config is nil\n")
		return nil
	}
	fmt.Printf("NewNotificationConfig - Config is available\n")

	notificationConfig, err := internal.NewNotificationConfigFromAppConfig(cfg)
	if err != nil {
		fmt.Printf("ERROR: NewNotificationConfig - failed to create config: %v\n", err)
		return nil
	}

	fmt.Printf("NewNotificationConfig - NotificationConfig created successfully, email_host=%s\n", notificationConfig.NOTIFICATION_EMAIL_HOST)
	return notificationConfig
}

// NewNotificationServiceProvider creates notification service with dependencies
func NewNotificationServiceProvider(
	repo repository.NotificationRepository,
	templateRepo repository.TemplateRepository,
	config *internal.NotificationConfig,
	log logger.Logger,
) internal.NotificationService {
	fmt.Printf("=== providers.NewNotificationServiceProvider called ===\n")
	fmt.Printf("DEBUG: repo=%v, templateRepo=%v, config=%v, log=%v\n", repo != nil, templateRepo != nil, config != nil, log != nil)

	// Check each dependency
	if repo == nil {
		fmt.Printf("ERROR: repo is nil\n")
		panic("repo is nil")
	}
	if templateRepo == nil {
		fmt.Printf("ERROR: templateRepo is nil\n")
		panic("templateRepo is nil")
	}
	if config == nil {
		fmt.Printf("ERROR: config is nil\n")
		panic("config is nil")
	}
	if log == nil {
		fmt.Printf("ERROR: log is nil\n")
		panic("log is nil")
	}

	fmt.Printf("All dependencies are valid, proceeding...\n")

	if log == nil {
		fmt.Printf("ERROR: providers.NewNotificationServiceProvider - logger is nil\n")
		return nil
	}

	log.Info("=== providers.NewNotificationService called ===")

	// Debug all dependencies
	if repo == nil {
		log.Error("providers.NewNotificationService: repo is nil")
		return nil
	}
	log.Info("providers.NewNotificationService: NotificationRepository is available")

	if templateRepo == nil {
		log.Error("providers.NewNotificationService: templateRepo is nil")
		return nil
	}
	log.Info("providers.NewNotificationService: TemplateRepository is available")

	if config == nil {
		log.Error("providers.NewNotificationService: config is nil")
		return nil
	}
	log.Info("providers.NewNotificationService: NotificationConfig is available", "email_host", config.NOTIFICATION_EMAIL_HOST)

	if log == nil {
		fmt.Printf("ERROR: providers.NewNotificationService: logger is nil\n")
		return nil
	}
	log.Info("providers.NewNotificationService: Logger is available")

	log.Info("providers.NewNotificationService: All dependencies are valid, creating notification service...")
	notificationService := service.NewNotificationServiceImpl(repo, templateRepo, *config, log)
	log.Info("providers.NewNotificationService: NotificationService created successfully")

	// Register delivery services
	RegisterDeliveryServices(notificationService, repo, config, log)

	// Return the concrete type as interface
	return internal.NotificationService(notificationService)
}

// NewChannelService creates channel service
func NewChannelService(repo repository.ChannelRepository) *service.ChannelService {
	return service.NewChannelService(repo)
}

// NewTemplateService creates template service
func NewTemplateService(
	templateRepo repository.TemplateRepository,
) *service.TemplateService {
	// For now, pass nil for cache since we don't have Redis cache setup
	return service.NewTemplateService(templateRepo, nil)
}

// NewEmailService creates email delivery service
func NewEmailService(config *internal.NotificationConfig) *delivery.EmailService {
	emailConfig := config.ToEmailConfig()
	return delivery.NewEmailService(emailConfig)
}

// NewQueueEmailService creates queue-based email service for auth integration
func NewQueueEmailService(queueClient queue.QueueClient) *service.EmailService {
	return service.NewEmailService(queueClient)
}

// RegisterDeliveryServices registers delivery services with the notification service
func RegisterDeliveryServices(
	notificationService *service.NotificationService,
	notificationRepo repository.NotificationRepository,
	config *internal.NotificationConfig,
	logger logger.Logger,
) {
	fmt.Printf("=== RegisterDeliveryServices called ===\n")
	fmt.Printf("DEBUG: notificationService=%v, notificationRepo=%v, config=%v, logger=%v\n",
		notificationService != nil, notificationRepo != nil, config != nil, logger != nil)

	if logger != nil {
		logger.Info("Registering delivery services")
	} else {
		fmt.Printf("ERROR: logger is nil in RegisterDeliveryServices\n")
		return
	}

	// Create email delivery service
	fmt.Printf("Creating email delivery service...\n")
	emailConfig := config.ToEmailConfig()
	fmt.Printf("Email config created: %+v\n", emailConfig)
	emailService := delivery.NewEmailService(emailConfig)
	fmt.Printf("Email service created: %v\n", emailService != nil)
	notificationService.AddDeliveryService("email", emailService)
	fmt.Printf("Email service added to notification service\n")
	logger.Info("Registered email delivery service")
	fmt.Printf("Email service registration completed\n")

	// Create SMS delivery service if enabled
	fmt.Printf("Checking SMS service configuration...\n")
	if config.NOTIFICATION_SMS_ENABLED {
		fmt.Printf("SMS service is enabled, creating...\n")
		smsConfig := config.ToSMSConfig()
		smsService := delivery.NewSMSService(smsConfig)
		notificationService.AddDeliveryService("sms", smsService)
		logger.Info("Registered SMS delivery service")
		fmt.Printf("SMS service registration completed\n")
	} else {
		fmt.Printf("SMS service is disabled\n")
		logger.Info("SMS delivery service disabled")
	}

	// Create push notification delivery service if enabled
	if config.NOTIFICATION_PUSH_ENABLED {
		pushConfig := config.ToPushConfig()
		pushService := delivery.NewPushService(pushConfig)
		notificationService.AddDeliveryService("push", pushService)
		logger.Info("Registered push delivery service")
	} else {
		logger.Info("Push delivery service disabled")
	}

	// Create in-app delivery service (always enabled for database notifications)
	inAppService := delivery.NewInAppService(notificationRepo)
	notificationService.AddDeliveryService("in_app", inAppService)
	logger.Info("Registered in-app delivery service")

	// Log total registered services
	fmt.Printf("=== Delivery service registration completed ===\n")
	fmt.Printf("Total services: %d\n", notificationService.GetDeliveryServiceCount())
	fmt.Printf("Channels: %v\n", notificationService.GetDeliveryServiceChannels())
	logger.Info("Delivery service registration completed",
		"total_services", notificationService.GetDeliveryServiceCount(),
		"channels", notificationService.GetDeliveryServiceChannels())
}

// NewWorkerServiceProvider creates worker service for processing notification tasks
func NewWorkerServiceProvider(
	queueConfig *queue.Config,
	logger logger.Logger,
	notificationService internal.NotificationService,
	notificationConfig *internal.NotificationConfig,
) (*service.WorkerService, error) {
	fmt.Printf("=== providers.NewWorkerServiceProvider called ===\n")

	if logger == nil {
		fmt.Printf("ERROR: providers.NewWorkerServiceProvider - logger is nil\n")
		return nil, fmt.Errorf("logger is nil")
	}

	logger.Info("=== NewWorkerService called ===")

	// Debug all dependencies
	if queueConfig == nil {
		logger.Error("queueConfig is nil")
		return nil, fmt.Errorf("queueConfig is nil")
	}
	logger.Info("Queue config", "addr", queueConfig.Redis.Addr, "db", queueConfig.Redis.DB, "password_set", queueConfig.Redis.Password != "")

	if logger == nil {
		fmt.Printf("ERROR: logger is nil\n")
		return nil, fmt.Errorf("logger is nil")
	}
	logger.Info("Logger is available")

	if notificationService == nil {
		logger.Error("notificationService is nil")
		return nil, fmt.Errorf("notificationService is nil")
	}
	logger.Info("NotificationService is available")

	if notificationConfig == nil {
		logger.Error("notificationConfig is nil")
		return nil, fmt.Errorf("notificationConfig is nil")
	}
	logger.Info("NotificationConfig is available", "email_host", notificationConfig.NOTIFICATION_EMAIL_HOST)

	logger.Info("All dependencies are valid, creating worker service...")
	workerService, err := service.NewWorkerService(queueConfig, logger, notificationService, notificationConfig)
	if err != nil {
		logger.Error("Failed to create worker service", "error", err)
		return nil, err
	}

	logger.Info("Worker service created successfully")
	return workerService, nil
}

// NewNotificationHandler creates notification API handler
func NewNotificationHandler(
	notificationService internal.NotificationService,
	channelService *service.ChannelService,
	templateService *service.TemplateService,
	middlewareFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	notificationUserService internal.NotificationUserService,
	tenantService tenantSvc.TenantService,
) *api.Handler {
	return api.NewHandler(
		notificationService, 
		channelService, 
		templateService, 
		middlewareFactory, 
		jwtService, 
		notificationUserService,
		tenantService,
	)
}

// RegisterNotificationRoutes registers notification routes with gin.Engine
func RegisterNotificationRoutes(handler *api.Handler, engine *gin.Engine) error {
	// Register proper handler routes first
	if handler != nil {
		// Use the RegisterRoutes method for FX system
		if err := handler.RegisterRoutes(engine); err != nil {
			return err
		}
	}

	// API Group for additional test endpoints (only register non-duplicate routes)
	apiGroup := engine.Group("/api/admin/v1/notifications")

	// Note: Most routes are already registered by handler.RegisterRoutes()
	// Only register additional test endpoints that are not in the handler

	// Additional test queue endpoint (different from the ones in handler)
	apiGroup.POST("/test-queue-simple", func(c *gin.Context) {
		// Get global queue manager
		queueManager := queue.GetGlobalManager()
		if queueManager == nil {
			c.JSON(500, gin.H{
				"error": "Queue manager not available",
			})
			return
		}

		// Test enqueue
		taskInfo, err := queueManager.EnqueueTask(c.Request.Context(), "test_task", map[string]interface{}{
			"message": "Hello from FX queue system!",
		}, &queue.EnqueueOptions{
			Queue:    "default",
			TenantID: 1,
		})

		if err != nil {
			c.JSON(500, gin.H{
				"error": err.Error(),
			})
			return
		}

		c.JSON(200, gin.H{
			"status":  "ok",
			"task_id": taskInfo.ID,
			"message": "Task enqueued successfully",
		})
	})

	// Test email template handler endpoint
	apiGroup.POST("/test-email-template", func(c *gin.Context) {
		// Parse request body
		var requestBody struct {
			TenantID   int               `json:"tenant_id" binding:"required"`
			UserID     int               `json:"user_id" binding:"required"`
			To         string            `json:"to" binding:"required,email"`
			Subject    string            `json:"subject" binding:"required"`
			TemplateID string            `json:"template_id" binding:"required"`
			Variables  map[string]string `json:"variables"`
		}

		if err := c.ShouldBindJSON(&requestBody); err != nil {
			c.JSON(400, gin.H{
				"error":   "Invalid request body",
				"details": err.Error(),
			})
			return
		}

		// Get global queue manager
		queueManager := queue.GetGlobalManager()
		if queueManager == nil {
			c.JSON(500, gin.H{
				"error": "Queue manager not available",
			})
			return
		}

		// Prepare email payload
		emailPayload := map[string]interface{}{
			"tenant_id":   requestBody.TenantID,
			"user_id":     requestBody.UserID,
			"to":          requestBody.To,
			"subject":     requestBody.Subject,
			"template_id": requestBody.TemplateID,
			"variables":   requestBody.Variables,
		}

		// Enqueue email template task
		taskInfo, err := queueManager.EnqueueTask(c.Request.Context(), types.TaskTypeSendEmailTemplate, emailPayload, &queue.EnqueueOptions{
			Queue:    "emails",
			TenantID: uint(requestBody.TenantID),
		})

		if err != nil {
			c.JSON(500, gin.H{
				"error":   "Failed to enqueue email template task",
				"details": err.Error(),
			})
			return
		}

		c.JSON(200, gin.H{
			"status":    "ok",
			"task_id":   taskInfo.ID,
			"task_type": types.TaskTypeSendEmailTemplate,
			"message":   "Email template task enqueued successfully",
			"payload": gin.H{
				"tenant_id":   requestBody.TenantID,
				"user_id":     requestBody.UserID,
				"to":          requestBody.To,
				"subject":     requestBody.Subject,
				"template_id": requestBody.TemplateID,
				"variables":   requestBody.Variables,
			},
		})
	})

	// Test direct email handler endpoint (without template)
	apiGroup.POST("/test-email-direct", func(c *gin.Context) {
		// Parse request body
		var requestBody struct {
			TenantID  int               `json:"tenant_id" binding:"required"`
			UserID    int               `json:"user_id" binding:"required"`
			To        string            `json:"to" binding:"required,email"`
			Subject   string            `json:"subject" binding:"required"`
			Body      string            `json:"body" binding:"required"`
			CC        []string          `json:"cc"`
			BCC       []string          `json:"bcc"`
			Variables map[string]string `json:"variables"`
		}

		if err := c.ShouldBindJSON(&requestBody); err != nil {
			c.JSON(400, gin.H{
				"error":   "Invalid request body",
				"details": err.Error(),
			})
			return
		}

		// Get global queue manager
		queueManager := queue.GetGlobalManager()
		if queueManager == nil {
			c.JSON(500, gin.H{
				"error": "Queue manager not available",
			})
			return
		}

		// Prepare email payload
		emailPayload := map[string]interface{}{
			"tenant_id": requestBody.TenantID,
			"user_id":   requestBody.UserID,
			"to":        requestBody.To,
			"subject":   requestBody.Subject,
			"body":      requestBody.Body,
			"variables": requestBody.Variables,
		}

		// Add optional fields if provided
		if len(requestBody.CC) > 0 {
			emailPayload["cc"] = requestBody.CC
		}
		if len(requestBody.BCC) > 0 {
			emailPayload["bcc"] = requestBody.BCC
		}

		// Enqueue direct email task
		taskInfo, err := queueManager.EnqueueTask(c.Request.Context(), types.TaskTypeSendEmail, emailPayload, &queue.EnqueueOptions{
			Queue:    "emails",
			TenantID: uint(requestBody.TenantID),
		})

		if err != nil {
			c.JSON(500, gin.H{
				"error":   "Failed to enqueue direct email task",
				"details": err.Error(),
			})
			return
		}

		c.JSON(200, gin.H{
			"status":    "ok",
			"task_id":   taskInfo.ID,
			"task_type": types.TaskTypeSendEmail,
			"message":   "Direct email task enqueued successfully",
			"payload": gin.H{
				"tenant_id": requestBody.TenantID,
				"user_id":   requestBody.UserID,
				"to":        requestBody.To,
				"subject":   requestBody.Subject,
				"body":      requestBody.Body,
				"cc":        requestBody.CC,
				"bcc":       requestBody.BCC,
				"variables": requestBody.Variables,
			},
		})
	})

	// Test SMS handler endpoint
	apiGroup.POST("/test-sms", func(c *gin.Context) {
		// Parse request body
		var requestBody struct {
			TenantID   int               `json:"tenant_id" binding:"required"`
			UserID     int               `json:"user_id" binding:"required"`
			To         string            `json:"to" binding:"required"`
			Message    string            `json:"message" binding:"required"`
			TemplateID string            `json:"template_id"`
			From       string            `json:"from"`
			Variables  map[string]string `json:"variables"`
		}

		if err := c.ShouldBindJSON(&requestBody); err != nil {
			c.JSON(400, gin.H{
				"error":   "Invalid request body",
				"details": err.Error(),
			})
			return
		}

		// Get global queue manager
		queueManager := queue.GetGlobalManager()
		if queueManager == nil {
			c.JSON(500, gin.H{
				"error": "Queue manager not available",
			})
			return
		}

		// Prepare SMS payload
		smsPayload := map[string]interface{}{
			"tenant_id": requestBody.TenantID,
			"user_id":   requestBody.UserID,
			"to":        requestBody.To,
			"message":   requestBody.Message,
			"variables": requestBody.Variables,
		}

		// Add optional fields if provided
		if requestBody.TemplateID != "" {
			smsPayload["template_id"] = requestBody.TemplateID
		}
		if requestBody.From != "" {
			smsPayload["from"] = requestBody.From
		}

		// Enqueue SMS task
		taskInfo, err := queueManager.EnqueueTask(c.Request.Context(), types.TaskTypeSendSMS, smsPayload, &queue.EnqueueOptions{
			Queue:    "sms",
			TenantID: uint(requestBody.TenantID),
		})

		if err != nil {
			c.JSON(500, gin.H{
				"error":   "Failed to enqueue SMS task",
				"details": err.Error(),
			})
			return
		}

		c.JSON(200, gin.H{
			"status":    "ok",
			"task_id":   taskInfo.ID,
			"task_type": types.TaskTypeSendSMS,
			"message":   "SMS task enqueued successfully",
			"payload": gin.H{
				"tenant_id":   requestBody.TenantID,
				"user_id":     requestBody.UserID,
				"to":          requestBody.To,
				"message":     requestBody.Message,
				"template_id": requestBody.TemplateID,
				"from":        requestBody.From,
				"variables":   requestBody.Variables,
			},
		})
	})

	// Note: /test-queue/send-notification is already registered by handler.RegisterRoutes()
	// Skipping duplicate registration

	// Test Push notification handler endpoint
	apiGroup.POST("/test-push", func(c *gin.Context) {
		// Parse request body
		var requestBody struct {
			TenantID     int                    `json:"tenant_id" binding:"required"`
			UserID       int                    `json:"user_id" binding:"required"`
			DeviceID     string                 `json:"device_id"`
			DeviceType   string                 `json:"device_type"`
			Title        string                 `json:"title" binding:"required"`
			Body         string                 `json:"body" binding:"required"`
			DeviceTokens []string               `json:"device_tokens"`
			Icon         string                 `json:"icon"`
			Sound        string                 `json:"sound"`
			Badge        string                 `json:"badge"`
			ClickAction  string                 `json:"click_action"`
			Data         map[string]interface{} `json:"data"`
			Options      map[string]interface{} `json:"options"`
		}

		if err := c.ShouldBindJSON(&requestBody); err != nil {
			c.JSON(400, gin.H{
				"error":   "Invalid request body",
				"details": err.Error(),
			})
			return
		}

		// Get global queue manager
		queueManager := queue.GetGlobalManager()
		if queueManager == nil {
			c.JSON(500, gin.H{
				"error": "Queue manager not available",
			})
			return
		}

		// Prepare Push payload
		pushPayload := map[string]interface{}{
			"tenant_id": requestBody.TenantID,
			"user_id":   requestBody.UserID,
			"title":     requestBody.Title,
			"body":      requestBody.Body,
		}

		// Add optional fields if provided
		if requestBody.DeviceID != "" {
			pushPayload["device_id"] = requestBody.DeviceID
		}
		if requestBody.DeviceType != "" {
			pushPayload["device_type"] = requestBody.DeviceType
		}
		if len(requestBody.DeviceTokens) > 0 {
			pushPayload["device_tokens"] = requestBody.DeviceTokens
		}
		if requestBody.Icon != "" {
			pushPayload["icon"] = requestBody.Icon
		}
		if requestBody.Sound != "" {
			pushPayload["sound"] = requestBody.Sound
		}
		if requestBody.Badge != "" {
			pushPayload["badge"] = requestBody.Badge
		}
		if requestBody.ClickAction != "" {
			pushPayload["click_action"] = requestBody.ClickAction
		}
		if requestBody.Data != nil {
			pushPayload["data"] = requestBody.Data
		}
		if requestBody.Options != nil {
			pushPayload["options"] = requestBody.Options
		}

		// Enqueue Push notification task
		taskInfo, err := queueManager.EnqueueTask(c.Request.Context(), types.TaskTypeSendPush, pushPayload, &queue.EnqueueOptions{
			Queue:    "push",
			TenantID: uint(requestBody.TenantID),
		})

		if err != nil {
			c.JSON(500, gin.H{
				"error":   "Failed to enqueue push notification task",
				"details": err.Error(),
			})
			return
		}

		c.JSON(200, gin.H{
			"status":    "ok",
			"task_id":   taskInfo.ID,
			"task_type": types.TaskTypeSendPush,
			"message":   "Push notification task enqueued successfully",
			"payload":   pushPayload,
		})
	})

	// TODO: Add other routes when we have proper JWT middleware integration
	// For now, just register the health check to verify the module is loading

	return nil
}

// RegisterNotificationQueueHandlersProvider registers notification queue handlers with the queue manager
func RegisterNotificationQueueHandlersProvider(
	notificationService internal.NotificationService,
	notificationConfig *internal.NotificationConfig,
	queueManager *queue.QueueManager,
	log logger.Logger,
) error {
	fmt.Printf("=== providers.RegisterNotificationQueueHandlersProvider called ===\n")

	if log == nil {
		fmt.Printf("ERROR: providers.RegisterNotificationQueueHandlersProvider - logger is nil\n")
		return fmt.Errorf("logger is nil")
	}

	fmt.Printf("Logger is available\n")
	log.Info("=== RegisterNotificationQueueHandlers called ===")
	log.Info("Starting notification queue handler registration")

	// Check if queue is enabled
	if queueManager == nil {
		fmt.Printf("Queue manager is nil\n")
		log.Info("Queue manager not available, skipping queue handler registration")
		return nil
	}

	fmt.Printf("Queue manager is available\n")
	log.Info("Queue manager available, proceeding with registration")

	// Create queue handlers
	fmt.Printf("About to create queue handlers\n")
	queueHandlers := notificationQueue.NewQueueHandlers(log, notificationService, notificationConfig)
	fmt.Printf("Queue handlers created\n")
	log.Info("Queue handlers created successfully")

	// Get task definitions with handlers
	fmt.Printf("About to get task definitions\n")
	taskDefinitions := queueHandlers.GetTaskDefinitionsWithHandlers()
	fmt.Printf("Got task definitions: %d\n", len(taskDefinitions))
	log.Info("Retrieved task definitions", "count", len(taskDefinitions))

	// Register each task definition
	registeredCount := 0
	fmt.Printf("About to register %d task definitions\n", len(taskDefinitions))
	for i, taskDef := range taskDefinitions {
		fmt.Printf("Registering task %d: %s\n", i+1, taskDef.Type)
		log.Info("Attempting to register task definition", "type", taskDef.Type)
		if err := queueManager.RegisterTask(taskDef); err != nil {
			fmt.Printf("Failed to register task %s: %v\n", taskDef.Type, err)
			log.Warn("Failed to register task definition", "type", taskDef.Type, "error", err)
		} else {
			fmt.Printf("Successfully registered task %s\n", taskDef.Type)
			log.Info("Successfully registered task definition", "type", taskDef.Type)
			registeredCount++
		}
	}

	fmt.Printf("Registration completed: %d/%d tasks registered\n", registeredCount, len(taskDefinitions))
	log.Info("Notification queue handlers registration completed", "registered", registeredCount, "total", len(taskDefinitions))
	return nil
}

// StartWorkerServiceProvider starts the notification worker service for processing tasks
func StartWorkerServiceProvider(
	workerService *service.WorkerService,
	log logger.Logger,
) error {
	fmt.Printf("=== providers.StartWorkerServiceProvider called ===\n")

	if log == nil {
		fmt.Printf("ERROR: providers.StartWorkerServiceProvider - logger is nil\n")
		return fmt.Errorf("logger is nil")
	}

	fmt.Printf("=== StartWorkerService called ===\n")
	log.Info("=== StartWorkerService called ===")

	// Debug dependencies
	if log == nil {
		fmt.Printf("ERROR: StartWorkerService - logger is nil\n")
		return fmt.Errorf("logger is nil")
	}

	if workerService == nil {
		fmt.Printf("Worker service is nil\n")
		log.Error("StartWorkerService - Worker service is nil, this indicates NewWorkerService failed or wasn't called")
		log.Info("Worker service not available, skipping worker startup")
		return nil
	}

	fmt.Printf("Worker service is available, starting...\n")
	log.Info("StartWorkerService - Worker service is available, proceeding with startup")
	log.Info("Starting notification worker service")

	// Start worker service in background
	go func() {
		ctx := context.Background()
		log.Info("About to start worker service in goroutine")
		if err := workerService.Start(ctx); err != nil {
			log.Error("Failed to start worker service", "error", err)
		} else {
			log.Info("Worker service started successfully")
		}
	}()

	log.Info("Notification worker service startup initiated")
	return nil
}
