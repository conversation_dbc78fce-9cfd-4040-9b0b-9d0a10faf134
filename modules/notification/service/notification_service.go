package service

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"log"
	"text/template"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/notification/events"
	"wnapi/modules/notification/internal"
	"wnapi/modules/notification/models"
	"wnapi/modules/notification/repository"
	"wnapi/modules/notification/repository/redis"
)

// DeliveryService defines methods for delivering notifications through different channels
type DeliveryService interface {
	Send(ctx context.Context, notification *models.Notification, userID uint) error
}

// NotificationService implements the notification service interface
type NotificationService struct {
	repo             repository.NotificationRepository
	templateRepo     repository.TemplateRepository
	config           internal.NotificationConfig
	logger           logger.Logger
	cache            *redis.NotificationCache
	eventPublisher   *events.Publisher
	deliveryServices map[string]DeliveryService
}

// NewNotificationServiceImpl creates a new notification service implementation
func NewNotificationServiceImpl(
	repo repository.NotificationRepository,
	templateRepo repository.TemplateRepository,
	config internal.NotificationConfig,
	log logger.Logger,
) *NotificationService {
	log.Info("=== service.NewNotificationService called (from service package) ===")

	// Debug all dependencies
	if repo == nil {
		log.Error("service.NewNotificationService: repo is nil")
		return nil
	}
	log.Info("service.NewNotificationService: NotificationRepository is available")

	if templateRepo == nil {
		log.Error("service.NewNotificationService: templateRepo is nil")
		return nil
	}
	log.Info("service.NewNotificationService: TemplateRepository is available")

	if log == nil {
		fmt.Printf("ERROR: service.NewNotificationService: logger is nil\n")
		return nil
	}
	log.Info("service.NewNotificationService: Logger is available")

	log.Info("service.NewNotificationService: NotificationConfig is available", "email_host", config.NOTIFICATION_EMAIL_HOST)

	log.Info("service.NewNotificationService: All dependencies are valid, creating notification service...")
	notificationService := &NotificationService{
		repo:             repo,
		templateRepo:     templateRepo,
		config:           config,
		logger:           log,
		deliveryServices: make(map[string]DeliveryService),
	}
	log.Info("service.NewNotificationService: NotificationService created successfully")

	return notificationService
}

// WithEventPublisher adds event publisher to service
func (s *NotificationService) WithEventPublisher(publisher *events.Publisher) *NotificationService {
	s.eventPublisher = publisher
	return s
}

// WithCache adds cache to service
func (s *NotificationService) WithCache(cache *redis.NotificationCache) *NotificationService {
	s.cache = cache
	return s
}

// AddDeliveryService adds a delivery service for a specific channel
func (s *NotificationService) AddDeliveryService(channel string, service DeliveryService) *NotificationService {
	if s.deliveryServices == nil {
		s.deliveryServices = make(map[string]DeliveryService)
	}
	s.deliveryServices[channel] = service

	if s.logger != nil {
		s.logger.Info("Delivery service registered",
			"channel", channel,
			"total_services", len(s.deliveryServices))
	}

	return s
}

// WithDeliveryServices sets multiple delivery services at once
func (s *NotificationService) WithDeliveryServices(services map[string]DeliveryService) *NotificationService {
	s.deliveryServices = services
	return s
}

// GetDeliveryServiceCount returns the number of registered delivery services
func (s *NotificationService) GetDeliveryServiceCount() int {
	return len(s.deliveryServices)
}

// GetDeliveryServiceChannels returns the list of registered delivery service channels
func (s *NotificationService) GetDeliveryServiceChannels() []string {
	channels := make([]string, 0, len(s.deliveryServices))
	for channel := range s.deliveryServices {
		channels = append(channels, channel)
	}
	return channels
}

// CreateNotification creates a new notification
func (s *NotificationService) CreateNotification(ctx context.Context, notification interface{}) (int, error) {
	// Cast to notification model
	var notif *models.Notification
	if n, ok := notification.(*models.Notification); ok {
		notif = n
	} else {
		return 0, fmt.Errorf("invalid notification type")
	}

	// Create notification
	id, err := s.repo.Create(ctx, notif)
	if err != nil {
		return 0, err
	}

	// Publish event if publisher is available
	if s.eventPublisher != nil {
		// Set ID from result
		notif.NotificationID = uint(id)

		// Use tenant ID 1 as default if not set (can be improved later)
		tenantID := uint(1)

		if err := s.eventPublisher.PublishNotificationCreated(ctx, notif, tenantID); err != nil {
			s.logger.Warn("Failed to publish notification created event: %v", err)
		}
	}

	// Invalidate cache if available
	if s.cache != nil {
		s.cache.InvalidateUserUnreadCount(ctx, int(notif.UserID))
	}

	return id, nil
}

// GetNotification gets a notification by ID
func (s *NotificationService) GetNotification(ctx context.Context, notificationID int) (interface{}, error) {
	return s.repo.GetByID(ctx, notificationID)
}

// GetUserNotifications gets notifications for a user
func (s *NotificationService) GetUserNotifications(ctx context.Context, cursor string, limit int, tenantID uint, userID *uint) ([]interface{}, string, error) {
	// Default limit if not specified
	if limit <= 0 {
		limit = 10
	}

	// Prepare filters
	filters := make(map[string]interface{})

	// Add userID to filters if provided
	if userID != nil {
		filters["user_id"] = *userID
	}

	// Get notifications by tenant ID
	notifications, nextCursor, err := s.repo.GetNotificationsByTenantID(ctx, int(tenantID), cursor, limit, filters)
	if err != nil {
		return nil, "", err
	}

	// Convert to interface slice
	result := make([]interface{}, len(notifications))
	for i, n := range notifications {
		result[i] = n
	}

	return result, nextCursor, nil
}

// MarkAsRead marks a notification as read
func (s *NotificationService) MarkAsRead(ctx context.Context, notificationID int) error {
	// Get notification first to get user ID
	notification, err := s.repo.GetByID(ctx, notificationID)
	if err != nil {
		return err
	}

	if notification == nil {
		return fmt.Errorf("notification not found")
	}

	// Mark as read
	err = s.repo.MarkAsRead(ctx, notificationID)
	if err != nil {
		return err
	}

	// Publish event if publisher is available
	if s.eventPublisher != nil {
		tenantID := uint(1) // Default tenant ID

		if err := s.eventPublisher.PublishNotificationRead(
			ctx,
			uint(notificationID),
			notification.UserID,
			tenantID,
		); err != nil {
			s.logger.Warn("Failed to publish notification read event: %v", err)
		}
	}

	// Invalidate cache if available
	if s.cache != nil {
		s.cache.InvalidateUserUnreadCount(ctx, int(notification.UserID))
	}

	return nil
}

// MarkAllAsRead marks all notifications as read for a user
func (s *NotificationService) MarkAllAsRead(ctx context.Context, userID int) error {
	// Mark all as read
	err := s.repo.MarkAllAsRead(ctx, userID)
	if err != nil {
		return err
	}

	// Publish event if publisher is available
	if s.eventPublisher != nil {
		tenantID := uint(1) // Default tenant ID

		if err := s.eventPublisher.PublishNotificationAllRead(
			ctx,
			uint(userID),
			tenantID,
		); err != nil {
			s.logger.Warn("Failed to publish notification all read event: %v", err)
		}
	}

	// Invalidate cache if available
	if s.cache != nil {
		s.cache.InvalidateUserUnreadCount(ctx, userID)
	}

	return nil
}

// GetUnreadCount gets the number of unread notifications for a user
func (s *NotificationService) GetUnreadCount(ctx context.Context, userID int) (int, error) {
	// Try to get from cache first
	if s.cache != nil {
		count, err := s.cache.GetUserUnreadCount(ctx, userID)
		if err == nil {
			return count, nil
		}
		// Continue if cache miss
	}

	// Get from database
	count, err := s.repo.GetUnreadCount(ctx, userID)
	if err != nil {
		return 0, err
	}

	// Store in cache if available
	if s.cache != nil {
		s.cache.SetUserUnreadCount(ctx, userID, count)
	}

	return count, nil
}

// DeleteNotification deletes a notification
func (s *NotificationService) DeleteNotification(ctx context.Context, notificationID int) error {
	// Get notification first to get user ID
	notification, err := s.repo.GetByID(ctx, notificationID)
	if err != nil {
		return err
	}

	if notification == nil {
		return fmt.Errorf("notification not found")
	}

	// Delete notification
	err = s.repo.Delete(ctx, notificationID)
	if err != nil {
		return err
	}

	// Publish event if publisher is available
	if s.eventPublisher != nil {
		tenantID := uint(1) // Default tenant ID

		// Create a notification model for the event
		notif := &models.Notification{
			NotificationID: uint(notificationID),
			UserID:         notification.UserID,
			Title:          notification.Title,
			Content:        notification.Content,
			UpdatedAt:      time.Now(),
		}

		if err := s.eventPublisher.PublishNotificationDeleted(ctx, notif, tenantID); err != nil {
			s.logger.Warn("Failed to publish notification deleted event: %v", err)
		}
	}

	// Invalidate cache if available
	if s.cache != nil {
		s.cache.InvalidateUserUnreadCount(ctx, int(notification.UserID))
	}

	return nil
}

// SendNotification sends a notification through the appropriate channels
// SendNotification gửi thông báo đã tồn tại trong database thông qua các kênh delivery đã đăng ký
// Hàm này thực hiện các bước sau:
// 1. Lấy thông tin notification từ database theo ID
// 2. Ghi log debug về các delivery service có sẵn
// 3. Kiểm tra xem có delivery service nào được đăng ký hay không
// 4. Xác định các kênh delivery phù hợp dựa trên notification type (mapping trực tiếp)
// 5. Gửi notification qua từng kênh delivery được phép (email, SMS, push, in-app)
// 6. Cập nhật trạng thái notification trong database sau khi gửi thành công
// 7. Ghi log chi tiết cho việc theo dõi và debug
//
// Tham số:
//   - ctx: Context để quản lý timeout và cancellation
//   - notificationID: ID của notification cần gửi (phải tồn tại trong database)
//
// Trả về:
//   - error: nil nếu gửi thành công, hoặc error mô tả lỗi xảy ra
func (s *NotificationService) SendNotification(ctx context.Context, notificationID int) error {
	// Bước 1: Lấy thông tin notification từ database theo ID
	// Kiểm tra xem notification có tồn tại hay không
	notification, err := s.repo.GetByID(ctx, notificationID)
	if err != nil {
		return fmt.Errorf("failed to get notification: %w", err)
	}

	// Bước 2: Ghi log thông tin debug về các delivery service có sẵn
	// Điều này giúp theo dõi và debug khi có vấn đề với việc gửi notification
	if s.logger != nil {
		s.logger.Info("SendNotification called",
			"notification_id", notificationID,
			"available_delivery_services", len(s.deliveryServices))

		// Liệt kê tất cả các kênh delivery đã đăng ký
		for channel := range s.deliveryServices {
			s.logger.Info("Available delivery service", "channel", channel)
		}
	}

	// Bước 3: Kiểm tra xem có delivery service nào được đăng ký hay không
	// Nếu không có delivery service nào, không thể gửi notification
	if len(s.deliveryServices) == 0 {
		if s.logger != nil {
			s.logger.Error("No delivery services registered")
		}
		return fmt.Errorf("no delivery services available")
	}

	// Bước 4: Xác định các kênh delivery phù hợp dựa trên notification type
	// Logic đơn giản: notification_type map trực tiếp với delivery channel
	enabledChannels, err := s.getEnabledChannelsForNotification(ctx, notification)
	if err != nil {
		if s.logger != nil {
			s.logger.Error("Failed to get enabled channels for notification",
				"notification_id", notificationID,
				"error", err)
		}
		// Fallback: sử dụng tất cả kênh có sẵn nếu không lấy được preferences
		enabledChannels = s.getAllAvailableChannels()
	}

	if s.logger != nil {
		s.logger.Info("Enabled channels for notification",
			"notification_id", notificationID,
			"notification_type", notification.NotificationType,
			"user_id", notification.UserID,
			"enabled_channels", enabledChannels)
	}

	// Bước 5: Lặp qua các kênh delivery đã được enable và gửi notification
	// Sử dụng strategy "best effort" - cố gắng gửi qua tất cả kênh được phép
	var lastError error
	sentCount := 0 // Đếm số kênh gửi thành công

	for channel, service := range s.deliveryServices {
		// Kiểm tra xem kênh này có được enable cho notification type này không
		if !s.isChannelEnabled(channel, enabledChannels) {
			if s.logger != nil {
				s.logger.Info("Skipping disabled channel",
					"notification_id", notificationID,
					"channel", channel,
					"notification_type", notification.NotificationType)
			}
			continue
		}
		if s.logger != nil {
			s.logger.Info("Attempting to send notification",
				"notification_id", notificationID,
				"channel", channel)
		}

		// Thử gửi notification qua kênh hiện tại
		if err := service.Send(ctx, notification, notification.UserID); err != nil {
			lastError = err
			// Ghi log lỗi nhưng tiếp tục với các kênh khác
			// Điều này đảm bảo một kênh lỗi không ảnh hưởng đến các kênh khác
			if s.logger != nil {
				s.logger.Error("Failed to send notification via channel",
					"notification_id", notificationID,
					"channel", channel,
					"error", err)
			}
		} else {
			sentCount++
			if s.logger != nil {
				s.logger.Info("Successfully sent notification via channel",
					"notification_id", notificationID,
					"channel", channel)
			}
		}
	}

	// Bước 6: Cập nhật trạng thái notification nếu ít nhất một kênh gửi thành công
	// Áp dụng nguyên tắc "partial success" - chỉ cần một kênh thành công là coi như đã gửi
	if sentCount > 0 {
		notification.IsSent = true
		now := time.Now()
		notification.SentAt = &now // Ghi lại thời điểm gửi thành công
		notification.UpdatedAt = time.Now()

		// Lưu trạng thái đã gửi vào database
		if err := s.repo.Update(ctx, notification); err != nil {
			return fmt.Errorf("failed to update notification sent status: %w", err)
		}

		if s.logger != nil {
			s.logger.Info("Notification marked as sent",
				"notification_id", notificationID,
				"successful_channels", sentCount)
		}

		return nil // Thành công
	}

	// Bước 7: Xử lý trường hợp không có kênh nào gửi thành công
	// Ghi log lỗi chi tiết và trả về error để caller có thể xử lý
	if s.logger != nil {
		s.logger.Error("Failed to send notification through any channel",
			"notification_id", notificationID,
			"last_error", lastError)
	}

	// Trả về error mô tả chi tiết nguyên nhân thất bại
	if lastError == nil {
		return fmt.Errorf("failed to send notification through any channel: no delivery services succeeded")
	}
	return fmt.Errorf("failed to send notification through any channel: %w", lastError)
}

// CreateNotificationFromTemplate creates a notification using a template
func (s *NotificationService) CreateNotificationFromTemplate(
	ctx context.Context,
	userID int,
	code string,
	placeholders map[string]interface{},
	referenceType, referenceID string,
) (int, error) {
	// Fetch template
	tmpl, err := s.templateRepo.GetByCode(ctx, code)
	if err != nil {
		return 0, fmt.Errorf("failed to get template: %w", err)
	}

	if tmpl == nil {
		return 0, fmt.Errorf("template with code %s not found", code)
	}

	if !tmpl.IsActive {
		return 0, errors.New("template is not active")
	}

	// Debug placeholders
	log.Println("DEBUG: Placeholders được truyền vào:", placeholders)
	for k, v := range placeholders {
		log.Printf("DEBUG: Key: %s, Value: %v, Type: %T", k, v, v)
	}

	// Parse title template
	titleTmpl, err := template.New("title").Parse(tmpl.TitleTemplate)
	if err != nil {
		return 0, fmt.Errorf("failed to parse title template: %w", err)
	}

	// Parse content template
	contentTmpl, err := template.New("content").Parse(tmpl.ContentTemplate)
	if err != nil {
		return 0, fmt.Errorf("failed to parse content template: %w", err)
	}

	// Execute title template
	var titleBuffer bytes.Buffer
	if err := titleTmpl.Execute(&titleBuffer, placeholders); err != nil {
		log.Printf("DEBUG: Lỗi thực thi title template: %v", err)
		return 0, fmt.Errorf("failed to execute title template: %w", err)
	}

	// Execute content template
	var contentBuffer bytes.Buffer
	if err := contentTmpl.Execute(&contentBuffer, placeholders); err != nil {
		log.Printf("DEBUG: Lỗi thực thi content template: %v", err)
		log.Printf("DEBUG: Content template: %s", tmpl.ContentTemplate)
		return 0, fmt.Errorf("failed to execute content template: %w", err)
	}

	//log.Println("Rendered title:", titleBuffer.String())
	//log.Println("Rendered content:", contentBuffer.String())

	// Create notification
	return s.CreateNotification(
		ctx,
		&models.Notification{
			TenantID:         uint(1), // Default tenant ID - can be improved to get from context
			UserID:           uint(userID),
			Title:            titleBuffer.String(),
			Content:          contentBuffer.String(),
			NotificationType: tmpl.NotificationType,
			ReferenceType:    referenceType,
			ReferenceID:      referenceID,
			IsRead:           false,
			IsSent:           false,
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		},
	)
}

// getEnabledChannelsForNotification xác định kênh delivery dựa trên notification_type
// Logic đơn giản: notification_type trực tiếp map với delivery channel
func (s *NotificationService) getEnabledChannelsForNotification(ctx context.Context, notification *models.Notification) ([]string, error) {
	if s.logger != nil {
		s.logger.Info("Getting enabled channels for notification",
			"notification_id", notification.NotificationID,
			"user_id", notification.UserID,
			"notification_type", notification.NotificationType)
	}

	// Map notification_type trực tiếp với delivery channel
	var enabledChannels []string

	switch notification.NotificationType {
	case "email":
		enabledChannels = []string{"email"}
	case "sms":
		enabledChannels = []string{"sms"}
	case "push":
		enabledChannels = []string{"push"}
	case "in_app":
		enabledChannels = []string{"in_app"}
	case "system":
		// System notifications gửi qua in_app và email
		enabledChannels = []string{"in_app", "email"}
	case "account":
		// Account notifications gửi qua email và in_app
		enabledChannels = []string{"email", "in_app"}
	case "content":
		// Content notifications chỉ gửi qua in_app
		enabledChannels = []string{"in_app"}
	default:
		// Fallback: gửi qua in_app cho các loại notification không xác định
		enabledChannels = []string{"in_app"}
		if s.logger != nil {
			s.logger.Warn("Unknown notification type, using in_app as fallback",
				"notification_type", notification.NotificationType)
		}
	}

	// Lọc chỉ những channel thực sự có delivery service
	availableChannels := s.getAllAvailableChannels()
	filteredChannels := make([]string, 0)

	for _, channel := range enabledChannels {
		if s.isChannelEnabled(channel, availableChannels) {
			filteredChannels = append(filteredChannels, channel)
		} else {
			if s.logger != nil {
				s.logger.Warn("Channel not available, skipping",
					"channel", channel,
					"notification_type", notification.NotificationType)
			}
		}
	}

	if s.logger != nil {
		s.logger.Info("Determined channels for notification",
			"notification_type", notification.NotificationType,
			"requested_channels", enabledChannels,
			"available_channels", filteredChannels)
	}

	return filteredChannels, nil
}

// getAllAvailableChannels trả về danh sách tất cả kênh delivery có sẵn
func (s *NotificationService) getAllAvailableChannels() []string {
	channels := make([]string, 0, len(s.deliveryServices))
	for channel := range s.deliveryServices {
		channels = append(channels, channel)
	}
	return channels
}

// isChannelEnabled kiểm tra xem một kênh có được enable hay không
func (s *NotificationService) isChannelEnabled(channel string, enabledChannels []string) bool {
	for _, enabledChannel := range enabledChannels {
		if enabledChannel == channel {
			return true
		}
	}
	return false
}
