package models

import (
	"time"
)

// Preference represents a user's notification preference
type Preference struct {
	PreferenceID     uint      `db:"preference_id" json:"preference_id" gorm:"primaryKey"`
	TenantID         uint      `db:"tenant_id" json:"tenant_id" gorm:"not null;index"`
	WebsiteID        uint      `db:"website_id" json:"website_id" gorm:"not null;index"`
	UserID           uint      `db:"user_id" json:"user_id" gorm:"not null;index"`
	NotificationType string    `db:"notification_type" json:"notification_type" gorm:"not null"`
	ChannelCode      string    `db:"channel_code" json:"channel_code" gorm:"not null"`
	IsEnabled        bool      `db:"is_enabled" json:"is_enabled" gorm:"default:true"`
	CreatedAt        time.Time `db:"created_at" json:"created_at"`
	UpdatedAt        time.Time `db:"updated_at" json:"updated_at"`
}

// TableName returns the table name for the Preference model
func (Preference) TableName() string {
	return "notification_user_preferences"
}
