package models

import (
	"time"
)

// Channel represents a notification delivery channel
type Channel struct {
	ChannelID   int       `db:"channel_id" json:"channel_id" gorm:"primaryKey"`
	TenantID    uint      `db:"tenant_id" json:"tenant_id" gorm:"not null;index"`
	WebsiteID   uint      `db:"website_id" json:"website_id" gorm:"not null;index"`
	ChannelCode string    `db:"channel_code" json:"channel_code" gorm:"not null"`
	ChannelName string    `db:"channel_name" json:"channel_name" gorm:"not null"`
	IsActive    bool      `db:"is_active" json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time `db:"created_at" json:"created_at"`
	UpdatedAt   time.Time `db:"updated_at" json:"updated_at"`
}

// Channel code constants
const (
	ChannelEmail     = "email"
	ChannelPush      = "push"
	ChannelSMS       = "sms"
	ChannelInApp     = "in_app"
	ChannelWebSocket = "websocket"
	ChannelTelegram  = "telegram"
)

// TableName returns the table name for the Channel model
func (Channel) TableName() string {
	return "notification_channels"
}
