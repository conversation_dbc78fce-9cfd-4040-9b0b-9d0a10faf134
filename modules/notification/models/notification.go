package models

import (
	"time"
)

// Notification đại diện cho một thông báo trong hệ thống
type Notification struct {
	NotificationID   uint       `json:"notification_id" gorm:"primaryKey" db:"notification_id"`
	TenantID         uint       `json:"tenant_id" gorm:"not null;index" db:"tenant_id"`
	WebsiteID        uint       `json:"website_id" gorm:"not null;index" db:"website_id"`
	UserID           uint       `json:"user_id" gorm:"not null;index" db:"user_id"`
	Title            string     `json:"title" gorm:"not null" db:"title"`
	Content          string     `json:"content" gorm:"not null" db:"content"`
	NotificationType string     `json:"notification_type" gorm:"not null" db:"notification_type"`
	ReferenceType    string     `json:"reference_type" db:"reference_type"`
	ReferenceID      string     `json:"reference_id" db:"reference_id"`
	IsRead           bool       `json:"is_read" gorm:"default:false" db:"is_read"`
	IsSent           bool       `json:"is_sent" gorm:"default:false" db:"is_sent"`
	SentAt           *time.Time `json:"sent_at,omitempty" db:"sent_at"`
	ReadAt           *time.Time `json:"read_at,omitempty" db:"read_at"`
	CreatedAt        time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at" db:"updated_at"`
}

// NotificationType constants
const (
	NotificationTypeInfo      = "info"
	NotificationTypeWarning   = "warning"
	NotificationTypeAlert     = "alert"
	NotificationTypePromotion = "promotion"
	NotificationTypeSystem    = "system"
)

// TableName trả về tên bảng trong cơ sở dữ liệu
func (Notification) TableName() string {
	return "notifications"
}
