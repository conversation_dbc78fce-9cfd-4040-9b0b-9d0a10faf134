package product

import (
	"go.uber.org/fx"

	"wnapi/internal/fx/modules"
	"wnapi/modules/product/api"
	"wnapi/modules/product/repository"
	"wnapi/modules/product/repository/mysql"
	"wnapi/modules/product/service"
)

func init() {
	modules.GlobalRegistry.Register(&ProductModule{})
}

// ProductModule implements the FX module interface
type ProductModule struct{}

// Name returns the module name
func (m *ProductModule) Name() string {
	return "product"
}

// Dependencies returns module dependencies
func (m *ProductModule) Dependencies() []string {
	return []string{"tenant", "auth"}
}

// Priority returns module loading priority
func (m *ProductModule) Priority() int {
	return 60 // Load after core modules
}

// Enabled returns whether the module is enabled
func (m *ProductModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default to enabled
}

// GetMigrationPath returns path to module migrations
func (m *ProductModule) GetMigrationPath() string {
	return "modules/product/migrations"
}

// GetMigrationOrder returns migration priority order
func (m *ProductModule) GetMigrationOrder() int {
	return 60 // Product module runs after core modules
}

// Module returns FX options for the module
func (m *ProductModule) Module() fx.Option {
	return fx.Module("product",
		// Provide configuration
		fx.Provide(
			NewProductConfig,
		),

		// Provide repositories
		fx.Provide(
			fx.Annotate(
				mysql.NewCategoryRepository,
				fx.As(new(repository.CategoryRepository)),
			),
			fx.Annotate(
				mysql.NewProductRepository,
				fx.As(new(repository.ProductRepository)),
			),
			fx.Annotate(
				mysql.NewProductAttributeGroupRepository,
				fx.As(new(repository.ProductAttributeGroupRepository)),
			),
			fx.Annotate(
				mysql.NewProductAttributeRepository,
				fx.As(new(repository.ProductAttributeRepository)),
			),
			fx.Annotate(
				mysql.NewProductAttributeOptionRepository,
				fx.As(new(repository.ProductAttributeOptionRepository)),
			),
			fx.Annotate(
				mysql.NewProductAttributeValueRepository,
				fx.As(new(repository.ProductAttributeValueRepository)),
			),
			fx.Annotate(
				mysql.NewProductVariantRepository,
				fx.As(new(repository.ProductVariantRepository)),
			),
			fx.Annotate(
				mysql.NewProductVariantAttributeValueRepository,
				fx.As(new(repository.ProductVariantAttributeValueRepository)),
			),
			fx.Annotate(
				mysql.NewProductConfigurableAttributeRepository,
				fx.As(new(repository.ProductConfigurableAttributeRepository)),
			),
		),

		// Provide services
		fx.Provide(
			fx.Annotate(
				service.NewCategoryService,
				fx.As(new(service.CategoryService)),
			),
			fx.Annotate(
				service.NewProductService,
				fx.As(new(service.ProductService)),
			),
			fx.Annotate(
				service.NewProductAttributeGroupService,
				fx.As(new(service.ProductAttributeGroupService)),
			),
			fx.Annotate(
				service.NewProductAttributeService,
				fx.As(new(service.ProductAttributeService)),
			),
			fx.Annotate(
				service.NewProductAttributeOptionService,
				fx.As(new(service.ProductAttributeOptionService)),
			),
			fx.Annotate(
				service.NewProductAttributeValueService,
				fx.As(new(service.ProductAttributeValueService)),
			),
			fx.Annotate(
				service.NewProductVariantService,
				fx.As(new(service.ProductVariantService)),
			),
			fx.Annotate(
				service.NewProductVariantAttributeValueService,
				fx.As(new(service.ProductVariantAttributeValueService)),
			),
		),

		// Provide API handler
		fx.Provide(
			api.NewHandler,
		),

		// Route registration
		fx.Invoke(RegisterProductRoutes),
	)
}
