package api

import (
	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/internal/middleware"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/product/api/handlers"
	"wnapi/modules/product/repository/mysql"
	productService "wnapi/modules/product/service"
	tenantService "wnapi/modules/tenant/service"
)

// Handler là đối tượng chính xử lý API cho module Product
type Handler struct {
	// Handlers
	categoryHandler                     *handlers.CategoryHandler
	productHandler                      *handlers.ProductHandler
	productAttributeGroupHandler        *handlers.ProductAttributeGroupHandler
	productAttributeHandler             *handlers.ProductAttributeHandler
	productAttributeOptionHandler       *handlers.ProductAttributeOptionHandler
	productAttributeValueHandler        *handlers.ProductAttributeValueHandler
	productVariantHandler               *handlers.ProductVariantHandler
	productVariantAttributeValueHandler *handlers.ProductVariantAttributeValueHandler

	// Dependencies
	middlewareFactory *permission.MiddlewareFactory
	jwtService        *auth.JWTService
	logger            logger.Logger
	db                *sqlx.DB
	gormDB            *gorm.DB
	tenantService     tenantService.TenantService
}

// NewHandler tạo một handler mới
func NewHandler(db *sqlx.DB, gormDB *gorm.DB, mwFactory *permission.MiddlewareFactory, jwtService *auth.JWTService, log logger.Logger, tenantService tenantService.TenantService) *Handler {
	// Initialize repositories
	categoryRepo := mysql.NewCategoryRepository(db)
	productRepo := mysql.NewProductRepository(db, gormDB)
	productAttributeGroupRepo := mysql.NewProductAttributeGroupRepository(db, gormDB)
	productAttributeRepo := mysql.NewProductAttributeRepository(db, gormDB)
	productAttributeOptionRepo := mysql.NewProductAttributeOptionRepository(db, gormDB)
	productAttributeValueRepo := mysql.NewProductAttributeValueRepository(db, gormDB)
	productVariantRepo := mysql.NewProductVariantRepository(db, gormDB)
	productVariantAttributeValueRepo := mysql.NewProductVariantAttributeValueRepository(db, gormDB)
	productConfigurableAttributeRepo := mysql.NewProductConfigurableAttributeRepository(db, gormDB)

	// Initialize services
	categoryService := productService.NewCategoryService(categoryRepo)
	productAttributeService := productService.NewProductAttributeService(productAttributeRepo)
	productAttributeOptionService := productService.NewProductAttributeOptionService(productAttributeOptionRepo, productAttributeService)
	productAttributeValueService := productService.NewProductAttributeValueService(productAttributeValueRepo, productAttributeRepo, productAttributeOptionRepo)
	productVariantService := productService.NewProductVariantService(productVariantRepo, productRepo)
	productVariantAttributeValueService := productService.NewProductVariantAttributeValueService(productVariantAttributeValueRepo, productVariantRepo, productAttributeRepo)
	prodService := productService.NewProductService(
		productRepo,
		productVariantService,
		productVariantAttributeValueService,
		productAttributeService,
		productConfigurableAttributeRepo,
		productAttributeOptionService,
	)
	productAttributeGroupService := productService.NewProductAttributeGroupService(productAttributeGroupRepo)

	// Initialize handlers
	categoryHandler := handlers.NewCategoryHandler(categoryService)
	productHandler := handlers.NewProductHandler(prodService)
	productAttributeHandler := handlers.NewProductAttributeHandler(productAttributeService)
	productAttributeOptionHandler := handlers.NewProductAttributeOptionHandler(productAttributeOptionService)
	productAttributeValueHandler := handlers.NewProductAttributeValueHandler(productAttributeValueService)
	productVariantHandler := handlers.NewProductVariantHandler(productVariantService)
	productVariantAttributeValueHandler := handlers.NewProductVariantAttributeValueHandler(productVariantAttributeValueService)
	productAttributeGroupHandler := handlers.NewProductAttributeGroupHandler(productAttributeGroupService)

	return &Handler{
		categoryHandler:                     categoryHandler,
		productHandler:                      productHandler,
		productAttributeHandler:             productAttributeHandler,
		productAttributeOptionHandler:       productAttributeOptionHandler,
		productAttributeValueHandler:        productAttributeValueHandler,
		productVariantHandler:               productVariantHandler,
		productVariantAttributeValueHandler: productVariantAttributeValueHandler,
		productAttributeGroupHandler:        productAttributeGroupHandler,
		middlewareFactory:                   mwFactory,
		jwtService:                          jwtService,
		logger:                              log,
		db:                                  db,
		gormDB:                              gormDB,
		tenantService:                       tenantService,
	}
}

// RegisterRoutes đăng ký tất cả routes cho module Product
func (h *Handler) RegisterRoutes(router *gin.Engine) error {
	apiGroup := router.Group("/api/admin/v1")
	apiGroup.Use(tracing.GinMiddleware("product"))

	apiGroup.GET("/health", h.healthCheck)

	categoryGroup := apiGroup.Group("/categories")
	{
		// Public endpoints
		categoryGroup.GET("", h.categoryHandler.List)
		categoryGroup.GET("/tree", h.categoryHandler.GetTree)
		categoryGroup.GET("/:id", h.categoryHandler.Get)
		categoryGroup.GET("/:id/subtree", h.categoryHandler.GetSubtree)
		categoryGroup.GET("/slug/:slug", h.categoryHandler.GetBySlug)

		// Protected endpoints - require JWT authentication
		protectedCategories := categoryGroup.Group("")
		protectedCategories.Use(h.jwtService.JWTAuthMiddleware())
		protectedCategories.Use(middleware.TenantMiddleware(h.tenantService))
		{
			protectedCategories.POST("", h.categoryHandler.Create)
			protectedCategories.PUT("/:id", h.categoryHandler.Update)
			protectedCategories.DELETE("/:id", h.categoryHandler.Delete)
			protectedCategories.POST("/move", h.categoryHandler.MoveNode)
			protectedCategories.POST("/position", h.categoryHandler.UpdatePosition)
			protectedCategories.POST("/rebuild", h.categoryHandler.RebuildTree)
			protectedCategories.POST("/move-root", h.categoryHandler.MoveNodeRoot)
		}
	}

	productGroup := apiGroup.Group("/product")
	{
		// Protected routes - require JWT authentication
		protectedProducts := productGroup.Group("")
		protectedProducts.Use(h.jwtService.JWTAuthMiddleware())
		protectedProducts.Use(middleware.TenantMiddleware(h.tenantService))
		{
			protectedProducts.GET("/",
				h.middlewareFactory.RequirePermission("product.list"),
				h.productHandler.List,
			)
			protectedProducts.GET("/:id",
				h.middlewareFactory.RequirePermission("product.read"),
				h.productHandler.Get,
			)
			protectedProducts.POST("",
				h.middlewareFactory.RequirePermission("product.create"),
				h.productHandler.Create,
			)
			protectedProducts.PUT("/:id",
				h.middlewareFactory.RequirePermission("product.update"),
				h.productHandler.Update,
			)
			protectedProducts.DELETE("/:id",
				h.middlewareFactory.RequirePermission("product.delete"),
				h.productHandler.Delete,
			)
			protectedProducts.PUT("/:id/publish",
				h.middlewareFactory.RequireAllPermissions("product.update", "product.publish"),
				h.productHandler.Publish,
			)
			protectedProducts.GET("/stats",
				h.middlewareFactory.RequireAnyPermission("product.read", "reports.view"),
				h.productHandler.GetStats,
			)
		}
	}

	attributeGroupGroup := apiGroup.Group("/attribute-groups")
	{
		// Public endpoints
		attributeGroupGroup.GET("", h.productAttributeGroupHandler.ListProductAttributeGroups)
		attributeGroupGroup.GET("/all", h.productAttributeGroupHandler.GetAllProductAttributeGroups)
		attributeGroupGroup.GET("/:id", h.productAttributeGroupHandler.GetProductAttributeGroup)
		attributeGroupGroup.GET("/code/:code", h.productAttributeGroupHandler.GetProductAttributeGroupByCode)

		// Protected endpoints - require JWT authentication
		protectedAttributeGroups := attributeGroupGroup.Group("")
		protectedAttributeGroups.Use(h.jwtService.JWTAuthMiddleware())
		protectedAttributeGroups.Use(middleware.TenantMiddleware(h.tenantService))
		{
			protectedAttributeGroups.POST("", h.productAttributeGroupHandler.CreateProductAttributeGroup)
			protectedAttributeGroups.PUT("/:id", h.productAttributeGroupHandler.UpdateProductAttributeGroup)
			protectedAttributeGroups.DELETE("/:id", h.productAttributeGroupHandler.DeleteProductAttributeGroup)
		}
	}

	return nil
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "product",
		"message": "Product module is running",
	})
}

// RegisterRoutes registers all API routes for the product module (backward compatibility)
func RegisterRoutes(router *gin.Engine, db *sqlx.DB, gormDB *gorm.DB, mwFactory *permission.MiddlewareFactory, jwtConfig auth.JWTConfig, log logger.Logger, tenantService tenantService.TenantService) {
	// Create JWT service from config for backward compatibility
	jwtService := auth.NewJWTService(jwtConfig)
	handler := NewHandler(db, gormDB, mwFactory, jwtService, log, tenantService)
	handler.RegisterRoutes(router)
}

// SetupRoutes sets up all the product module routes (for backward compatibility)
func SetupRoutes(router *gin.Engine, db *sqlx.DB, gormDB *gorm.DB, mwFactory *permission.MiddlewareFactory, jwtConfig auth.JWTConfig, log logger.Logger, tenantService tenantService.TenantService) {
	RegisterRoutes(router, db, gormDB, mwFactory, jwtConfig, log, tenantService)
}
