package mysql

import (
	"context"
	"fmt"
	"strconv"

	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/internal/pkg/pagination"
	"wnapi/modules/product/models"
)

// ProductAttributeRepository đại diện cho repository xử lý thuộc tính sản phẩm
type ProductAttributeRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewProductAttributeRepository tạo một ProductAttributeRepository mới
func NewProductAttributeRepository(db *sqlx.DB, gormDB *gorm.DB) *ProductAttributeRepository {
	return &ProductAttributeRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create tạo một thuộc tính sản phẩm mới
func (r *ProductAttributeRepository) Create(ctx context.Context, attribute *models.ProductAttribute) error {
	// Tạo bản ghi mới với GORM
	if err := r.gormDB.WithContext(ctx).Create(attribute).Error; err != nil {
		return fmt.Errorf("failed to create product attribute: %w", err)
	}
	return nil
}

// Update cập nhật một thuộc tính sản phẩm
func (r *ProductAttributeRepository) Update(ctx context.Context, attribute *models.ProductAttribute) error {
	// Cập nhật bản ghi với GORM
	if err := r.gormDB.WithContext(ctx).Save(attribute).Error; err != nil {
		return fmt.Errorf("failed to update product attribute: %w", err)
	}
	return nil
}

// GetByID lấy thuộc tính sản phẩm theo ID
func (r *ProductAttributeRepository) GetByID(ctx context.Context, tenantID int, attributeID int) (*models.ProductAttribute, error) {
	var attribute models.ProductAttribute
	if err := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND attribute_id = ?", tenantID, attributeID).First(&attribute).Error; err != nil {
		return nil, fmt.Errorf("failed to get product attribute by ID: %w", err)
	}
	return &attribute, nil
}

// GetByCode lấy thuộc tính sản phẩm theo mã
func (r *ProductAttributeRepository) GetByCode(ctx context.Context, tenantID int, code string) (*models.ProductAttribute, error) {
	var attribute models.ProductAttribute
	if err := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND code = ?", tenantID, code).First(&attribute).Error; err != nil {
		return nil, fmt.Errorf("failed to get product attribute by code: %w", err)
	}
	return &attribute, nil
}

// List lấy danh sách thuộc tính sản phẩm với phân trang
func (r *ProductAttributeRepository) List(ctx context.Context, tenantID int, cursor string, limit int, orderField, orderDirection string) ([]*models.ProductAttribute, *pagination.CursorInfo, error) {
	var attributes []*models.ProductAttribute
	query := r.gormDB.WithContext(ctx).Where("tenant_id = ?", tenantID)

	// Xử lý phân trang
	if cursor != "" {
		// Giải mã cursor
		cursorData, err := pagination.DecodeSimpleCursor(cursor)
		if err != nil {
			return nil, nil, fmt.Errorf("invalid cursor: %w", err)
		}

		// Chuyển đổi cursor thành ID
		cursorID, err := strconv.Atoi(cursorData)
		if err != nil {
			return nil, nil, fmt.Errorf("invalid cursor ID: %w", err)
		}

		// Áp dụng điều kiện cursor
		query = query.Where("attribute_id > ?", cursorID)
	}

	// Áp dụng sắp xếp
	if orderField == "" {
		orderField = "attribute_id"
	}
	if orderDirection == "" {
		orderDirection = "ASC"
	}
	query = query.Order(fmt.Sprintf("%s %s", orderField, orderDirection)).Limit(limit)

	// Thực hiện truy vấn
	if err := query.Find(&attributes).Error; err != nil {
		return nil, nil, fmt.Errorf("failed to list product attributes: %w", err)
	}

	// Tạo cursor tiếp theo
	var nextCursor string
	hasMore := len(attributes) == limit
	if len(attributes) > 0 && hasMore {
		lastAttribute := attributes[len(attributes)-1]
		nextCursor = pagination.EncodeCursor(strconv.Itoa(int(lastAttribute.AttributeID)))
	}

	cursorInfo := &pagination.CursorInfo{
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}

	return attributes, cursorInfo, nil
}

// Delete xóa thuộc tính sản phẩm
func (r *ProductAttributeRepository) Delete(ctx context.Context, tenantID int, attributeID int) error {
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND attribute_id = ?", tenantID, attributeID).Delete(&models.ProductAttribute{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete product attribute: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("product attribute not found")
	}
	return nil
}

// ListByGroupID lấy tất cả thuộc tính sản phẩm theo ID nhóm
func (r *ProductAttributeRepository) ListByGroupID(ctx context.Context, tenantID int, groupID int) ([]*models.ProductAttribute, error) {
	var attributes []*models.ProductAttribute
	if err := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND group_id = ?", tenantID, groupID).Order("display_order ASC, name ASC").Find(&attributes).Error; err != nil {
		return nil, fmt.Errorf("failed to get product attributes by group ID: %w", err)
	}
	return attributes, nil
}

// GetConfigurableAttributes lấy tất cả thuộc tính sản phẩm có thể cấu hình
func (r *ProductAttributeRepository) GetConfigurableAttributes(ctx context.Context, tenantID int) ([]*models.ProductAttribute, error) {
	var attributes []*models.ProductAttribute
	if err := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND is_configurable = ?", tenantID, true).Order("display_order ASC, name ASC").Find(&attributes).Error; err != nil {
		return nil, fmt.Errorf("failed to get configurable product attributes: %w", err)
	}
	return attributes, nil
}

// GetAll lấy tất cả thuộc tính sản phẩm
func (r *ProductAttributeRepository) GetAll(tenantID uint) ([]*models.ProductAttribute, error) {
	var attributes []*models.ProductAttribute
	if err := r.gormDB.Where("tenant_id = ?", tenantID).Order("display_order ASC, name ASC").Find(&attributes).Error; err != nil {
		return nil, fmt.Errorf("failed to get all product attributes: %w", err)
	}
	return attributes, nil
}

// GetByGroupID lấy tất cả thuộc tính sản phẩm theo ID nhóm
func (r *ProductAttributeRepository) GetByGroupID(tenantID uint, groupID uint) ([]*models.ProductAttribute, error) {
	var attributes []*models.ProductAttribute
	if err := r.gormDB.Where("tenant_id = ? AND group_id = ?", tenantID, groupID).Order("display_order ASC, name ASC").Find(&attributes).Error; err != nil {
		return nil, fmt.Errorf("failed to get product attributes by group ID: %w", err)
	}
	return attributes, nil
}

// GetConfigurable lấy tất cả thuộc tính sản phẩm có thể cấu hình
func (r *ProductAttributeRepository) GetConfigurable(tenantID uint) ([]*models.ProductAttribute, error) {
	var attributes []*models.ProductAttribute
	if err := r.gormDB.Where("tenant_id = ? AND is_configurable = ?", tenantID, true).Order("display_order ASC, name ASC").Find(&attributes).Error; err != nil {
		return nil, fmt.Errorf("failed to get configurable product attributes: %w", err)
	}
	return attributes, nil
}

// GetFilterable lấy tất cả thuộc tính sản phẩm có thể lọc
func (r *ProductAttributeRepository) GetFilterable(tenantID uint) ([]*models.ProductAttribute, error) {
	var attributes []*models.ProductAttribute
	if err := r.gormDB.Where("tenant_id = ? AND is_filterable = ?", tenantID, true).Order("display_order ASC, name ASC").Find(&attributes).Error; err != nil {
		return nil, fmt.Errorf("failed to get filterable product attributes: %w", err)
	}
	return attributes, nil
}

// GetSearchable lấy tất cả thuộc tính sản phẩm có thể tìm kiếm
func (r *ProductAttributeRepository) GetSearchable(tenantID uint) ([]*models.ProductAttribute, error) {
	var attributes []*models.ProductAttribute
	if err := r.gormDB.Where("tenant_id = ? AND is_searchable = ?", tenantID, true).Order("display_order ASC, name ASC").Find(&attributes).Error; err != nil {
		return nil, fmt.Errorf("failed to get searchable product attributes: %w", err)
	}
	return attributes, nil
}
