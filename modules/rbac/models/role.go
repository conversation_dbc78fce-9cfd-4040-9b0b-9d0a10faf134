package models

import (
	"time"
)

// Role định nghĩa mô hình dữ liệu cho bảng roles
type Role struct {
	RoleID          uint      `gorm:"primaryKey;column:role_id" json:"role_id" db:"role_id"`
	RoleCode        string    `gorm:"column:role_code" json:"role_code" db:"role_code"`
	TenantID        *uint     `gorm:"column:tenant_id" json:"tenant_id" db:"tenant_id"`
	RoleName        string    `gorm:"column:role_name" json:"role_name" db:"role_name"`
	RoleDescription *string   `gorm:"column:role_description" json:"role_description" db:"role_description"`
	Status          string    `json:"status" gorm:"-"`
	CreatedBy       *uint     `gorm:"column:created_by" json:"created_by" db:"created_by"`
	CreatedAt       time.Time `gorm:"column:created_at" json:"created_at" db:"created_at"`
	UpdatedBy       *uint     `gorm:"column:updated_by" json:"updated_by" db:"updated_by"`
	UpdatedAt       time.Time `gorm:"column:updated_at" json:"updated_at" db:"updated_at"`

	// <PERSON><PERSON><PERSON> tr<PERSON> alias đ<PERSON> tư<PERSON>ng thích với code cũ
	Name        string `json:"-" db:"-" gorm:"-"` // Alias cho RoleName
	Description string `json:"-" db:"-" gorm:"-"` // Alias cho RoleDescription

	// Virtual fields
	Permissions []uint `db:"-" json:"permissions,omitempty"` // Permission IDs
}

// TableName specifies the table name for the Role model
func (Role) TableName() string {
	return "rbac_roles"
}
