package response

import "time"

type PermissionGroupResponse struct {
	GroupID                    uint      `json:"group_id"`
	TenantID                   *uint     `json:"tenant_id"`
	PermissionGroupName        string    `json:"permission_group_name"`
	PermissionGroupDescription *string   `json:"permission_group_description"`
	CreatedBy                  *uint     `json:"created_by"`
	CreatedAt                  time.Time `json:"created_at"`
	UpdatedBy                  *uint     `json:"updated_by"`
	UpdatedAt                  time.Time `json:"updated_at"`
}

type PermissionGroupListResponse struct {
	Groups     []*PermissionGroupResponse `json:"groups"`
	NextCursor string                     `json:"next_cursor,omitempty"`
	Has<PERSON><PERSON>    bool                       `json:"has_more"`
}
