package commands

import (
	"context"
	"fmt"
	"strings"

	"wnapi/internal/pkg/console"
	"wnapi/internal/pkg/pagination"
	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/dto/response"
	"wnapi/modules/rbac/service"

	"github.com/spf13/cobra"
)

// ListPermissionsCommand tạo command để liệt kê permissions trong hệ thống
type ListPermissionsCommand struct {
	permissionService service.PermissionService
}

// NewListPermissionsCommand khởi tạo command list permissions
func NewListPermissionsCommand(permissionService service.PermissionService) *cobra.Command {
	cmd := &ListPermissionsCommand{
		permissionService: permissionService,
	}

	cobraCmd := &cobra.Command{
		Use:   "list-permissions",
		Short: "Liệt kê tất cả permissions trong RBAC system",
		Long: `Hiển thị danh sách tất cả permissions đã được seed vào RBAC database.

Command này sẽ:
- <PERSON><PERSON><PERSON> tất cả permissions từ database
- Hi<PERSON><PERSON> thị thông tin chi tiết về từng permission
- Hỗ trợ filter theo module, group, hoặc pattern
- Hiển thị thống kê tổng quan`,
		Example: `  # Liệt kê tất cả permissions
  wnapi list-permissions

  # Liệt kê permissions của module cụ thể
  wnapi list-permissions --module=auth

  # Liệt kê permissions theo pattern
  wnapi list-permissions --pattern="*.create"

  # Liệt kê permissions của group cụ thể
  wnapi list-permissions --group-id=1`,
		RunE: cmd.run,
	}

	// Add flags
	cobraCmd.Flags().String("module", "", "Filter theo module cụ thể")
	cobraCmd.Flags().String("pattern", "", "Filter theo pattern (hỗ trợ wildcard *)")
	cobraCmd.Flags().Uint("group-id", 0, "Filter theo group ID")
	cobraCmd.Flags().Int("limit", 50, "Số lượng permissions tối đa hiển thị")
	cobraCmd.Flags().Bool("show-stats", true, "Hiển thị thống kê")

	return cobraCmd
}

// run thực thi command
func (c *ListPermissionsCommand) run(cmd *cobra.Command, args []string) error {
	// Lấy các flags
	moduleFilter, _ := cmd.Flags().GetString("module")
	patternFilter, _ := cmd.Flags().GetString("pattern")
	groupIDFilter, _ := cmd.Flags().GetUint("group-id")
	limit, _ := cmd.Flags().GetInt("limit")
	showStats, _ := cmd.Flags().GetBool("show-stats")

	console.Info("📋 Đang lấy danh sách permissions từ RBAC database...")

	// Tạo request để lấy permissions
	ctx := context.Background()
	req := request.ListPermissionRequest{
		Request: pagination.Request{
			Limit: limit,
		},
	}

	// Lấy permissions từ service
	resp, err := c.permissionService.ListPermissions(ctx, req)
	if err != nil {
		console.Error(fmt.Sprintf("Lỗi khi lấy danh sách permissions: %v", err))
		return err
	}

	if len(resp.Permissions) == 0 {
		console.Warning("Không tìm thấy permissions nào trong hệ thống")
		console.Info("💡 Sử dụng command 'seed-permissions' để seed permissions từ modules")
		return nil
	}

	// Filter permissions
	filteredPermissions := c.filterPermissions(resp.Permissions, moduleFilter, patternFilter, groupIDFilter)

	// Hiển thị thống kê
	if showStats {
		c.displayStats(resp.Permissions, filteredPermissions)
	}

	// Hiển thị danh sách permissions
	c.displayPermissions(filteredPermissions)

	return nil
}

// filterPermissions lọc permissions theo các criteria
func (c *ListPermissionsCommand) filterPermissions(permissions []response.PermissionResponse, moduleFilter, patternFilter string, groupIDFilter uint) []response.PermissionResponse {
	var filtered []response.PermissionResponse

	for _, perm := range permissions {
		// Filter theo module
		if moduleFilter != "" {
			parts := strings.Split(perm.PermissionCode, ".")
			if len(parts) > 0 && parts[0] != moduleFilter {
				continue
			}
		}

		// Filter theo group ID
		if groupIDFilter > 0 {
			if perm.GroupID == nil || *perm.GroupID != groupIDFilter {
				continue
			}
		}

		// Filter theo pattern
		if patternFilter != "" && !c.matchPattern(perm.PermissionCode, patternFilter) {
			continue
		}

		filtered = append(filtered, perm)
	}

	return filtered
}

// displayStats hiển thị thống kê permissions
func (c *ListPermissionsCommand) displayStats(allPermissions, filteredPermissions []response.PermissionResponse) {
	console.Info("📊 Thống kê Permissions:")
	console.Info(fmt.Sprintf("  ├─ Tổng số permissions: %d", len(allPermissions)))
	console.Info(fmt.Sprintf("  ├─ Permissions hiển thị: %d", len(filteredPermissions)))

	// Group by module
	moduleStats := make(map[string]int)
	for _, perm := range allPermissions {
		// Extract module from permission code
		parts := strings.Split(perm.PermissionCode, ".")
		module := "unknown"
		if len(parts) > 0 {
			module = parts[0]
		}
		moduleStats[module]++
	}

	console.Info("  └─ Phân bổ theo module:")
	for module, count := range moduleStats {
		console.Info(fmt.Sprintf("     ├─ %s: %d permissions", module, count))
	}
}

// displayPermissions hiển thị danh sách permissions
func (c *ListPermissionsCommand) displayPermissions(permissions []response.PermissionResponse) {
	console.Info("🔐 Danh sách Permissions:")

	if len(permissions) == 0 {
		console.Warning("Không có permissions nào phù hợp với filter")
		return
	}

	for i, perm := range permissions {
		groupName := "N/A"
		if perm.Group != nil {
			groupName = perm.Group.PermissionGroupName
		}

		description := "N/A"
		if perm.PermissionDescription != nil {
			description = *perm.PermissionDescription
		}

		console.Info(fmt.Sprintf("  %d. %s", i+1, perm.PermissionCode))
		console.Info(fmt.Sprintf("     ├─ Name: %s", perm.PermissionName))
		console.Info(fmt.Sprintf("     ├─ Description: %s", description))
		console.Info(fmt.Sprintf("     ├─ Group: %s", groupName))
		console.Info(fmt.Sprintf("     └─ Created: %s", perm.CreatedAt.Format("2006-01-02 15:04:05")))
	}
}

// matchPattern kiểm tra xem string có match với pattern không (hỗ trợ wildcard *)
func (c *ListPermissionsCommand) matchPattern(str, pattern string) bool {
	if pattern == "" {
		return true
	}

	// Simple wildcard matching
	if strings.Contains(pattern, "*") {
		parts := strings.Split(pattern, "*")
		if len(parts) == 2 {
			prefix := parts[0]
			suffix := parts[1]
			return strings.HasPrefix(str, prefix) && strings.HasSuffix(str, suffix)
		}
	}

	return strings.Contains(str, pattern)
}
