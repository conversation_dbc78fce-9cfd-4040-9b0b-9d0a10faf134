package handlers

import (
	"net/http"
	"strconv"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/service"

	"github.com/gin-gonic/gin"
)

// RoleHandler xử lý các HTTP request liên quan đến vai trò
type RoleHandler struct {
	roleService service.RoleService
}

// NewRoleHandler tạo một instance mới của RoleHandler
func NewRoleHandler(roleService service.RoleService) *RoleHandler {
	return &RoleHandler{
		roleService: roleService,
	}
}

// Create xử lý tạo mới vai trò
func (h *RoleHandler) Create(c *gin.Context) {
	// Parse request
	var req request.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Lấy tenant ID từ context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Gọi service
	role, err := h.roleService.CreateRole(c.Request.Context(), uint(tenantID), req)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi tạo vai trò", "CREATE_ROLE_FAILED", map[string]interface{}{
			"error":     err.Error(),
			"tenant_id": tenantID,
		})
		return
	}

	// Trả về kết quả thành công
	response.Success(c, role, nil)
}

// Get xử lý lấy thông tin vai trò theo ID
func (h *RoleHandler) Get(c *gin.Context) {
	// Parse role ID từ path
	roleID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Lấy tenant ID từ context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Gọi service
	role, err := h.roleService.GetRole(c.Request.Context(), tenantID, uint(roleID))
	if err != nil {
		handleRoleNotFound(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, role, nil)
}

// Update xử lý cập nhật vai trò
func (h *RoleHandler) Update(c *gin.Context) {
	// Parse role ID từ path
	roleID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Parse request
	var req request.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Lấy tenant ID từ context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Gọi service
	role, err := h.roleService.UpdateRole(c.Request.Context(), tenantID, uint(roleID), req)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi cập nhật vai trò", "UPDATE_ROLE_FAILED", map[string]interface{}{
			"error":     err.Error(),
			"tenant_id": tenantID,
			"role_id":   roleID,
		})
		return
	}

	// Trả về kết quả thành công
	response.Success(c, role, nil)
}

// Delete xử lý xóa vai trò
func (h *RoleHandler) Delete(c *gin.Context) {
	// Parse role ID từ path
	roleID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Lấy tenant ID từ context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Gọi service
	err = h.roleService.DeleteRole(c.Request.Context(), tenantID, uint(roleID))
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi xóa vai trò", "DELETE_ROLE_FAILED", map[string]interface{}{
			"error":     err.Error(),
			"tenant_id": tenantID,
			"role_id":   roleID,
		})
		return
	}

	// Trả về kết quả thành công
	response.Success(c, nil, nil)
}

// List xử lý lấy danh sách vai trò với phân trang
func (h *RoleHandler) List(c *gin.Context) {
	// Parse query parameters
	var req request.ListRoleRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Parse cursor từ token và set defaults
	if err := req.ParseCursor(); err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid cursor format", "INVALID_CURSOR", map[string]interface{}{
			"error": err.Error(),
		})
		return
	}
	req.SetDefaults()

	// Lấy tenant ID từ context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Gọi service
	result, err := h.roleService.ListRoles(c.Request.Context(), tenantID, req)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi lấy danh sách vai trò", "LIST_ROLES_FAILED", map[string]interface{}{
			"error":     err.Error(),
			"tenant_id": tenantID,
			"limit":     req.Limit,
			"cursor":    req.Cursor,
		})
		return
	}

	// Trả về kết quả thành công
	meta := &response.Meta{
		NextCursor: result.NextCursor,
		HasMore:    result.HasMore,
	}
	response.Success(c, result.Roles, meta)
}

// GetRolePermissions xử lý lấy danh sách quyền của vai trò
func (h *RoleHandler) GetRolePermissions(c *gin.Context) {
	// Parse role ID từ path
	roleID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Lấy tenant ID từ context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Gọi service để lấy thông tin vai trò (bao gồm danh sách quyền)
	role, err := h.roleService.GetRole(c.Request.Context(), tenantID, uint(roleID))
	if err != nil {
		handleRoleNotFound(c, err)
		return
	}

	// Trả về danh sách quyền
	response.Success(c, role.Permissions, nil)
}
