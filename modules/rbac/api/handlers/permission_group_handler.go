package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"wnapi/internal/pkg/response"
	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/service"

	"github.com/gin-gonic/gin"
)

type PermissionGroupHandler struct {
	service service.PermissionGroupAPIService
}

func NewPermissionGroupHandler(s service.PermissionGroupAPIService) *PermissionGroupHandler {
	return &PermissionGroupHandler{
		service: s,
	}
}

// List permission groups (cursor pagination)
func (h *PermissionGroupHandler) List(c *gin.Context) {
	var req request.ListPermissionGroupRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "Tham số truy vấn không hợp lệ", "INVALID_PERMISSION_GROUP_QUERY", nil)
		return
	}

	// Parse cursor và set defaults
	if err := req.ParseCursor(); err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid cursor format", "INVALID_CURSOR", map[string]interface{}{
			"error": err.Error(),
		})
		return
	}
	req.SetDefaults()

	result, err := h.service.ListPermissionGroups(c.Request.Context(), req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "List permission groups failed", "LIST_PERMISSION_GROUPS_FAILED")
		return
	}

	meta := &response.Meta{
		NextCursor: result.NextCursor,
		HasMore:    result.HasMore,
	}
	response.Success(c, result.Groups, meta)
}

// Create permission group
func (h *PermissionGroupHandler) Create(c *gin.Context) {
	var req request.CreatePermissionGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Dữ liệu nhóm quyền không hợp lệ", "INVALID_PERMISSION_GROUP_DATA", nil)
		return
	}
	group, err := h.service.CreatePermissionGroup(c.Request.Context(), req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Create permission group failed", "CREATE_PERMISSION_GROUP_FAILED")
		return
	}
	response.Success(c, group, nil)
}

// Get permission group by ID
func (h *PermissionGroupHandler) Get(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequest(c, "ID nhóm quyền không hợp lệ", "INVALID_PERMISSION_GROUP_ID", nil)
		return
	}
	group, err := h.service.GetPermissionGroup(c.Request.Context(), uint(id))
	if err != nil {
		response.NotFound(c, "Không tìm thấy nhóm quyền")
		return
	}
	response.Success(c, group, nil)
}

// Update permission group
func (h *PermissionGroupHandler) Update(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequest(c, "ID nhóm quyền không hợp lệ", "INVALID_PERMISSION_GROUP_ID", nil)
		return
	}
	var req request.UpdatePermissionGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Dữ liệu nhóm quyền không hợp lệ", "INVALID_PERMISSION_GROUP_DATA", nil)
		return
	}
	group, err := h.service.UpdatePermissionGroup(c.Request.Context(), uint(id), req)
	if err != nil {
		fmt.Println("Update permission group failed", err)
		response.Error(c, http.StatusInternalServerError, "Update permission group failed", "UPDATE_PERMISSION_GROUP_FAILED")
		return
	}
	response.Success(c, group, nil)
}

// Delete permission group
func (h *PermissionGroupHandler) Delete(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequest(c, "ID nhóm quyền không hợp lệ", "INVALID_PERMISSION_GROUP_ID", nil)
		return
	}
	if err := h.service.DeletePermissionGroup(c.Request.Context(), uint(id)); err != nil {
		response.Error(c, http.StatusInternalServerError, "Delete permission group failed", "DELETE_PERMISSION_GROUP_FAILED")
		return
	}
	response.Success(c, nil, nil)
}
