package handlers

import (
	"net/http"
	"strconv"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/service"

	"github.com/gin-gonic/gin"
)

// UserRoleHandler xử lý các HTTP request liên quan đến liên kết người dùng và vai trò
type UserRoleHandler struct {
	userRoleService service.UserRoleService
}

// NewUserRoleHandler tạo một instance mới của UserRoleHandler
func NewUserRoleHandler(userRoleService service.UserRoleService) *UserRoleHandler {
	return &UserRoleHandler{
		userRoleService: userRoleService,
	}
}

// AssignRole xử lý gán vai trò cho người dùng
func (h *UserRoleHandler) AssignRole(c *gin.Context) {
	// Parse request
	var req request.AssignUserRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "<PERSON><PERSON> liệu không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	// Lấy tenant ID từ context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Gọi service
	err := h.userRoleService.AssignRoleToUser(c.Request.Context(), tenantID, req)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi gán vai trò cho người dùng", "ASSIGN_USER_ROLE_FAILED", map[string]interface{}{
			"error":     err.Error(),
			"tenant_id": tenantID,
			"user_id":   req.UserID,
			"role_id":   req.RoleID,
		})
		return
	}

	// Trả về kết quả thành công
	response.Success(c, gin.H{
		"user_id": req.UserID,
		"role_id": req.RoleID,
	}, nil)
}

// RevokeRole xử lý thu hồi vai trò từ người dùng
func (h *UserRoleHandler) RevokeRole(c *gin.Context) {
	// Parse user ID và role ID từ path
	userID, err := strconv.Atoi(c.Param("user_id"))
	if err != nil {
		response.BadRequest(c, "User ID không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	roleID, err := strconv.Atoi(c.Param("role_id"))
	if err != nil {
		response.BadRequest(c, "Role ID không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	// Lấy tenant ID từ context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Gọi service
	err = h.userRoleService.RevokeRoleFromUser(c.Request.Context(), tenantID, uint(userID), uint(roleID))
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi thu hồi vai trò từ người dùng", "REVOKE_USER_ROLE_FAILED", map[string]interface{}{
			"error":     err.Error(),
			"tenant_id": tenantID,
			"user_id":   userID,
			"role_id":   roleID,
		})
		return
	}

	// Trả về kết quả thành công
	response.Success(c, nil, nil)
}

// GetUserRoles xử lý lấy danh sách vai trò của người dùng
func (h *UserRoleHandler) GetUserRoles(c *gin.Context) {
	// Parse user ID từ path
	userID, err := strconv.Atoi(c.Param("user_id"))
	if err != nil {
		response.BadRequest(c, "User ID không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	// Lấy tenant ID từ context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Gọi service
	roles, err := h.userRoleService.GetUserRoles(c.Request.Context(), uint(tenantID), uint(userID))
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi lấy danh sách vai trò của người dùng", "GET_USER_ROLES_FAILED", map[string]interface{}{
			"error":     err.Error(),
			"tenant_id": tenantID,
			"user_id":   userID,
		})
		return
	}

	// Trả về kết quả thành công
	response.Success(c, roles, nil)
}

// CheckUserRole xử lý kiểm tra người dùng có vai trò cụ thể không
func (h *UserRoleHandler) CheckUserRole(c *gin.Context) {
	// Parse user ID và role ID từ path
	userID, err := strconv.Atoi(c.Param("user_id"))
	if err != nil {
		response.BadRequest(c, "User ID không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	roleID, err := strconv.Atoi(c.Param("role_id"))
	if err != nil {
		response.BadRequest(c, "Role ID không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	// Lấy tenant ID từ context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Gọi service
	hasRole, err := h.userRoleService.HasRole(c.Request.Context(), tenantID, uint(userID), uint(roleID))
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi kiểm tra vai trò của người dùng", "CHECK_USER_ROLE_FAILED", map[string]interface{}{
			"error":     err.Error(),
			"tenant_id": tenantID,
			"user_id":   userID,
			"role_id":   roleID,
		})
		return
	}

	// Trả về kết quả thành công
	response.Success(c, gin.H{"has_role": hasRole}, nil)
}

// GetUsersWithRole xử lý lấy danh sách người dùng có vai trò cụ thể
func (h *UserRoleHandler) GetUsersWithRole(c *gin.Context) {
	// Parse role ID từ path
	roleID, err := strconv.Atoi(c.Param("role_id"))
	if err != nil {
		response.BadRequest(c, "Role ID không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	// Lấy tenant ID từ context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Gọi service
	userIDs, err := h.userRoleService.GetUsersWithRole(c.Request.Context(), tenantID, uint(roleID))
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi lấy danh sách người dùng có vai trò", "GET_USERS_WITH_ROLE_FAILED", map[string]interface{}{
			"error":     err.Error(),
			"tenant_id": tenantID,
			"role_id":   roleID,
		})
		return
	}

	// Trả về kết quả thành công
	response.Success(c, gin.H{"user_ids": userIDs}, nil)
}
