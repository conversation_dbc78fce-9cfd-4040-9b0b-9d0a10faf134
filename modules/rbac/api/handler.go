package api

import (
	"wnapi/internal/core"
	"wnapi/internal/middleware"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/rbac/api/handlers"
	"wnapi/modules/rbac/service"
	tenantService "wnapi/modules/tenant/service"

	"github.com/gin-gonic/gin"
)

// Handler là đối tượng chính xử lý API cho module RBAC
type Handler struct {
	permissionHandler      *handlers.PermissionHandler
	roleHandler            *handlers.RoleHandler
	userRoleHandler        *handlers.UserRoleHandler
	permissionGroupHandler *handlers.PermissionGroupHandler
	jwtService             *auth.JWTService
	tenantService          tenantService.TenantService
}

// NewHandler tạo một handler mới
func NewHandler(
	permissionService service.PermissionService,
	roleService service.RoleService,
	userRoleService service.UserRoleService,
	permissionGroupService service.PermissionGroupAPIService,
	jwtService *auth.JWTService,
	tenantService tenantService.TenantService,
) *Handler {
	return &Handler{
		permissionHandler:      handlers.NewPermissionHandler(permissionService),
		roleHandler:            handlers.NewRoleHandler(roleService),
		userRoleHandler:        handlers.NewUserRoleHandler(userRoleService),
		permissionGroupHandler: handlers.NewPermissionGroupHandler(permissionGroupService),
		jwtService:             jwtService,
		tenantService:          tenantService,
	}
}

// RegisterRoutes đăng ký tất cả routes cho module RBAC
func (h *Handler) RegisterRoutes(server *core.Server) error {
	// API Group
	apiGroup := server.Group("/api/admin/v1/rbac")

	// Thêm middleware tracing cho tất cả các route rbac
	apiGroup.Use(tracing.GinMiddleware("rbac"))

	// Health check endpoint
	apiGroup.GET("/healthy", h.healthCheck)

	// Áp dụng JWT middleware cho các protected routes
	protectedRoutes := apiGroup.Group("")
	protectedRoutes.Use(h.jwtService.JWTAuthMiddleware())

	// Permissions API
	permissionsGroup := protectedRoutes.Group("/permissions")
	{
		permissionsGroup.GET("", h.permissionHandler.List)
		permissionsGroup.POST("", h.permissionHandler.Create)
		permissionsGroup.GET("/:id", h.permissionHandler.Get)
		permissionsGroup.PUT("/:id", h.permissionHandler.Update)
		permissionsGroup.DELETE("/:id", h.permissionHandler.Delete)
	}

	// Roles API
	rolesGroup := protectedRoutes.Group("/roles")
	{
		rolesGroup.GET("", h.roleHandler.List)
		rolesGroup.POST("", h.roleHandler.Create)
		rolesGroup.GET("/:id", h.roleHandler.Get)
		rolesGroup.GET("/:id/permissions", h.roleHandler.GetRolePermissions)
		rolesGroup.PUT("/:id", h.roleHandler.Update)
		rolesGroup.DELETE("/:id", h.roleHandler.Delete)
	}

	// User Roles API
	userRolesGroup := protectedRoutes.Group("/user-roles")
	{
		userRolesGroup.POST("/assign", h.userRoleHandler.AssignRole)
		userRolesGroup.DELETE("/users/:user_id/roles/:role_id", h.userRoleHandler.RevokeRole)
		userRolesGroup.GET("/users/:user_id/roles", h.userRoleHandler.GetUserRoles)
		userRolesGroup.GET("/users/:user_id/roles/:role_id/check", h.userRoleHandler.CheckUserRole)
		userRolesGroup.GET("/roles/:role_id/users", h.userRoleHandler.GetUsersWithRole)
	}

	// Permission Groups API
	permissionGroupsGroup := protectedRoutes.Group("/permission-groups")
	{
		permissionGroupsGroup.GET("", h.permissionGroupHandler.List)
		permissionGroupsGroup.POST("", h.permissionGroupHandler.Create)
		permissionGroupsGroup.GET("/:id", h.permissionGroupHandler.Get)
		permissionGroupsGroup.PUT("/:id", h.permissionGroupHandler.Update)
		permissionGroupsGroup.DELETE("/:id", h.permissionGroupHandler.Delete)
	}

	return nil
}

// RegisterRoutesWithGin đăng ký tất cả routes cho module RBAC với gin.Engine
func (h *Handler) RegisterRoutesWithGin(engine *gin.Engine) error {
	// API Group
	apiGroup := engine.Group("/api/admin/v1/rbac")

	// Thêm middleware tracing cho tất cả các route rbac
	apiGroup.Use(tracing.GinMiddleware("rbac"))

	// Health check endpoint
	apiGroup.GET("/healthy", h.healthCheck)

	// Áp dụng JWT middleware cho các protected routes
	protectedRoutes := apiGroup.Group("")
	protectedRoutes.Use(
		h.jwtService.JWTAuthMiddleware(),
		middleware.TenantMiddleware(h.tenantService),
	)

	// Permissions API
	permissionsGroup := protectedRoutes.Group("/permissions")
	{
		permissionsGroup.GET("", h.permissionHandler.List)
		permissionsGroup.POST("", h.permissionHandler.Create)
		permissionsGroup.GET("/:id", h.permissionHandler.Get)
		permissionsGroup.PUT("/:id", h.permissionHandler.Update)
		permissionsGroup.DELETE("/:id", h.permissionHandler.Delete)
	}

	// Roles API
	rolesGroup := protectedRoutes.Group("/roles")
	{
		rolesGroup.GET("", h.roleHandler.List)
		rolesGroup.POST("", h.roleHandler.Create)
		rolesGroup.GET("/:id", h.roleHandler.Get)
		rolesGroup.GET("/:id/permissions", h.roleHandler.GetRolePermissions)
		rolesGroup.PUT("/:id", h.roleHandler.Update)
		rolesGroup.DELETE("/:id", h.roleHandler.Delete)
	}

	// User Roles API
	userRolesGroup := protectedRoutes.Group("/user-roles")
	{
		userRolesGroup.POST("/assign", h.userRoleHandler.AssignRole)
		userRolesGroup.DELETE("/users/:user_id/roles/:role_id", h.userRoleHandler.RevokeRole)
		userRolesGroup.GET("/users/:user_id/roles", h.userRoleHandler.GetUserRoles)
		userRolesGroup.GET("/users/:user_id/roles/:role_id/check", h.userRoleHandler.CheckUserRole)
		userRolesGroup.GET("/roles/:role_id/users", h.userRoleHandler.GetUsersWithRole)
	}

	// Permission Groups API
	permissionGroupsGroup := protectedRoutes.Group("/permission-groups")
	{
		permissionGroupsGroup.GET("", h.permissionGroupHandler.List)
		permissionGroupsGroup.POST("", h.permissionGroupHandler.Create)
		permissionGroupsGroup.GET("/:id", h.permissionGroupHandler.Get)
		permissionGroupsGroup.PUT("/:id", h.permissionGroupHandler.Update)
		permissionGroupsGroup.DELETE("/:id", h.permissionGroupHandler.Delete)
	}

	return nil
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status": "ok",
		"module": "rbac",
	})
}
