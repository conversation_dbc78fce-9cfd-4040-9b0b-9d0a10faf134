package mysql

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	"wnapi/internal/pkg/pagination"
	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/models"
	"wnapi/modules/rbac/repository"
)

// RoleRepository interface định nghĩa các phương thức thao tác với bảng roles
type RoleRepository interface {
	Create(ctx context.Context, role *models.Role) error
	Update(ctx context.Context, role *models.Role) error
	Delete(ctx context.Context, roleID uint) error
	FindByID(ctx context.Context, roleID uint) (*models.Role, error)
	FindAll(ctx context.Context) ([]*models.Role, error)
}

// RoleRepositoryImpl triển khai RoleRepository
type RoleRepositoryImpl struct {
	db *gorm.DB
}

// NewRoleRepository tạo một instance mới của RoleRepository
func NewRoleRepository(db *gorm.DB) repository.RoleRepository {
	return &RoleRepositoryImpl{
		db: db,
	}
}

// Create tạo một role mới
func (r *RoleRepositoryImpl) Create(ctx context.Context, tenantID uint, role *models.Role) error {
	// Gán tenantID cho role.TenantID (role.TenantID là con trỏ *uint)
	tmpTenantID := tenantID
	role.TenantID = &tmpTenantID

	result := r.db.WithContext(ctx).Create(role)
	return result.Error
}

// Update cập nhật role
func (r *RoleRepositoryImpl) Update(ctx context.Context, role *models.Role) error {
	result := r.db.WithContext(ctx).Save(role)
	return result.Error
}

// Delete xóa role
func (r *RoleRepositoryImpl) Delete(ctx context.Context, tenantID uint, roleID uint) error {
	result := r.db.WithContext(ctx).Where("role_id = ? AND tenant_id = ?", roleID, tenantID).Delete(&models.Role{})
	return result.Error
}

// GetByID tìm role theo ID
func (r *RoleRepositoryImpl) GetByID(ctx context.Context, tenantID uint, roleID uint) (*models.Role, error) {
	var role models.Role
	result := r.db.WithContext(ctx).Where("role_id = ? AND tenant_id = ?", roleID, tenantID).First(&role)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("role not found")
		}
		return nil, result.Error
	}
	return &role, nil
}

// GetByCode lấy role theo code
func (r *RoleRepositoryImpl) GetByCode(ctx context.Context, tenantID uint, code string) (*models.Role, error) {
	var role models.Role
	result := r.db.WithContext(ctx).Where("tenant_id = ? AND role_code = ?", tenantID, code).First(&role)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, result.Error
	}
	return &role, nil
}

// GetRolePermissions lấy danh sách quyền của vai trò
func (r *RoleRepositoryImpl) GetRolePermissions(ctx context.Context, roleID uint) ([]uint, error) {
	var permissionIDs []uint
	result := r.db.WithContext(ctx).Table("rbac_role_permissions").
		Where("role_id = ?", roleID).
		Pluck("permission_id", &permissionIDs)
	return permissionIDs, result.Error
}

// SetRolePermissions thiết lập quyền cho vai trò
func (r *RoleRepositoryImpl) SetRolePermissions(ctx context.Context, roleID uint, permissionIDs []uint, grantedBy *uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Lấy danh sách quyền hiện tại của vai trò
		var currentPermissions []uint
		if err := tx.Model(&models.RolePermission{}).
			Where("role_id = ?", roleID).
			Pluck("permission_id", &currentPermissions).Error; err != nil {
			return err
		}

		// Lọc bỏ các permission ID không hợp lệ (0 hoặc null)
		var validPermissionIDs []uint
		for _, permID := range permissionIDs {
			if permID > 0 {
				validPermissionIDs = append(validPermissionIDs, permID)
			}
		}

		// Tạo map để dễ dàng kiểm tra
		currentPermMap := make(map[uint]bool)
		for _, permID := range currentPermissions {
			if permID > 0 {
				currentPermMap[permID] = true
			}
		}

		newPermMap := make(map[uint]bool)
		for _, permID := range validPermissionIDs {
			newPermMap[permID] = true
		}

		// Xác định quyền cần xóa (có trong current nhưng không có trong new)
		var permissionsToDelete []uint
		for permID := range currentPermMap {
			if !newPermMap[permID] {
				permissionsToDelete = append(permissionsToDelete, permID)
			}
		}

		// Xác định quyền cần thêm (có trong new nhưng không có trong current)
		var permissionsToAdd []uint
		for permID := range newPermMap {
			if !currentPermMap[permID] {
				permissionsToAdd = append(permissionsToAdd, permID)
			}
		}

		// Xóa các quyền không còn được gán
		if len(permissionsToDelete) > 0 {
			if err := tx.Where("role_id = ? AND permission_id IN ?", roleID, permissionsToDelete).
				Delete(&models.RolePermission{}).Error; err != nil {
				return err
			}
		}

		// Thêm các quyền mới
		if len(permissionsToAdd) > 0 {
			var rolePermissions []models.RolePermission
			for _, permID := range permissionsToAdd {
				rolePermissions = append(rolePermissions, models.RolePermission{
					RoleID:       roleID,
					PermissionID: permID,
					CreatedBy:    grantedBy,
				})
			}
			if err := tx.Create(&rolePermissions).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// List lấy danh sách roles với phân trang cursor
func (r *RoleRepositoryImpl) List(ctx context.Context, tenantID uint, req request.ListRoleRequest) ([]*models.Role, string, bool, error) {
	var roles []*models.Role
	limit := 20
	if req.Limit > 0 {
		limit = req.Limit
	}

	query := r.db.WithContext(ctx).Table("rbac_roles").Where("tenant_id = ?", tenantID)

	// Nếu có cursor, thêm điều kiện để phân trang
	if req.Cursor != nil {
		if req.Cursor.Type == pagination.CursorTypeID {
			cursorID, err := req.Cursor.GetIDValue()
			if err != nil {
				return nil, "", false, fmt.Errorf("invalid cursor ID: %w", err)
			}
			if cursorID > 0 {
				query = query.Where("role_id > ?", cursorID)
			}
		}
	}

	// Thêm điều kiện tìm kiếm theo từ khóa nếu có
	if req.Search != "" {
		searchTerm := "%" + req.Search + "%"
		query = query.Where(
			"role_name LIKE ? OR role_code LIKE ? OR role_description LIKE ?",
			searchTerm, searchTerm, searchTerm,
		)
	}

	// Thực hiện query với limit + 1 để kiểm tra có phần tử tiếp theo không
	result := query.Order("role_id").Limit(limit + 1).Find(&roles)
	if result.Error != nil {
		return nil, "", false, result.Error
	}

	// Kiểm tra có phần tử tiếp theo không
	hasMore := false
	nextCursor := ""

	if len(roles) > limit {
		hasMore = true
		// Tạo cursor cho phần tử cuối cùng
		lastRole := roles[limit-1]
		cursor := pagination.IDCursor(lastRole.RoleID, "next")
		if encoded, err := cursor.Encode(); err == nil {
			nextCursor = encoded
		}
		roles = roles[:limit]
	}

	return roles, nextCursor, hasMore, nil
}

// GetDefaultRolesForTenant lấy danh sách vai trò mặc định cho tenant
func (r *RoleRepositoryImpl) GetDefaultRolesForTenant(ctx context.Context, tenantID uint) ([]*models.Role, error) {
	var roles []*models.Role
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND is_default = ?", tenantID, true).
		Find(&roles)
	return roles, result.Error
}
