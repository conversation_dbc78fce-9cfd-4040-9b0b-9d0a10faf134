package mysql

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	"wnapi/internal/pkg/pagination"
	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/models"
	"wnapi/modules/rbac/repository"
)

type permissionRepository struct {
	db *gorm.DB
}

// NewPermissionRepository tạo một instance mới của PermissionRepository
func NewPermissionRepository(db *gorm.DB) repository.PermissionRepository {
	return &permissionRepository{
		db: db,
	}
}

// Create tạo một permission mới
func (r *permissionRepository) Create(ctx context.Context, permission *models.Permission) error {
	result := r.db.WithContext(ctx).Create(permission)
	return result.Error
}

// GetByID lấy permission theo ID
func (r *permissionRepository) GetByID(ctx context.Context, permissionID uint) (*models.Permission, error) {
	var permission models.Permission
	result := r.db.WithContext(ctx).First(&permission, permissionID)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("permission not found")
		}
		return nil, result.Error
	}
	return &permission, nil
}

// Update cập nhật permission
func (r *permissionRepository) Update(ctx context.Context, permission *models.Permission) error {
	result := r.db.WithContext(ctx).Save(permission)
	return result.Error
}

// Delete xóa permission
func (r *permissionRepository) Delete(ctx context.Context, permissionID uint) error {
	result := r.db.WithContext(ctx).Delete(&models.Permission{}, permissionID)
	return result.Error
}

// List lấy danh sách permissions với phân trang cursor
func (r *permissionRepository) List(ctx context.Context, req request.ListPermissionRequest) ([]*models.Permission, string, bool, error) {
	var permissions []*models.Permission
	limit := 20
	if req.Limit > 0 {
		limit = req.Limit
	}

	query := r.db.WithContext(ctx).Model(&models.Permission{})

	// Nếu có cursor, thêm điều kiện để phân trang
	if req.Cursor != nil {
		if req.Cursor.Type == pagination.CursorTypeID {
			cursorID, err := req.Cursor.GetIDValue()
			if err != nil {
				return nil, "", false, fmt.Errorf("invalid cursor ID: %w", err)
			}
			query = query.Where("permission_id > ?", cursorID)
		}
	}

	// Thêm điều kiện lọc theo group_id nếu có
	if req.GroupID > 0 {
		query = query.Where("group_id = ?", req.GroupID)
	}

	// Thêm điều kiện lọc theo tenant_id nếu có
	if req.TenantID > 0 {
		query = query.Where("tenant_id = ? OR tenant_id IS NULL", req.TenantID)
	}

	// Thêm điều kiện tìm kiếm theo từ khóa nếu có
	if req.Search != "" {
		searchTerm := "%" + req.Search + "%"
		query = query.Where(
			"permission_name LIKE ? OR permission_code LIKE ? OR permission_description LIKE ?",
			searchTerm, searchTerm, searchTerm,
		)
	}

	// Thực hiện query với limit + 1 để kiểm tra có phần tử tiếp theo không
	result := query.Order("permission_id").Limit(limit + 1).Find(&permissions)
	if result.Error != nil {
		return nil, "", false, result.Error
	}

	// Kiểm tra có phần tử tiếp theo không
	hasMore := false
	nextCursor := ""

	if len(permissions) > limit {
		hasMore = true
		// Tạo cursor cho phần tử cuối cùng
		lastPermission := permissions[limit-1]
		cursor := pagination.IDCursor(lastPermission.PermissionID, "next")
		if encoded, err := cursor.Encode(); err == nil {
			nextCursor = encoded
		}
		permissions = permissions[:limit]
	}

	return permissions, nextCursor, hasMore, nil
}

// GetByCode lấy permission theo code
func (r *permissionRepository) GetByCode(ctx context.Context, code string) (*models.Permission, error) {
	var permission models.Permission
	result := r.db.WithContext(ctx).Where("permission_code = ?", code).First(&permission)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, result.Error
	}
	return &permission, nil
}
