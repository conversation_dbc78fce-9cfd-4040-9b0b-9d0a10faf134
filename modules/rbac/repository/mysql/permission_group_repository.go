package mysql

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"wnapi/internal/pkg/pagination"
	"wnapi/modules/rbac/models"
	"wnapi/modules/rbac/repository"
)

type permissionGroupRepository struct {
	db *gorm.DB
}

// NewPermissionGroupRepository tạo một instance mới của PermissionGroupRepository
func NewPermissionGroupRepository(db *gorm.DB) repository.PermissionGroupRepository {
	return &permissionGroupRepository{db: db}
}

// Create tạo một nhóm quyền mới
func (r *permissionGroupRepository) Create(ctx context.Context, group *models.PermissionGroup) error {
	return r.db.WithContext(ctx).Create(group).Error
}

// Update cập nhật thông tin nhóm quyền
func (r *permissionGroupRepository) Update(ctx context.Context, group *models.PermissionGroup) error {
	return r.db.WithContext(ctx).Model(&models.PermissionGroup{}).
		Where("group_id = ?", group.GroupID).
		Updates(map[string]interface{}{
			"permission_group_name":        group.PermissionGroupName,
			"permission_group_description": group.PermissionGroupDescription,
			"updated_by":                   group.UpdatedBy,
			"updated_at":                   group.UpdatedAt,
		}).Error
}

// Delete xóa nhóm quyền
func (r *permissionGroupRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.PermissionGroup{}, id).Error
}

// GetByID lấy thông tin nhóm quyền theo ID
func (r *permissionGroupRepository) GetByID(ctx context.Context, id uint) (*models.PermissionGroup, error) {
	var group models.PermissionGroup
	err := r.db.WithContext(ctx).Where("group_id = ?", id).First(&group).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &group, nil
}

// List lấy danh sách nhóm quyền với phân trang
func (r *permissionGroupRepository) List(ctx context.Context, tenantID *uint, limit int, cursor *uint) ([]*models.PermissionGroup, string, bool, error) {
	query := r.db.WithContext(ctx).Model(&models.PermissionGroup{})

	// Áp dụng các điều kiện lọc
	if tenantID != nil {
		query = query.Where("tenant_id = ?", *tenantID)
	}

	if cursor != nil {
		query = query.Where("group_id > ?", *cursor)
	}

	// Sắp xếp và giới hạn số lượng
	query = query.Order("group_id").Limit(limit + 1)

	// Thực hiện truy vấn
	var groups []*models.PermissionGroup
	if err := query.Find(&groups).Error; err != nil {
		return nil, "", false, err
	}

	// Kiểm tra có phần tử tiếp theo không
	hasMore := false
	if len(groups) > limit {
		hasMore = true
		groups = groups[:limit]
	}

	// Tạo cursor nếu có phần tử tiếp theo
	nextCursor := ""
	if hasMore && len(groups) > 0 {
		lastID := groups[len(groups)-1].GroupID
		cursor := pagination.IDCursor(lastID, "next")
		if encoded, err := cursor.Encode(); err == nil {
			nextCursor = encoded
		}
	}

	return groups, nextCursor, hasMore, nil
}
