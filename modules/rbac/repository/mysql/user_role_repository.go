package mysql

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"wnapi/modules/rbac/models"
	"wnapi/modules/rbac/repository"
)

type userRoleRepository struct {
	db *gorm.DB
}

// NewUserRoleRepository tạo một instance mới của UserRoleRepository
func NewUserRoleRepository(db *gorm.DB) repository.UserRoleRepository {
	return &userRoleRepository{
		db: db,
	}
}

// AssignRole gán vai trò cho người dùng
func (r *userRoleRepository) AssignRole(ctx context.Context, tenantID uint, userID uint, roleID uint, createdBy *uint) error {
	// Kiểm tra role có tồn tại trong tenant không
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Role{}).
		Where("role_id = ? AND tenant_id = ?", roleID, tenantID).
		Count(&count).Error; err != nil {
		return err
	}
	if count == 0 {
		return errors.New("role not found or not in specified tenant")
	}

	// Kiểm tra xem gán vai trò này đã tồn tại chưa
	if err := r.db.WithContext(ctx).Model(&models.UserRole{}).
		Where("user_id = ? AND role_id = ? AND tenant_id = ?", userID, roleID, tenantID).
		Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return nil
	}

	// Thêm mới
	userRole := models.UserRole{
		TenantID:  tenantID,
		UserID:    userID,
		RoleID:    roleID,
		CreatedBy: createdBy,
	}
	return r.db.WithContext(ctx).Create(&userRole).Error
}

// RevokeRole thu hồi vai trò từ người dùng
func (r *userRoleRepository) RevokeRole(ctx context.Context, tenantID uint, userID uint, roleID uint) error {
	result := r.db.WithContext(ctx).Where("user_id = ? AND role_id = ? AND tenant_id = ?", userID, roleID, tenantID).
		Delete(&models.UserRole{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("user does not have specified role in this tenant")
	}
	return nil
}

// GetUserRoles lấy danh sách vai trò của người dùng
func (r *userRoleRepository) GetUserRoles(ctx context.Context, tenantID uint, userID uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	result := r.db.WithContext(ctx).Where("tenant_id = ? AND user_id = ?", tenantID, userID).
		Find(&userRoles)
	return userRoles, result.Error
}

// HasRole kiểm tra người dùng có vai trò cụ thể không
func (r *userRoleRepository) HasRole(ctx context.Context, tenantID uint, userID uint, roleID uint) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.UserRole{}).
		Where("tenant_id = ? AND user_id = ? AND role_id = ?", tenantID, userID, roleID).
		Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetUsersWithRole lấy danh sách người dùng có vai trò cụ thể
func (r *userRoleRepository) GetUsersWithRole(ctx context.Context, tenantID uint, roleID uint) ([]uint, error) {
	var userIDs []uint
	result := r.db.WithContext(ctx).Table("rbac_user_roles").
		Where("tenant_id = ? AND role_id = ?", tenantID, roleID).
		Pluck("user_id", &userIDs)
	return userIDs, result.Error
}
