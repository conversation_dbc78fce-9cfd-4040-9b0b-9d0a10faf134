package service

import (
	"context"
	"wnapi/modules/tenant/dto"
	"wnapi/modules/tenant/internal"
	"wnapi/modules/tenant/models"
)

// TenantService định nghĩa interface cho tenant service
type TenantService interface {
	// CRUD operations
	Create(ctx context.Context, req dto.CreateTenantRequest) (*dto.CreateTenantResponse, error)
	GetByID(ctx context.Context, tenantID uint) (*dto.GetTenantResponse, error)
	GetByCode(ctx context.Context, tenantCode string) (*dto.GetTenantResponse, error)
	Update(ctx context.Context, tenantID uint, req dto.UpdateTenantRequest) (*dto.UpdateTenantResponse, error)
	Delete(ctx context.Context, tenantID uint) error

	// List operations
	List(ctx context.Context, req dto.ListTenantsRequest) (*dto.ListTenantsResponse, *internal.PaginationMeta, error)

	// Business operations
	ActivateTenant(ctx context.Context, tenantID uint) (*dto.ActivateTenantResponse, error)
	SuspendTenant(ctx context.Context, tenantID uint, req dto.SuspendTenantRequest) (*dto.SuspendTenantResponse, error)
	ExtendSubscription(ctx context.Context, tenantID uint, req dto.ExtendSubscriptionRequest) (*dto.ExtendSubscriptionResponse, error)
	ValidateTenantAccess(ctx context.Context, tenantCode string) (*dto.ValidateTenantAccessResponse, error)
	// Xác thực quyền truy cập của user vào tenant
	VerifyUserTenantAccess(ctx context.Context, userID, tenantID uint) (*models.Tenant, error)

	// Utility operations
	CheckTenantCodeAvailability(ctx context.Context, tenantCode string) (bool, error)
	GetExpiredTenants(ctx context.Context) ([]*dto.TenantSummary, error)
}
