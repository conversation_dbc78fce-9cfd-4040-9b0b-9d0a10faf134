package repository

import (
	"context"
	"wnapi/modules/tenant/models"
)

// TenantRepository định nghĩa interface cho tenant repository
type TenantRepository interface {
	// CRUD operations với GORM
	Create(ctx context.Context, tenant *models.Tenant) (*models.Tenant, error)
	GetByID(ctx context.Context, tenantID uint) (*models.Tenant, error)
	GetByCode(ctx context.Context, tenantCode string) (*models.Tenant, error)
	Update(ctx context.Context, tenant *models.Tenant) (*models.Tenant, error)
	Delete(ctx context.Context, tenantID uint) error

	// List operations với raw SQL và cursor-based pagination
	List(ctx context.Context, limit int, cursor string) ([]*models.Tenant, string, bool, error)
	ListByStatus(ctx context.Context, status models.TenantStatus, limit int, cursor string) ([]*models.Tenant, string, bool, error)

	// Business operations
	ExistsByCode(ctx context.Context, tenantCode string) (bool, error)
	CountByStatus(ctx context.Context, status models.TenantStatus) (int64, error)
	GetExpiredTenants(ctx context.Context) ([]*models.Tenant, error)
	// Kiểm tra user có thuộc tenant không
	IsUserMemberOfTenant(ctx context.Context, userID, tenantID uint) (bool, error)

	// Batch operations
	UpdateStatus(ctx context.Context, tenantIDs []uint, status models.TenantStatus) error
	BulkDelete(ctx context.Context, tenantIDs []uint) error
}

// NewTenantRepository tạo repository implementation
func NewTenantRepository(db interface{}, logger interface{}) (TenantRepository, error) {
	// Implementation sẽ được thực hiện trong mysql package
	return nil, nil
}
