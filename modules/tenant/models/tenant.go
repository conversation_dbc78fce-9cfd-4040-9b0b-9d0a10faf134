package models

import (
	"time"
)

// TenantStatus định nghĩa các trạng thái của tenant
type TenantStatus string

const (
	TenantStatusActive    TenantStatus = "active"
	TenantStatusInactive  TenantStatus = "inactive"
	TenantStatusSuspended TenantStatus = "suspended"
	TenantStatusTrial     TenantStatus = "trial"
)

// TenantType định nghĩa loại tenant
type TenantType string

const (
	TenantTypeIndividual TenantType = "individual"
	TenantTypeCompany    TenantType = "company"
)

// IsValid kiểm tra tính hợp lệ của tenant status
func (s TenantStatus) IsValid() bool {
	switch s {
	case TenantStatusActive, TenantStatusInactive, TenantStatusSuspended, TenantStatusTrial:
		return true
	default:
		return false
	}
}

// String trả về string representation của status
func (s TenantStatus) String() string {
	return string(s)
}

// IsValid kiểm tra tính hợ<PERSON> lệ của tenant type
func (t TenantType) IsValid() bool {
	switch t {
	case TenantTypeIndividual, TenantTypeCompany:
		return true
	default:
		return false
	}
}

// String trả về string representation của type
func (t TenantType) String() string {
	return string(t)
}

// Tenant model mapping với database table
type Tenant struct {
	TenantID              uint         `gorm:"column:tenant_id;primaryKey;autoIncrement" json:"tenant_id"`
	TenantName            string       `gorm:"column:tenant_name;not null" json:"tenant_name"`
	TenantCode            string       `gorm:"column:tenant_code;uniqueIndex;not null" json:"tenant_code"`
	TenantType            TenantType   `gorm:"column:tenant_type;default:individual" json:"tenant_type"`
	CompanyName           *string      `gorm:"column:company_name" json:"company_name,omitempty"`
	TaxCode               *string      `gorm:"column:tax_code" json:"tax_code,omitempty"`
	LegalRepresentative   *string      `gorm:"column:legal_representative" json:"legal_representative,omitempty"`
	CompanyAddress        *string      `gorm:"column:company_address" json:"company_address,omitempty"`
	CompanyPhone          *string      `gorm:"column:company_phone" json:"company_phone,omitempty"`
	CompanyEmail          *string      `gorm:"column:company_email" json:"company_email,omitempty"`
	Status                TenantStatus `gorm:"column:status;default:active" json:"status"`
	PlanType              string       `gorm:"column:plan_type;default:standard" json:"plan_type"`
	SubscriptionExpiresAt *time.Time   `gorm:"column:subscription_expires_at" json:"subscription_expires_at"`
	CreatedAt             time.Time    `gorm:"column:created_at" json:"created_at"`
	UpdatedAt             time.Time    `gorm:"column:updated_at" json:"updated_at"`
}

// TableName trả về tên table trong database
func (Tenant) TableName() string {
	return "tenants"
}

// IsActive kiểm tra tenant có đang active không
func (t *Tenant) IsActive() bool {
	return t.Status == TenantStatusActive
}

// IsExpired kiểm tra subscription có hết hạn không
func (t *Tenant) IsExpired() bool {
	if t.SubscriptionExpiresAt == nil {
		return false
	}
	return t.SubscriptionExpiresAt.Before(time.Now())
}

// CanAccess kiểm tra tenant có thể truy cập hệ thống không
func (t *Tenant) CanAccess() bool {
	return t.IsActive() && !t.IsExpired()
}

// IsIndividual kiểm tra tenant có phải cá nhân không
func (t *Tenant) IsIndividual() bool {
	return t.TenantType == TenantTypeIndividual
}

// IsCompany kiểm tra tenant có phải công ty không
func (t *Tenant) IsCompany() bool {
	return t.TenantType == TenantTypeCompany
}

// HasCompanyInfo kiểm tra tenant có thông tin công ty đầy đủ không
func (t *Tenant) HasCompanyInfo() bool {
	if !t.IsCompany() {
		return false
	}
	return t.CompanyName != nil && *t.CompanyName != "" &&
		t.TaxCode != nil && *t.TaxCode != "" &&
		t.LegalRepresentative != nil && *t.LegalRepresentative != ""
}

// GetDisplayName trả về tên hiển thị phù hợp
func (t *Tenant) GetDisplayName() string {
	return t.TenantName
}

// GetLegalName trả về tên pháp lý (cho hóa đơn)
func (t *Tenant) GetLegalName() string {
	if t.IsCompany() && t.CompanyName != nil {
		return *t.CompanyName
	}
	return t.TenantName
}
