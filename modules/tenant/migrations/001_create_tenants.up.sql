CREATE TABLE IF NOT EXISTS tenants (
  tenant_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tenant_name VARCHAR(255) NOT NULL COMMENT 'Display name for tenant (individual name or company display name)',
  tenant_code VARCHAR(50) NOT NULL UNIQUE COMMENT 'Unique code for the tenant, useful for API and URL paths',
  tenant_type ENUM('individual', 'company') NOT NULL DEFAULT 'individual' COMMENT 'Type of tenant: individual or company',
  company_name VARCHAR(255) NULL COMMENT 'Official legal company name for invoices and legal documents',
  tax_code VARCHAR(50) NULL COMMENT 'Tax code for company type tenant',
  legal_representative VARCHAR(255) NULL COMMENT 'Legal representative for company type tenant',
  company_address TEXT NULL COMMENT 'Company address for company type tenant',
  company_phone VARCHAR(20) NULL COMMENT 'Company phone for company type tenant',
  company_email VARCHAR(255) NULL COMMENT 'Company email for company type tenant',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  status ENUM('active', 'inactive', 'suspended', 'trial') DEFAULT 'active',
  plan_type VARCHAR(50) DEFAULT 'standard' COMMENT 'Subscription plan type',
  subscription_expires_at TIMESTAMP NULL COMMENT 'When the current subscription expires',
  INDEX idx_tenants_status (status),
  INDEX idx_tenants_code (tenant_code),
  INDEX idx_tenants_type (tenant_type),
  INDEX idx_tenants_tax_code (tax_code)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 