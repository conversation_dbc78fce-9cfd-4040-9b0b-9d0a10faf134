package request

import (
	"time"
	"wnapi/modules/marketing/models"
)

// CreateAdsBannerRequest represents the request to create an ads banner
type CreateAdsBannerRequest struct {
	Name         string     `json:"name" binding:"required,min=1,max=255" example:"Summer Sale Banner"`
	Type         string     `json:"type" binding:"required,min=1,max=50" example:"image"`
	Position     string     `json:"position" binding:"required,min=1,max=100" example:"header"`
	Image        *string    `json:"image,omitempty" binding:"omitempty,max=255" example:"/uploads/banner.jpg"`
	Link         *string    `json:"link,omitempty" binding:"omitempty,max=500" example:"https://example.com/sale"`
	Content      *string    `json:"content,omitempty" example:"<h1>Summer Sale 50% Off</h1>"`
	OpenInNewTab bool       `json:"open_in_new_tab" example:"true"`
	Platform     string     `json:"platform" binding:"required,oneof=web mobile all" example:"web"`
	FromDate     *time.Time `json:"from_date,omitempty" example:"2024-01-01T00:00:00Z"`
	ToDate       *time.Time `json:"to_date,omitempty" example:"2024-12-31T23:59:59Z"`
	SortOrder    int        `json:"sort_order" binding:"min=0" example:"1"`
	Status       string     `json:"status" binding:"required,oneof=pending active inactive expired" example:"pending"`
}

// UpdateAdsBannerRequest represents the request to update an ads banner
type UpdateAdsBannerRequest struct {
	Name         *string    `json:"name,omitempty" binding:"omitempty,min=1,max=255" example:"Winter Sale Banner"`
	Type         *string    `json:"type,omitempty" binding:"omitempty,min=1,max=50" example:"video"`
	Position     *string    `json:"position,omitempty" binding:"omitempty,min=1,max=100" example:"sidebar"`
	Image        *string    `json:"image,omitempty" binding:"omitempty,max=255" example:"/uploads/new-banner.jpg"`
	Link         *string    `json:"link,omitempty" binding:"omitempty,max=500" example:"https://example.com/winter-sale"`
	Content      *string    `json:"content,omitempty" example:"<h1>Winter Sale 30% Off</h1>"`
	OpenInNewTab *bool      `json:"open_in_new_tab,omitempty" example:"false"`
	Platform     *string    `json:"platform,omitempty" binding:"omitempty,oneof=web mobile all" example:"mobile"`
	FromDate     *time.Time `json:"from_date,omitempty" example:"2024-02-01T00:00:00Z"`
	ToDate       *time.Time `json:"to_date,omitempty" example:"2024-02-29T23:59:59Z"`
	SortOrder    *int       `json:"sort_order,omitempty" binding:"omitempty,min=0" example:"2"`
	Status       *string    `json:"status,omitempty" binding:"omitempty,oneof=pending active inactive expired" example:"active"`
}

// ListAdsBannersRequest represents the request to list ads banners
type ListAdsBannersRequest struct {
	Cursor   string  `form:"cursor" binding:"omitempty" example:""`
	Limit    int     `form:"limit" binding:"omitempty,min=1,max=100" example:"10"`
	Status   *string `form:"status" binding:"omitempty,oneof=pending active inactive expired" example:"active"`
	Platform *string `form:"platform" binding:"omitempty,oneof=web mobile all" example:"web"`
	Position *string `form:"position" binding:"omitempty,max=100" example:"header"`
	Type     *string `form:"type" binding:"omitempty,max=50" example:"image"`
	Search   *string `form:"search" binding:"omitempty,max=255" example:"sale"`
	SortBy   *string `form:"sort_by" binding:"omitempty,oneof=name type position platform sort_order created_at updated_at" example:"sort_order"`
	SortDesc *bool   `form:"sort_desc" example:"false"`
}

// ToModel converts CreateAdsBannerRequest to AdsBanner model
func (r *CreateAdsBannerRequest) ToModel(tenantID, userID uint) *models.AdsBanner {
	return &models.AdsBanner{
		TenantID:     tenantID,
		Name:         r.Name,
		Type:         r.Type,
		Position:     r.Position,
		Image:        r.Image,
		Link:         r.Link,
		Content:      r.Content,
		OpenInNewTab: r.OpenInNewTab,
		Platform:     r.Platform,
		FromDate:     r.FromDate,
		ToDate:       r.ToDate,
		SortOrder:    r.SortOrder,
		Status:       r.Status,
		CreatedBy:    &userID,
	}
}