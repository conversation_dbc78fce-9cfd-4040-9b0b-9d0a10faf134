package request

import (
	"wnapi/modules/marketing/models"
)

// CreateAdsPositionRequest represents the request to create an ads position
type CreateAdsPositionRequest struct {
	Name        string  `json:"name" binding:"required,min=1,max=255" example:"Header Banner"`
	Code        string  `json:"code" binding:"required,min=1,max=255" example:"header_banner"`
	Description *string `json:"description,omitempty" binding:"omitempty,max=500" example:"Banner position at the top of the page"`
	Status      string  `json:"status" binding:"required,oneof=active inactive" example:"active"`
}

// UpdateAdsPositionRequest represents the request to update an ads position
type UpdateAdsPositionRequest struct {
	Name        *string `json:"name,omitempty" binding:"omitempty,min=1,max=255" example:"Header Banner Updated"`
	Code        *string `json:"code,omitempty" binding:"omitempty,min=1,max=255" example:"header_banner_v2"`
	Description *string `json:"description,omitempty" binding:"omitempty,max=500" example:"Updated banner position description"`
	Status      *string `json:"status,omitempty" binding:"omitempty,oneof=active inactive" example:"inactive"`
}

// ListAdsPositionsRequest represents the request to list ads positions
type ListAdsPositionsRequest struct {
	Cursor   string  `form:"cursor" binding:"omitempty" example:""`
	Limit    int     `form:"limit" binding:"omitempty,min=1,max=100" example:"10"`
	Status   *string `form:"status" binding:"omitempty,oneof=active inactive" example:"active"`
	Search   *string `form:"search" binding:"omitempty,max=255" example:"header"`
	SortBy   *string `form:"sort_by" binding:"omitempty,oneof=name code created_at updated_at" example:"created_at"`
	SortDesc *bool   `form:"sort_desc" example:"true"`
}

// ToModel converts CreateAdsPositionRequest to AdsPosition model
func (r *CreateAdsPositionRequest) ToModel(tenantID, userID uint) *models.AdsPosition {
	return &models.AdsPosition{
		TenantID:    tenantID,
		Name:        r.Name,
		Code:        r.Code,
		Description: r.Description,
		Status:      r.Status,
		CreatedBy:   &userID,
	}
}