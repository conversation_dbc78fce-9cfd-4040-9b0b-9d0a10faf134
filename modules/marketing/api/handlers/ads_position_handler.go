package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/response"
	"wnapi/modules/marketing/dto/request"
	"wnapi/modules/marketing/service"
)

// AdsPositionHandler handles ads position related HTTP requests
type AdsPositionHandler struct {
	adsPositionService service.AdsPositionService
	logger             logger.Logger
}

// NewAdsPositionHandler creates a new ads position handler
func NewAdsPositionHandler(
	adsPositionService service.AdsPositionService,
	logger logger.Logger,
) *AdsPositionHandler {
	return &AdsPositionHandler{
		adsPositionService: adsPositionService,
		logger:             logger,
	}
}

// CreateAdsPosition godoc
// @Summary Create a new ads position
// @Description Create a new ads position with the provided information
// @Tags Ads Positions
// @Accept json
// @Produce json
// @Param request body request.CreateAdsPositionRequest true "Create ads position request"
// @Success 201 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/marketing/ads-positions [post]
// @Security BearerAuth
func (h *AdsPositionHandler) CreateAdsPosition(c *gin.Context) {
	var req request.CreateAdsPositionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", "error", err)
		response.BadRequest(c, "Invalid request body", "INVALID_REQUEST_BODY", nil)
		return
	}

	// Get tenant and user info from context
	tenantID := auth.GetTenantID(c)
	userID := auth.GetUserID(c)

	// Create ads position
	var createdBy uint
	if userID != nil {
		createdBy = *userID
	}
	result, err := h.adsPositionService.CreateAdsPosition(c.Request.Context(), tenantID, createdBy, &req)
	if err != nil {
		h.logger.Error("Failed to create ads position", "error", err)
		response.InternalServerError(c, "Failed to create ads position")
		return
	}

	response.Created(c, result, nil)
}

// GetAdsPosition godoc
// @Summary Get an ads position by ID
// @Description Retrieve an ads position by its ID
// @Tags Ads Positions
// @Accept json
// @Produce json
// @Param id path int true "Ads Position ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/marketing/ads-positions/{id} [get]
// @Security BearerAuth
func (h *AdsPositionHandler) GetAdsPosition(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid ads position ID", "id", idStr, "error", err)
		response.BadRequest(c, "Invalid ads position ID", "INVALID_ID", nil)
		return
	}

	// Get tenant info from context
	tenantID := auth.GetTenantID(c)

	// Get ads position
	result, err := h.adsPositionService.GetAdsPosition(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		h.logger.Error("Failed to get ads position", "id", id, "error", err)
		if err.Error() == "ads position not found" {
			response.NotFound(c, "Ads position not found")
		} else {
			response.InternalServerError(c, "Failed to get ads position")
		}
		return
	}

	response.Success(c, result, nil)
}

// UpdateAdsPosition godoc
// @Summary Update an ads position
// @Description Update an existing ads position with the provided information
// @Tags Ads Positions
// @Accept json
// @Produce json
// @Param id path int true "Ads Position ID"
// @Param request body request.UpdateAdsPositionRequest true "Update ads position request"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/marketing/ads-positions/{id} [put]
// @Security BearerAuth
func (h *AdsPositionHandler) UpdateAdsPosition(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid ads position ID", "id", idStr, "error", err)
		response.BadRequest(c, "Invalid ads position ID", "INVALID_ID", nil)
		return
	}

	var req request.UpdateAdsPositionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", "error", err)
		response.BadRequest(c, "Invalid request body", "INVALID_REQUEST_BODY", nil)
		return
	}

	// Get tenant and user info from context
	tenantID := auth.GetTenantID(c)
	userID := auth.GetUserID(c)

	// Update ads position
	var updatedBy uint
	if userID != nil {
		updatedBy = *userID
	}
	result, err := h.adsPositionService.UpdateAdsPosition(c.Request.Context(), tenantID, uint(id), updatedBy, &req)
	if err != nil {
		h.logger.Error("Failed to update ads position", "id", id, "error", err)
		if err.Error() == "ads position not found" {
			response.NotFound(c, "Ads position not found")
		} else {
			response.InternalServerError(c, "Failed to update ads position")
		}
		return
	}

	response.Success(c, result, nil)
}

// DeleteAdsPosition godoc
// @Summary Delete an ads position
// @Description Delete an existing ads position by ID
// @Tags Ads Positions
// @Accept json
// @Produce json
// @Param id path int true "Ads Position ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/marketing/ads-positions/{id} [delete]
// @Security BearerAuth
func (h *AdsPositionHandler) DeleteAdsPosition(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid ads position ID", "id", idStr, "error", err)
		response.BadRequest(c, "Invalid ads position ID", "INVALID_ID", nil)
		return
	}

	// Get tenant info from context
	tenantID := auth.GetTenantID(c)

	// Delete ads position
	err = h.adsPositionService.DeleteAdsPosition(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		h.logger.Error("Failed to delete ads position", "id", id, "error", err)
		if err.Error() == "ads position not found" {
			response.NotFound(c, "Ads position not found")
		} else {
			response.InternalServerError(c, "Failed to delete ads position")
		}
		return
	}

	response.Success(c, nil, nil)
}

// ListAdsPositions godoc
// @Summary List ads positions
// @Description Retrieve a list of ads positions with cursor-based pagination and filters
// @Tags Ads Positions
// @Accept json
// @Produce json
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Items per page" default(10)
// @Param status query string false "Filter by status" Enums(active, inactive)
// @Param search query string false "Search by name, code, or description"
// @Param sort_by query string false "Sort by field" Enums(name, code, created_at, updated_at)
// @Param sort_desc query bool false "Sort in descending order" default(true)
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/marketing/ads-positions [get]
// @Security BearerAuth
func (h *AdsPositionHandler) ListAdsPositions(c *gin.Context) {
	var req request.ListAdsPositionsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("Invalid query parameters", "error", err)
		response.BadRequest(c, "Invalid query parameters", "INVALID_QUERY_PARAMS", nil)
		return
	}

	// Get tenant info from context
	tenantID := auth.GetTenantID(c)

	// List ads positions
	result, err := h.adsPositionService.ListAdsPositions(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.logger.Error("Failed to list ads positions", "error", err)
		response.InternalServerError(c, "Failed to list ads positions")
		return
	}

	// Use cursor meta from service result
	meta := &response.Meta{
		NextCursor: result.Meta.NextCursor,
		HasMore:    result.Meta.HasMore,
	}

	response.Success(c, result.Data, meta)
}

// GetActivePositions godoc
// @Summary Get active ads positions
// @Description Retrieve all active ads positions
// @Tags Ads Positions
// @Accept json
// @Produce json
// @Success 200 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/marketing/ads-positions/active [get]
// @Security BearerAuth
func (h *AdsPositionHandler) GetActivePositions(c *gin.Context) {
	// Get tenant info from context
	tenantID := auth.GetTenantID(c)

	// Get active positions
	result, err := h.adsPositionService.GetActivePositions(c.Request.Context(), tenantID)
	if err != nil {
		h.logger.Error("Failed to get active ads positions", "error", err)
		response.InternalServerError(c, "Failed to get active ads positions")
		return
	}

	response.Success(c, result, nil)
}