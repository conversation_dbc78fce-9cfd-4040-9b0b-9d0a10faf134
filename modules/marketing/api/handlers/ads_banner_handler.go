package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/response"
	"wnapi/modules/marketing/dto/request"
	marketingResponse "wnapi/modules/marketing/dto/response"
	"wnapi/modules/marketing/service"
)

// AdsBannerHandler handles ads banner related HTTP requests
type AdsBannerHandler struct {
	adsBannerService service.AdsBannerService
	logger           logger.Logger
}

// NewAdsBannerHandler creates a new ads banner handler
func NewAdsBannerHandler(adsBannerService service.AdsBannerService, logger logger.Logger) *AdsBannerHandler {
	return &AdsBannerHandler{
		adsBannerService: adsBannerService,
		logger:           logger,
	}
}

// CreateAdsBanner godoc
// @Summary Create a new ads banner
// @Description Create a new ads banner with the provided information
// @Tags ads-banners
// @Accept json
// @Produce json
// @Param request body request.CreateAdsBannerRequest true "Create ads banner request"
// @Success 201 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ads-banners [post]
// @Security BearerAuth
func (h *AdsBannerHandler) CreateAdsBanner(c *gin.Context) {
	var req request.CreateAdsBannerRequest

	// Bind and validate request
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", "error", err)
		response.BadRequest(c, "Invalid request body", "INVALID_REQUEST_BODY", nil)
		return
	}

	// Get tenant and user info from context
	tenantID := auth.GetTenantID(c)
	userID := auth.GetUserID(c)

	// Create ads banner
	var createdBy uint
	if userID != nil {
		createdBy = *userID
	}
	result, err := h.adsBannerService.CreateAdsBanner(c.Request.Context(), tenantID, createdBy, &req)
	if err != nil {
		h.logger.Error("Failed to create ads banner", "error", err)
		response.InternalServerError(c, "Failed to create ads banner")
		return
	}

	response.Created(c, result, nil)
}

// GetAdsBanner godoc
// @Summary Get an ads banner by ID
// @Description Retrieve an ads banner by its ID
// @Tags ads-banners
// @Accept json
// @Produce json
// @Param id path int true "Ads Banner ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ads-banners/{id} [get]
// @Security BearerAuth
func (h *AdsBannerHandler) GetAdsBanner(c *gin.Context) {
	// Parse ID from URL parameter
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid ads banner ID", "id", idStr, "error", err)
		response.BadRequest(c, "Invalid ads banner ID", "INVALID_ID", nil)
		return
	}

	// Get tenant info from context
	tenantID := auth.GetTenantID(c)

	// Get ads banner
	result, err := h.adsBannerService.GetAdsBanner(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		h.logger.Error("Failed to get ads banner", "id", id, "error", err)
		if err.Error() == "ads banner not found" {
			response.NotFound(c, "Ads banner not found")
		} else {
			response.InternalServerError(c, "Failed to get ads banner")
		}
		return
	}

	response.Success(c, result, nil)
}

// UpdateAdsBanner godoc
// @Summary Update an ads banner
// @Description Update an existing ads banner with the provided information
// @Tags ads-banners
// @Accept json
// @Produce json
// @Param id path int true "Ads Banner ID"
// @Param request body request.UpdateAdsBannerRequest true "Update ads banner request"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ads-banners/{id} [put]
// @Security BearerAuth
func (h *AdsBannerHandler) UpdateAdsBanner(c *gin.Context) {
	// Parse ID from URL parameter
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid ads banner ID", "id", idStr, "error", err)
		response.BadRequest(c, "Invalid ads banner ID", "INVALID_ID", nil)
		return
	}

	var req request.UpdateAdsBannerRequest

	// Bind and validate request
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", "error", err)
		response.BadRequest(c, "Invalid request body", "INVALID_REQUEST_BODY", nil)
		return
	}

	// Get tenant and user info from context
	tenantID := auth.GetTenantID(c)
	userID := auth.GetUserID(c)

	// Update ads banner
	var updatedBy uint
	if userID != nil {
		updatedBy = *userID
	}
	result, err := h.adsBannerService.UpdateAdsBanner(c.Request.Context(), tenantID, updatedBy, uint(id), &req)
	if err != nil {
		h.logger.Error("Failed to update ads banner", "id", id, "error", err)
		if err.Error() == "ads banner not found" {
			response.NotFound(c, "Ads banner not found")
		} else {
			response.InternalServerError(c, "Failed to update ads banner")
		}
		return
	}

	response.Success(c, result, nil)
}

// DeleteAdsBanner godoc
// @Summary Delete an ads banner
// @Description Delete an ads banner by its ID
// @Tags ads-banners
// @Accept json
// @Produce json
// @Param id path int true "Ads Banner ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ads-banners/{id} [delete]
// @Security BearerAuth
func (h *AdsBannerHandler) DeleteAdsBanner(c *gin.Context) {
	// Parse ID from URL parameter
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid ads banner ID", "id", idStr, "error", err)
		response.BadRequest(c, "Invalid ads banner ID", "INVALID_ID", nil)
		return
	}

	// Get tenant info from context
	tenantID := auth.GetTenantID(c)

	// Delete ads banner
	err = h.adsBannerService.DeleteAdsBanner(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		h.logger.Error("Failed to delete ads banner", "id", id, "error", err)
		if err.Error() == "ads banner not found" {
			response.NotFound(c, "Ads banner not found")
		} else {
			response.InternalServerError(c, "Failed to delete ads banner")
		}
		return
	}

	response.Success(c, nil, nil)
}

// ListAdsBanners godoc
// @Summary List ads banners with cursor pagination
// @Description Retrieve a list of ads banners with cursor pagination and optional filters
// @Tags ads-banners
// @Accept json
// @Produce json
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Items per page" default(10)
// @Param status query string false "Filter by status"
// @Param position query string false "Filter by position"
// @Param platform query string false "Filter by platform"
// @Param search query string false "Search term"
// @Param sort_by query string false "Sort field" default("created_at")
// @Param sort_desc query bool false "Sort descending" default(false)
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ads-banners [get]
// @Security BearerAuth
func (h *AdsBannerHandler) ListAdsBanners(c *gin.Context) {
	var req request.ListAdsBannersRequest

	// Bind and validate query parameters
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("Invalid query parameters", "error", err)
		response.BadRequest(c, "Invalid query parameters", "INVALID_QUERY_PARAMS", nil)
		return
	}

	// Get tenant info from context
	tenantID := auth.GetTenantID(c)

	// List ads banners
	result, err := h.adsBannerService.ListAdsBanners(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.logger.Error("Failed to list ads banners", "error", err)
		response.InternalServerError(c, "Failed to list ads banners")
		return
	}

	// Use cursor meta from service response
	meta := &response.Meta{
		NextCursor: result.Meta.NextCursor,
		HasMore:    result.Meta.HasMore,
	}

	response.Success(c, result.Data, meta)
}

// GetActiveBanners godoc
// @Summary Get active ads banners
// @Description Retrieve all active ads banners for a specific position and platform
// @Tags ads-banners
// @Accept json
// @Produce json
// @Param position query string true "Position code"
// @Param platform query string false "Platform (web, mobile, all)" default("web")
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ads-banners/active [get]
// @Security BearerAuth
func (h *AdsBannerHandler) GetActiveBanners(c *gin.Context) {
	position := c.Query("position")
	platform := c.DefaultQuery("platform", "web")

	if position == "" {
		response.BadRequest(c, "Position is required", "MISSING_POSITION", nil)
		return
	}

	// Get tenant info from context
	tenantID := auth.GetTenantID(c)

	// Get active banners
	result, err := h.adsBannerService.GetActiveBanners(c.Request.Context(), tenantID, position, platform)
	if err != nil {
		h.logger.Error("Failed to get active banners", "error", err)
		response.InternalServerError(c, "Failed to get active banners")
		return
	}

	response.Success(c, result, nil)
}

// GetBannersByPosition godoc
// @Summary Get banners by position
// @Description Retrieve all banners for a specific position
// @Tags ads-banners
// @Accept json
// @Produce json
// @Param position query string true "Position code"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ads-banners/by-position [get]
// @Security BearerAuth
func (h *AdsBannerHandler) GetBannersByPosition(c *gin.Context) {
	position := c.Query("position")

	if position == "" {
		response.BadRequest(c, "Position is required", "MISSING_POSITION", nil)
		return
	}

	// Get tenant info from context
	tenantID := auth.GetTenantID(c)

	// Get banners by position
	result, err := h.adsBannerService.GetBannersByPosition(c.Request.Context(), tenantID, position)
	if err != nil {
		h.logger.Error("Failed to get banners by position", "error", err)
		response.InternalServerError(c, "Failed to get banners by position")
		return
	}

	response.Success(c, result, nil)
}

// UpdateBannerStatus godoc
// @Summary Update banner status
// @Description Update the status of an ads banner
// @Tags ads-banners
// @Accept json
// @Produce json
// @Param id path int true "Ads Banner ID"
// @Param request body request.UpdateAdsBannerRequest true "Update status request"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ads-banners/{id}/status [patch]
// @Security BearerAuth
func (h *AdsBannerHandler) UpdateBannerStatus(c *gin.Context) {
	// Parse ID from URL parameter
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		h.logger.Error("Invalid ads banner ID", "id", idStr, "error", err)
		response.BadRequest(c, "Invalid ads banner ID", "INVALID_ID", nil)
		return
	}

	var req request.UpdateAdsBannerRequest

	// Bind and validate request
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", "error", err)
		response.BadRequest(c, "Invalid request body", "INVALID_REQUEST_BODY", nil)
		return
	}

	// Get tenant info from context
	tenantID := auth.GetTenantID(c)

	// Update banner status
	var status string
	if req.Status != nil {
		status = *req.Status
	}
	err = h.adsBannerService.UpdateBannerStatus(c.Request.Context(), tenantID, uint(id), status)
	if err != nil {
		h.logger.Error("Failed to update banner status", "id", id, "error", err)
		if err.Error() == "ads banner not found" {
			response.NotFound(c, "Ads banner not found")
		} else {
			response.InternalServerError(c, "Failed to update banner status")
		}
		return
	}

	response.Success(c, nil, nil)
}

// GetPublicBannersByPosition godoc
// @Summary Get public banners by position (Public API)
// @Description Retrieve active banners for a specific position - public endpoint
// @Tags public
// @Accept json
// @Produce json
// @Param position path string true "Position code"
// @Param platform query string false "Platform (web, mobile, all)" default("web")
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/public/marketing/banners/{position} [get]
func (h *AdsBannerHandler) GetPublicBannersByPosition(c *gin.Context) {
	position := c.Param("position")
	platform := c.DefaultQuery("platform", "web")

	if position == "" {
		response.BadRequest(c, "Position is required", "MISSING_POSITION", nil)
		return
	}

	// For public API, we might need to get tenant from subdomain or other means
	// For now, assuming a default tenant or getting from context
	tenantID := auth.GetTenantID(c)

	// Get active banners
	result, err := h.adsBannerService.GetActiveBanners(c.Request.Context(), tenantID, position, platform)
	if err != nil {
		h.logger.Error("Failed to get public banners", "error", err)
		response.InternalServerError(c, "Failed to get banners")
		return
	}

	// Convert to public response format
	publicBanners := make([]marketingResponse.AdsBannerPublicResponse, len(result))
	for i, banner := range result {
		publicBanners[i] = marketingResponse.AdsBannerPublicResponse{
			ID:           banner.ID,
			Name:         banner.Name,
			Type:         banner.Type,
			Position:     banner.Position,
			Image:        banner.Image,
			Link:         banner.Link,
			Content:      banner.Content,
			OpenInNewTab: banner.OpenInNewTab,
			Platform:     banner.Platform,
			SortOrder:    banner.SortOrder,
		}
	}

	// Use proper response format with data and meta fields
	response.Success(c, publicBanners, nil)
}