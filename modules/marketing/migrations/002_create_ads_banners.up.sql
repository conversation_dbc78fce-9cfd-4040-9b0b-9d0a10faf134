CREATE TABLE ads_banners (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    position VARCHAR(100) NOT NULL,
    image VARCHAR(255),
    link VARCHAR(500),
    content TEXT,
    open_in_new_tab BOOLEAN DEFAULT FALSE,
    platform VARCHAR(50) NOT NULL,
    from_date TIMESTAMP NULL,
    to_date TIMESTAMP NULL,
    sort_order INT DEFAULT 0,
    status ENUM('pending','active', 'inactive','expired') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT UNSIGNED,
    updated_by INT UNSIGNED,
    INDEX idx_ads_banner_status (status),
    INDEX idx_ads_banner_tenant (tenant_id),
    INDEX idx_ads_banner_position (position),
    INDEX idx_ads_banner_platform (platform),
    INDEX idx_ads_banner_dates (from_date, to_date),
    INDEX idx_ads_banner_sort (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;