package request

// CreateSeoMetaRequest chứa thông tin để tạo SEO meta mới
type CreateSeoMetaRequest struct {
	TableID            uint   `json:"table_id" binding:"required"`
	TableName          string `json:"table_name" binding:"required"`
	MetaTitle          string `json:"meta_title"`
	MetaDescription    string `json:"meta_description"`
	Keywords           string `json:"keywords"`
	CanonicalUrl       string `json:"canonical_url"`
	OgTitle            string `json:"og_title"`
	OgDescription      string `json:"og_description"`
	OgImage            string `json:"og_image"`
	RobotsIndex        bool   `json:"robots_index"`
	RobotsFollow       bool   `json:"robots_follow"`
	RobotsAdvanced     string `json:"robots_advanced"`
	SeoScore           uint8  `json:"seo_score"`
	ReadabilityScore   uint8  `json:"readability_score"`
	SchemaData         string `json:"schema_data"`
}

// UpdateSeoMetaRequest chứa thông tin để cập nhật SEO meta
type UpdateSeoMetaRequest struct {
	MetaTitle          string `json:"meta_title"`
	MetaDescription    string `json:"meta_description"`
	Keywords           string `json:"keywords"`
	CanonicalUrl       string `json:"canonical_url"`
	OgTitle            string `json:"og_title"`
	OgDescription      string `json:"og_description"`
	OgImage            string `json:"og_image"`
	RobotsIndex        bool   `json:"robots_index"`
	RobotsFollow       bool   `json:"robots_follow"`
	RobotsAdvanced     string `json:"robots_advanced"`
	SeoScore           uint8  `json:"seo_score"`
	ReadabilityScore   uint8  `json:"readability_score"`
	SchemaData         string `json:"schema_data"`
}

// GetSeoMetaByTableRequest chứa thông tin để lấy SEO meta theo table
type GetSeoMetaByTableRequest struct {
	TableID   uint   `form:"table_id" binding:"required"`
	TableName string `form:"table_name" binding:"required"`
}

// DeleteSeoMetaByTableRequest chứa thông tin để xóa SEO meta theo table
type DeleteSeoMetaByTableRequest struct {
	TableID   uint   `form:"table_id" binding:"required"`
	TableName string `form:"table_name" binding:"required"`
}

// UpsertSeoMetaRequest chứa thông tin để tạo hoặc cập nhật SEO meta
type UpsertSeoMetaRequest struct {
	TableID            uint   `json:"table_id" binding:"required"`
	TableName          string `json:"table_name" binding:"required"`
	MetaTitle          string `json:"meta_title"`
	MetaDescription    string `json:"meta_description"`
	Keywords           string `json:"keywords"`
	CanonicalUrl       string `json:"canonical_url"`
	OgTitle            string `json:"og_title"`
	OgDescription      string `json:"og_description"`
	OgImage            string `json:"og_image"`
	RobotsIndex        bool   `json:"robots_index"`
	RobotsFollow       bool   `json:"robots_follow"`
	RobotsAdvanced     string `json:"robots_advanced"`
	SeoScore           uint8  `json:"seo_score"`
	ReadabilityScore   uint8  `json:"readability_score"`
	SchemaData         string `json:"schema_data"`
}
