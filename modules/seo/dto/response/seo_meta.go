package response

import (
	"time"

	"wnapi/modules/seo/models"
)

// SeoMetaResponse chứa thông tin trả về cho SEO meta
type SeoMetaResponse struct {
	MetaID           uint      `json:"meta_id"`
	TableID          uint      `json:"table_id"`
	TableName        string    `json:"table_name"`
	MetaTitle        string    `json:"meta_title"`
	MetaDescription  string    `json:"meta_description"`
	Keywords         string    `json:"keywords"`
	CanonicalUrl     string    `json:"canonical_url"`
	OgTitle          string    `json:"og_title"`
	OgDescription    string    `json:"og_description"`
	OgImage          string    `json:"og_image"`
	RobotsIndex      bool      `json:"robots_index"`
	RobotsFollow     bool      `json:"robots_follow"`
	RobotsAdvanced   string    `json:"robots_advanced"`
	SeoScore         uint8     `json:"seo_score"`
	ReadabilityScore uint8     `json:"readability_score"`
	SchemaData       string    `json:"schema_data"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// FromModel chuyển đổi từ model sang response
func FromModel(model models.SeoMeta) *SeoMetaResponse {
	return &SeoMetaResponse{
		MetaID:          model.MetaID,
		TableID:         model.TableID,
		TableName:       model.TableNameStr,
		MetaTitle:       model.MetaTitle,
		MetaDescription: model.MetaDescription,
		Keywords:        model.Keywords,
		CanonicalUrl:    model.CanonicalUrl,
		OgTitle:         model.OgTitle,
		OgDescription:   model.OgDescription,
		OgImage:         model.OgImage,

		RobotsIndex:      model.RobotsIndex,
		RobotsFollow:     model.RobotsFollow,
		RobotsAdvanced:   model.RobotsAdvanced,
		SeoScore:         model.SeoScore,
		ReadabilityScore: model.ReadabilityScore,
		SchemaData:       model.SchemaData,
		CreatedAt:        model.CreatedAt,
		UpdatedAt:        model.UpdatedAt,
	}
}

// FromModelList chuyển đổi danh sách model SeoMeta thành danh sách SeoMetaResponse
func FromModelList(metas []models.SeoMeta) []SeoMetaResponse {
	result := make([]SeoMetaResponse, len(metas))
	for i, meta := range metas {
		result[i] = *FromModel(meta)
	}
	return result
}
