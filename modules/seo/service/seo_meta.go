package service

import (
	"context"

	"go.opentelemetry.io/otel/attribute"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/seo/dto/request"
	"wnapi/modules/seo/dto/response"
	"wnapi/modules/seo/internal"
	"wnapi/modules/seo/models"
)

// Service là alias cho interface Service trong internal, để tránh import internal từ module khác
// Đảm bảo các module khác chỉ import từ service
//go:generate mockgen -destination=../mocks/seo_service_mock.go -package=mocks wnapi/modules/seo/service Service

type Service = internal.Service

// SeoMetaService triển khai Service interface
type SeoMetaService struct {
	repo   internal.Repository
	logger logger.Logger
}

// NewSeoMetaService tạo một service mới
func NewSeoMetaService(repo internal.Repository, logger logger.Logger) internal.Service {
	return &SeoMetaService{
		repo:   repo,
		logger: logger,
	}
}

// GetSeoMetaByID lấy thông tin SEO Meta theo ID
func (s *SeoMetaService) GetSeoMetaByID(ctx context.Context, id uint) (*response.SeoMetaResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "seo-service", "seo.GetSeoMetaByID")
	defer span.End()

	// Add relevant attributes to the span
	span.SetAttributes(
		attribute.String("seo.operation", "get_meta_by_id"),
		attribute.Int64("seo.meta_id", int64(id)),
	)

	// Lấy dữ liệu từ repository
	seoMeta, err := s.repo.GetSeoMetaByID(ctx, id)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error.operation", "get_seo_meta_by_id"),
			attribute.Int64("seo.meta_id", int64(id)),
		)
		return nil, err
	}

	// Chuyển đổi sang response
	result := response.FromModel(*seoMeta)
	return result, nil
}

// GetSeoMetaByTable lấy thông tin SEO Meta theo table_id và table_name
func (s *SeoMetaService) GetSeoMetaByTable(ctx context.Context, req request.GetSeoMetaByTableRequest) (*response.SeoMetaResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "seo-service", "seo.GetSeoMetaByTable")
	defer span.End()

	// Add relevant attributes to the span
	span.SetAttributes(
		attribute.String("seo.operation", "get_meta_by_table"),
		attribute.Int64("seo.table_id", int64(req.TableID)),
		attribute.String("seo.table_name", req.TableName),
	)

	// Lấy dữ liệu từ repository
	seoMeta, err := s.repo.GetSeoMetaByTable(ctx, req.TableName, req.TableID)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error.operation", "get_seo_meta_by_table"),
			attribute.Int64("seo.table_id", int64(req.TableID)),
			attribute.String("seo.table_name", req.TableName),
		)
		return nil, err
	}

	// Chuyển đổi sang response
	result := response.FromModel(*seoMeta)
	return result, nil
}

// CreateSeoMeta tạo mới SEO Meta
func (s *SeoMetaService) CreateSeoMeta(ctx context.Context, req request.CreateSeoMetaRequest) (*response.SeoMetaResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "seo-service", "seo.CreateSeoMeta")
	defer span.End()

	// Add relevant attributes to the span
	span.SetAttributes(
		attribute.String("seo.operation", "create_meta"),
		attribute.Int64("seo.table_id", int64(req.TableID)),
		attribute.String("seo.table_name", req.TableName),
	)

	// Chuyển đổi từ request sang model
	seoMeta := &models.SeoMeta{
		TableID:         req.TableID,
		TableNameStr:    req.TableName,
		MetaTitle:       req.MetaTitle,
		MetaDescription: req.MetaDescription,
		Keywords:        req.Keywords,
		CanonicalUrl:    req.CanonicalUrl,
		OgTitle:         req.OgTitle,
		OgDescription:   req.OgDescription,
		OgImage:         req.OgImage,

		RobotsIndex:      req.RobotsIndex,
		RobotsFollow:     req.RobotsFollow,
		RobotsAdvanced:   req.RobotsAdvanced,
		SeoScore:         req.SeoScore,
		ReadabilityScore: req.ReadabilityScore,
		SchemaData:       req.SchemaData,
	}

	// Lưu vào repository
	err := s.repo.CreateSeoMeta(ctx, seoMeta)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error.operation", "create_seo_meta"),
			attribute.Int64("seo.table_id", int64(req.TableID)),
			attribute.String("seo.table_name", req.TableName),
		)
		return nil, err
	}

	// Record SEO performance metrics
	span.SetAttributes(
		attribute.Int64("seo.meta_id", int64(seoMeta.MetaID)),
		attribute.Int64("seo.seo_score", int64(req.SeoScore)),
		attribute.Int64("seo.readability_score", int64(req.ReadabilityScore)),
	)

	// Lấy dữ liệu đã lưu
	createdMeta, err := s.repo.GetSeoMetaByID(ctx, seoMeta.MetaID)
	if err != nil {
		s.logger.Error("Không thể lấy SeoMeta đã tạo: %v", err)
		tracing.RecordError(ctx, err,
			attribute.String("error.operation", "get_created_seo_meta"),
			attribute.Int64("seo.meta_id", int64(seoMeta.MetaID)),
		)
		return nil, internal.ErrDatabase
	}

	// Chuyển đổi sang response
	result := response.FromModel(*createdMeta)
	return result, nil
}

// UpdateSeoMeta cập nhật SEO Meta
func (s *SeoMetaService) UpdateSeoMeta(ctx context.Context, id uint, req request.UpdateSeoMetaRequest) (*response.SeoMetaResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "seo-service", "seo.UpdateSeoMeta")
	defer span.End()

	// Add relevant attributes to the span
	span.SetAttributes(
		attribute.String("seo.operation", "update_meta"),
		attribute.Int64("seo.meta_id", int64(id)),
	)

	// Lấy dữ liệu hiện tại
	existingMeta, err := s.repo.GetSeoMetaByID(ctx, id)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error.operation", "get_existing_seo_meta"),
			attribute.Int64("seo.meta_id", int64(id)),
		)
		return nil, err
	}

	// Cập nhật các trường
	existingMeta.MetaTitle = req.MetaTitle
	existingMeta.MetaDescription = req.MetaDescription
	existingMeta.Keywords = req.Keywords
	existingMeta.CanonicalUrl = req.CanonicalUrl
	existingMeta.OgTitle = req.OgTitle
	existingMeta.OgDescription = req.OgDescription
	existingMeta.OgImage = req.OgImage

	existingMeta.RobotsIndex = req.RobotsIndex
	existingMeta.RobotsFollow = req.RobotsFollow
	existingMeta.RobotsAdvanced = req.RobotsAdvanced
	existingMeta.SeoScore = req.SeoScore
	existingMeta.ReadabilityScore = req.ReadabilityScore
	existingMeta.SchemaData = req.SchemaData

	// Record SEO performance metrics
	span.SetAttributes(
		attribute.Int64("seo.table_id", int64(existingMeta.TableID)),
		attribute.String("seo.table_name", existingMeta.TableNameStr),
		attribute.Int64("seo.seo_score", int64(req.SeoScore)),
		attribute.Int64("seo.readability_score", int64(req.ReadabilityScore)),
	)

	// Lưu vào repository
	err = s.repo.UpdateSeoMeta(ctx, existingMeta)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error.operation", "update_seo_meta"),
			attribute.Int64("seo.meta_id", int64(id)),
		)
		return nil, err
	}

	// Chuyển đổi sang response
	result := response.FromModel(*existingMeta)
	return result, nil
}

// DeleteSeoMeta xóa SEO Meta theo ID
func (s *SeoMetaService) DeleteSeoMeta(ctx context.Context, id uint) error {
	ctx, span := tracing.StartSpan(ctx, "seo-service", "seo.DeleteSeoMeta")
	defer span.End()

	// Add relevant attributes to the span
	span.SetAttributes(
		attribute.String("seo.operation", "delete_meta"),
		attribute.Int64("seo.meta_id", int64(id)),
	)

	// Xóa từ repository
	err := s.repo.DeleteSeoMeta(ctx, id)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error.operation", "delete_seo_meta"),
			attribute.Int64("seo.meta_id", int64(id)),
		)
		return err
	}

	return nil
}

// DeleteSeoMetaByTable xóa SEO Meta theo table_id và table_name
func (s *SeoMetaService) DeleteSeoMetaByTable(ctx context.Context, req request.DeleteSeoMetaByTableRequest) error {
	ctx, span := tracing.StartSpan(ctx, "seo-service", "seo.DeleteSeoMetaByTable")
	defer span.End()

	// Add relevant attributes to the span
	span.SetAttributes(
		attribute.String("seo.operation", "delete_meta_by_table"),
		attribute.Int64("seo.table_id", int64(req.TableID)),
		attribute.String("seo.table_name", req.TableName),
	)

	// Xóa từ repository
	err := s.repo.DeleteSeoMetaByTable(ctx, req.TableName, req.TableID)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error.operation", "delete_seo_meta_by_table"),
			attribute.Int64("seo.table_id", int64(req.TableID)),
			attribute.String("seo.table_name", req.TableName),
		)
		return err
	}

	return nil
}

// CreateOrUpdateSeoMeta tạo mới hoặc cập nhật SEO Meta
func (s *SeoMetaService) CreateOrUpdateSeoMeta(ctx context.Context, req request.UpsertSeoMetaRequest) (*response.SeoMetaResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "seo-service", "seo.CreateOrUpdateSeoMeta")
	defer span.End()

	// Add relevant attributes to the span
	span.SetAttributes(
		attribute.String("seo.operation", "upsert_meta"),
		attribute.Int64("seo.table_id", int64(req.TableID)),
		attribute.String("seo.table_name", req.TableName),
	)

	// Kiểm tra xem đã tồn tại chưa
	existingMeta, err := s.repo.GetSeoMetaByTable(ctx, req.TableName, req.TableID)
	if err != nil {
		// Nếu không tìm thấy, tạo mới
		if err == internal.ErrSeoMetaNotFound {
			span.SetAttributes(attribute.String("seo.upsert_action", "create"))
			createReq := request.CreateSeoMetaRequest{
				TableID:         req.TableID,
				TableName:       req.TableName,
				MetaTitle:       req.MetaTitle,
				MetaDescription: req.MetaDescription,
				Keywords:        req.Keywords,
				CanonicalUrl:    req.CanonicalUrl,
				OgTitle:         req.OgTitle,
				OgDescription:   req.OgDescription,
				OgImage:         req.OgImage,

				RobotsIndex:      req.RobotsIndex,
				RobotsFollow:     req.RobotsFollow,
				RobotsAdvanced:   req.RobotsAdvanced,
				SeoScore:         req.SeoScore,
				ReadabilityScore: req.ReadabilityScore,
				SchemaData:       req.SchemaData,
			}
			return s.CreateSeoMeta(ctx, createReq)
		}
		tracing.RecordError(ctx, err,
			attribute.String("error.operation", "get_seo_meta_for_upsert"),
			attribute.Int64("seo.table_id", int64(req.TableID)),
			attribute.String("seo.table_name", req.TableName),
		)
		return nil, err
	}

	// Nếu tìm thấy, cập nhật
	span.SetAttributes(
		attribute.String("seo.upsert_action", "update"),
		attribute.Int64("seo.existing_meta_id", int64(existingMeta.MetaID)),
	)
	updateReq := request.UpdateSeoMetaRequest{
		MetaTitle:       req.MetaTitle,
		MetaDescription: req.MetaDescription,
		Keywords:        req.Keywords,
		CanonicalUrl:    req.CanonicalUrl,
		OgTitle:         req.OgTitle,
		OgDescription:   req.OgDescription,
		OgImage:         req.OgImage,

		RobotsIndex:      req.RobotsIndex,
		RobotsFollow:     req.RobotsFollow,
		RobotsAdvanced:   req.RobotsAdvanced,
		SeoScore:         req.SeoScore,
		ReadabilityScore: req.ReadabilityScore,
		SchemaData:       req.SchemaData,
	}
	return s.UpdateSeoMeta(ctx, existingMeta.MetaID, updateReq)
}
