package mysql

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	"wnapi/modules/seo/internal"
	"wnapi/modules/seo/models"
)

// SeoMetaRepository triển khai Repository interface sử dụng GORM
type SeoMetaRepository struct {
	db *gorm.DB
}

// NewSeoMetaRepository tạo một repository mới
func NewSeoMetaRepository(db *gorm.DB) internal.Repository {
	return &SeoMetaRepository{
		db: db,
	}
}

// GetSeoMetaByID lấy thông tin SEO Meta theo ID
func (r *SeoMetaRepository) GetSeoMetaByID(ctx context.Context, id uint) (*models.SeoMeta, error) {
	var seoMeta models.SeoMeta
	result := r.db.WithContext(ctx).Where("meta_id = ?", id).First(&seoMeta)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrSeoMetaNotFound
		}
		return nil, fmt.Errorf("failed to get seo meta: %w", result.Error)
	}

	return &seoMeta, nil
}

// GetSeoMetaByTable lấy thông tin SEO Meta theo table_id và table_name
func (r *SeoMetaRepository) GetSeoMetaByTable(ctx context.Context, tableName string, tableID uint) (*models.SeoMeta, error) {
	var seoMeta models.SeoMeta
	result := r.db.WithContext(ctx).Where("table_name = ? AND table_id = ?", tableName, tableID).First(&seoMeta)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrSeoMetaNotFound
		}
		return nil, fmt.Errorf("failed to get seo meta: %w", result.Error)
	}

	return &seoMeta, nil
}

// CreateSeoMeta tạo mới SEO Meta
func (r *SeoMetaRepository) CreateSeoMeta(ctx context.Context, seoMeta *models.SeoMeta) error {
	// Kiểm tra xem đã tồn tại chưa
	var count int64
	r.db.WithContext(ctx).Model(&models.SeoMeta{}).Where("table_name = ? AND table_id = ?", seoMeta.TableNameStr, seoMeta.TableID).Count(&count)
	if count > 0 {
		return internal.ErrDuplicateSeoMeta
	}

	result := r.db.WithContext(ctx).Create(seoMeta)
	if result.Error != nil {
		return fmt.Errorf("failed to create seo meta: %w", result.Error)
	}

	return nil
}

// UpdateSeoMeta cập nhật SEO Meta
func (r *SeoMetaRepository) UpdateSeoMeta(ctx context.Context, seoMeta *models.SeoMeta) error {
	result := r.db.WithContext(ctx).Save(seoMeta)
	if result.Error != nil {
		return fmt.Errorf("failed to update seo meta: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return internal.ErrSeoMetaNotFound
	}

	return nil
}

// DeleteSeoMeta xóa SEO Meta theo ID
func (r *SeoMetaRepository) DeleteSeoMeta(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Delete(&models.SeoMeta{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete seo meta: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return internal.ErrSeoMetaNotFound
	}

	return nil
}

// DeleteSeoMetaByTable xóa SEO Meta theo table_id và table_name
func (r *SeoMetaRepository) DeleteSeoMetaByTable(ctx context.Context, tableName string, tableID uint) error {
	result := r.db.WithContext(ctx).Where("table_name = ? AND table_id = ?", tableName, tableID).Delete(&models.SeoMeta{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete seo meta: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return internal.ErrSeoMetaNotFound
	}

	return nil
}
