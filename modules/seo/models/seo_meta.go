package models

import (
	"time"
)

// SeoMeta là model cho bảng seo_meta
type SeoMeta struct {
	MetaID             uint      `gorm:"primaryKey;column:meta_id"`
	TableID            uint      `gorm:"column:table_id;index:idx_table"`
	TableNameStr       string    `gorm:"column:table_name;index:idx_table"`
	MetaTitle          string    `gorm:"column:meta_title"`
	MetaDescription    string    `gorm:"column:meta_description"`
	Keywords           string    `gorm:"column:keywords"`
	CanonicalUrl       string    `gorm:"column:canonical_url"`
	OgTitle            string    `gorm:"column:og_title"`
	OgDescription      string    `gorm:"column:og_description"`
	OgImage            string    `gorm:"column:og_image"`
	RobotsIndex        bool      `gorm:"column:robots_index"`
	RobotsFollow       bool      `gorm:"column:robots_follow"`
	RobotsAdvanced     string    `gorm:"column:robots_advanced"`
	SeoScore           uint8     `gorm:"column:seo_score"`
	ReadabilityScore   uint8     `gorm:"column:readability_score"`
	SchemaData         string    `gorm:"column:schema_data;type:json"`
	CreatedAt          time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt          time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

// TableName trả về tên bảng trong database
func (SeoMeta) TableName() string {
	return "seo_meta"
}
