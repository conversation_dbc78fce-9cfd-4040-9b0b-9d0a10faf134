package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"

	"wnapi/internal/pkg/response"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/seo/dto/request"
	"wnapi/modules/seo/internal"
)

// SeoMetaHandler xử lý các request liên quan đến Seo<PERSON>eta
type SeoMetaHandler struct {
	seoMetaService internal.Service
}

// NewSeoMetaHandler tạo một instance mới của SeoMetaHandler
func NewSeoMetaHandler(seoMetaService internal.Service) *SeoMetaHandler {
	return &SeoMetaHandler{
		seoMetaService: seoMetaService,
	}
}

// GetSeoMeta xử lý request lấy thông tin SeoMeta theo ID
func (h *SeoMetaHandler) GetSeoMeta(c *gin.Context) {
	// Sử dụng context từ request
	ctx := c.Request.Context()

	ctx, span := tracing.StartSpan(ctx, "seo-api", "get_seo_meta")
	defer span.End()

	span.SetAttributes(
		attribute.String("operation", "GetSeoMeta"),
		attribute.String("handler", "SeoMetaHandler"),
	)

	idStr := c.Param("id")
	span.SetAttributes(attribute.String("id_param", idStr))

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error_type", "invalid_id"),
			attribute.String("id_value", idStr),
		)
		response.BadRequest(c, "ID không hợp lệ", "INVALID_ID", nil)
		return
	}

	span.SetAttributes(attribute.Int64("id", int64(id)))

	meta, err := h.seoMetaService.GetSeoMetaByID(ctx, uint(id))
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("service_method", "GetSeoMetaByID"),
			attribute.Int64("id", int64(id)),
		)
		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, nil)
		return
	}

	span.SetAttributes(
		attribute.Bool("success", true),
		attribute.String("meta_title", meta.MetaTitle),
		attribute.String("table_name", meta.TableName),
		attribute.Int64("table_id", int64(meta.TableID)),
	)

	response.Success(c, meta, nil)
}

// GetSeoMetaByTable xử lý request lấy thông tin SeoMeta theo table_id và table_name
func (h *SeoMetaHandler) GetSeoMetaByTable(c *gin.Context) {
	// Sử dụng context từ request
	ctx := c.Request.Context()

	ctx, span := tracing.StartSpan(ctx, "seo-api", "get_seo_meta_by_table")
	defer span.End()

	span.SetAttributes(
		attribute.String("operation", "GetSeoMetaByTable"),
		attribute.String("handler", "SeoMetaHandler"),
	)

	var req request.GetSeoMetaByTableRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error_type", "invalid_request"),
		)
		response.BadRequest(c, "Dữ liệu không hợp lệ", "INVALID_REQUEST", nil)
		return
	}

	span.SetAttributes(
		attribute.Int64("table_id", int64(req.TableID)),
		attribute.String("table_name", req.TableName),
	)

	meta, err := h.seoMetaService.GetSeoMetaByTable(ctx, req)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("service_method", "GetSeoMetaByTable"),
			attribute.Int64("table_id", int64(req.TableID)),
			attribute.String("table_name", req.TableName),
		)
		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, nil)
		return
	}

	span.SetAttributes(
		attribute.Bool("success", true),
		attribute.Int64("meta_id", int64(meta.MetaID)),
		attribute.String("meta_title", meta.MetaTitle),
	)

	response.Success(c, meta, nil)
}

// CreateSeoMeta xử lý request tạo SeoMeta mới
func (h *SeoMetaHandler) CreateSeoMeta(c *gin.Context) {
	// Sử dụng context từ request
	ctx := c.Request.Context()

	ctx, span := tracing.StartSpan(ctx, "seo-api", "create_seo_meta")
	defer span.End()

	span.SetAttributes(
		attribute.String("operation", "CreateSeoMeta"),
		attribute.String("handler", "SeoMetaHandler"),
	)

	var req request.CreateSeoMetaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error_type", "invalid_request"),
		)
		response.BadRequest(c, "Dữ liệu không hợp lệ", "INVALID_REQUEST", nil)
		return
	}

	span.SetAttributes(
		attribute.Int64("table_id", int64(req.TableID)),
		attribute.String("table_name", req.TableName),
	)

	meta, err := h.seoMetaService.CreateSeoMeta(ctx, req)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("service_method", "CreateSeoMeta"),
			attribute.Int64("table_id", int64(req.TableID)),
			attribute.String("table_name", req.TableName),
		)
		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, nil)
		return
	}

	span.SetAttributes(
		attribute.Bool("success", true),
		attribute.Int64("meta_id", int64(meta.MetaID)),
		attribute.Int64("seo_score", int64(meta.SeoScore)),
		attribute.Int64("readability_score", int64(meta.ReadabilityScore)),
	)

	response.Success(c, meta, nil)
}

// UpdateSeoMeta xử lý request cập nhật SeoMeta
func (h *SeoMetaHandler) UpdateSeoMeta(c *gin.Context) {
	// Sử dụng context từ request
	ctx := c.Request.Context()

	ctx, span := tracing.StartSpan(ctx, "seo-api", "update_seo_meta")
	defer span.End()

	span.SetAttributes(
		attribute.String("operation", "UpdateSeoMeta"),
		attribute.String("handler", "SeoMetaHandler"),
	)

	idStr := c.Param("id")
	span.SetAttributes(attribute.String("id_param", idStr))

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error_type", "invalid_id"),
			attribute.String("id_value", idStr),
		)
		response.BadRequest(c, "ID không hợp lệ", "INVALID_ID", nil)
		return
	}

	span.SetAttributes(attribute.Int64("id", int64(id)))

	var req request.UpdateSeoMetaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error_type", "invalid_request"),
			attribute.Int64("id", int64(id)),
		)
		response.BadRequest(c, "Dữ liệu không hợp lệ", "INVALID_REQUEST", nil)
		return
	}

	meta, err := h.seoMetaService.UpdateSeoMeta(ctx, uint(id), req)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("service_method", "UpdateSeoMeta"),
			attribute.Int64("id", int64(id)),
		)
		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, nil)
		return
	}

	span.SetAttributes(
		attribute.Bool("success", true),
		attribute.Int64("meta_id", int64(meta.MetaID)),
		attribute.Int64("seo_score", int64(meta.SeoScore)),
		attribute.Int64("readability_score", int64(meta.ReadabilityScore)),
	)

	response.Success(c, meta, nil)
}

// DeleteSeoMeta xử lý request xóa SeoMeta
func (h *SeoMetaHandler) DeleteSeoMeta(c *gin.Context) {
	// Sử dụng context từ request
	ctx := c.Request.Context()

	ctx, span := tracing.StartSpan(ctx, "seo-api", "delete_seo_meta")
	defer span.End()

	span.SetAttributes(
		attribute.String("operation", "DeleteSeoMeta"),
		attribute.String("handler", "SeoMetaHandler"),
	)

	idStr := c.Param("id")
	span.SetAttributes(attribute.String("id_param", idStr))

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error_type", "invalid_id"),
			attribute.String("id_value", idStr),
		)
		response.BadRequest(c, "ID không hợp lệ", "INVALID_ID", nil)
		return
	}

	span.SetAttributes(attribute.Int64("id", int64(id)))

	err = h.seoMetaService.DeleteSeoMeta(ctx, uint(id))
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("service_method", "DeleteSeoMeta"),
			attribute.Int64("id", int64(id)),
		)
		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, nil)
		return
	}

	span.SetAttributes(attribute.Bool("success", true))

	response.Success(c, nil, nil)
}

// DeleteSeoMetaByTable xử lý request xóa SeoMeta theo table_id và table_name
func (h *SeoMetaHandler) DeleteSeoMetaByTable(c *gin.Context) {
	// Sử dụng context từ request
	ctx := c.Request.Context()

	ctx, span := tracing.StartSpan(ctx, "seo-api", "delete_seo_meta_by_table")
	defer span.End()

	span.SetAttributes(
		attribute.String("operation", "DeleteSeoMetaByTable"),
		attribute.String("handler", "SeoMetaHandler"),
	)

	var req request.DeleteSeoMetaByTableRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error_type", "invalid_request"),
		)
		response.BadRequest(c, "Dữ liệu không hợp lệ", "INVALID_REQUEST", nil)
		return
	}

	span.SetAttributes(
		attribute.Int64("table_id", int64(req.TableID)),
		attribute.String("table_name", req.TableName),
	)

	err := h.seoMetaService.DeleteSeoMetaByTable(ctx, req)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("service_method", "DeleteSeoMetaByTable"),
			attribute.Int64("table_id", int64(req.TableID)),
			attribute.String("table_name", req.TableName),
		)
		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, nil)
		return
	}

	span.SetAttributes(attribute.Bool("success", true))

	response.Success(c, nil, nil)
}

// CreateOrUpdateSeoMeta xử lý request tạo hoặc cập nhật SeoMeta
func (h *SeoMetaHandler) CreateOrUpdateSeoMeta(c *gin.Context) {
	// Sử dụng context từ request
	ctx := c.Request.Context()

	ctx, span := tracing.StartSpan(ctx, "seo-api", "create_or_update_seo_meta")
	defer span.End()

	span.SetAttributes(
		attribute.String("operation", "CreateOrUpdateSeoMeta"),
		attribute.String("handler", "SeoMetaHandler"),
	)

	var req request.UpsertSeoMetaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("error_type", "invalid_request"),
		)
		response.BadRequest(c, "Dữ liệu không hợp lệ", "INVALID_REQUEST", nil)
		return
	}

	span.SetAttributes(
		attribute.Int64("table_id", int64(req.TableID)),
		attribute.String("table_name", req.TableName),
	)

	meta, err := h.seoMetaService.CreateOrUpdateSeoMeta(ctx, req)
	if err != nil {
		tracing.RecordError(ctx, err,
			attribute.String("service_method", "CreateOrUpdateSeoMeta"),
			attribute.Int64("table_id", int64(req.TableID)),
			attribute.String("table_name", req.TableName),
		)
		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, nil)
		return
	}

	span.SetAttributes(
		attribute.Bool("success", true),
		attribute.Int64("meta_id", int64(meta.MetaID)),
		attribute.Int64("seo_score", int64(meta.SeoScore)),
		attribute.Int64("readability_score", int64(meta.ReadabilityScore)),
	)

	response.Success(c, meta, nil)
}
