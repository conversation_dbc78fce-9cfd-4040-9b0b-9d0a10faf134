package api

import (
	"github.com/gin-gonic/gin"

	"wnapi/internal/core"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/permission"
	"wnapi/modules/seo/api/handlers"
	"wnapi/modules/seo/internal"
)

// Handler là đối tượng chính xử lý API cho module SEO
type Handler struct {
	seoMetaHandler    *handlers.SeoMetaHandler
	middlewareFactory *permission.MiddlewareFactory
	jwtService        *auth.JWTService
}

// NewHandler tạo một handler mới
func NewHandler(seoMetaHandler *handlers.SeoMetaHandler, middlewareFactory *permission.MiddlewareFactory, jwtService *auth.JWTService) *Handler {
	return &Handler{
		seoMetaHandler:    seoMetaHandler,
		middlewareFactory: middlewareFactory,
		jwtService:        jwtService,
	}
}

// RegisterRoutes đăng ký tất cả routes cho module SEO
func (h *Handler) RegisterRoutes(server *core.Server) error {
	// API Group
	apiGroup := server.Group("/api/admin/v1/seo")

	// Health check endpoint
	apiGroup.GET("/healthy", h.healthCheck)

	// Protected routes cho SEO Meta - require JWT authentication
	metaGroup := apiGroup.Group("/meta")
	metaGroup.Use(h.jwtService.JWTAuthMiddleware())
	{
		// Get SEO meta by ID - requires read permission
		metaGroup.GET("/:id",
			h.middlewareFactory.RequirePermission(internal.ReadSeoMetaPermission),
			h.seoMetaHandler.GetSeoMeta,
		)

		// Get SEO meta by table - requires read permission
		metaGroup.GET("/table",
			h.middlewareFactory.RequirePermission(internal.ReadSeoMetaPermission),
			h.seoMetaHandler.GetSeoMetaByTable,
		)

		// Create SEO meta - requires create permission
		metaGroup.POST("",
			h.middlewareFactory.RequirePermission(internal.CreateSeoMetaPermission),
			h.seoMetaHandler.CreateSeoMeta,
		)

		// Update SEO meta - requires update permission
		metaGroup.PUT("/:id",
			h.middlewareFactory.RequirePermission(internal.UpdateSeoMetaPermission),
			h.seoMetaHandler.UpdateSeoMeta,
		)

		// Delete SEO meta by ID - requires delete permission
		metaGroup.DELETE("/:id",
			h.middlewareFactory.RequirePermission(internal.DeleteSeoMetaPermission),
			h.seoMetaHandler.DeleteSeoMeta,
		)

		// Delete SEO meta by table - requires delete permission
		metaGroup.DELETE("/table",
			h.middlewareFactory.RequirePermission(internal.DeleteSeoMetaPermission),
			h.seoMetaHandler.DeleteSeoMetaByTable,
		)

		// Upsert SEO meta - requires upsert permission
		metaGroup.POST("/upsert",
			h.middlewareFactory.RequirePermission(internal.UpsertSeoMetaPermission),
			h.seoMetaHandler.CreateOrUpdateSeoMeta,
		)
	}

	return nil
}

// RegisterRoutesWithEngine đăng ký tất cả routes cho module SEO với gin.Engine
func (h *Handler) RegisterRoutesWithEngine(engine *gin.Engine) error {
	// Register routes directly with gin.Engine (duplicate of RegisterRoutes logic)
	return h.registerRoutesDirectly(engine)
}

// registerRoutesDirectly registers routes directly with gin.Engine
func (h *Handler) registerRoutesDirectly(engine *gin.Engine) error {
	// API Group
	apiGroup := engine.Group("/api/admin/v1/seo")

	// Health check endpoint
	apiGroup.GET("/healthy", h.healthCheck)

	// Protected routes cho SEO Meta - require JWT authentication
	metaGroup := apiGroup.Group("/meta")
	metaGroup.Use(h.jwtService.JWTAuthMiddleware())
	{
		// Get SEO meta by ID - requires read permission
		metaGroup.GET("/:id",
			h.middlewareFactory.RequirePermission(internal.ReadSeoMetaPermission),
			h.seoMetaHandler.GetSeoMeta,
		)

		// Get SEO meta by table - requires read permission
		metaGroup.GET("/table",
			h.middlewareFactory.RequirePermission(internal.ReadSeoMetaPermission),
			h.seoMetaHandler.GetSeoMetaByTable,
		)

		// Create SEO meta - requires create permission
		metaGroup.POST("",
			h.middlewareFactory.RequirePermission(internal.CreateSeoMetaPermission),
			h.seoMetaHandler.CreateSeoMeta,
		)

		// Update SEO meta - requires update permission
		metaGroup.PUT("/:id",
			h.middlewareFactory.RequirePermission(internal.UpdateSeoMetaPermission),
			h.seoMetaHandler.UpdateSeoMeta,
		)

		// Delete SEO meta by ID - requires delete permission
		metaGroup.DELETE("/:id",
			h.middlewareFactory.RequirePermission(internal.DeleteSeoMetaPermission),
			h.seoMetaHandler.DeleteSeoMeta,
		)

		// Delete SEO meta by table - requires delete permission
		metaGroup.DELETE("/table",
			h.middlewareFactory.RequirePermission(internal.DeleteSeoMetaPermission),
			h.seoMetaHandler.DeleteSeoMetaByTable,
		)

		// Upsert SEO meta - requires upsert permission
		metaGroup.POST("/upsert",
			h.middlewareFactory.RequirePermission(internal.UpsertSeoMetaPermission),
			h.seoMetaHandler.CreateOrUpdateSeoMeta,
		)
	}

	return nil
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "seo",
		"message": "SEO module is running",
	})
}
