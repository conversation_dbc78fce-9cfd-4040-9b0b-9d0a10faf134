package api

import (
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/modules/cart/api/handlers"
	"wnapi/modules/cart/internal"
	"wnapi/modules/cart/service"

	"github.com/gin-gonic/gin"
)

// Handler là đối tượng chính xử lý API cho module Cart
type Handler struct {
	cartHandler       *handlers.CartHandler
	middlewareFactory *permission.MiddlewareFactory
	jwtService        *auth.JWTService
}

// NewHandler tạo một handler mới
func NewHandler(cartService service.CartService, middlewareFactory *permission.MiddlewareFactory, jwtService *auth.JWTService, logger logger.Logger) *Handler {
	return &Handler{
		cartHandler:       handlers.NewCartHandler(cartService, logger),
		middlewareFactory: middlewareFactory,
		jwtService:        jwtService,
	}
}

// RegisterRoutes đăng ký các route của module Cart
func (h *Handler) RegisterRoutes(engine *gin.Engine) error {
	// Định nghĩa base path
	basePath := "/api/admin/v1/carts"

	// Định nghĩa API group
	apiGroup := engine.Group(basePath)

	// Thêm route health check
	apiGroup.GET("/health", h.healthCheck)

	// Protected routes - yêu cầu JWT authentication
	protectedRoutes := apiGroup.Group("")
	protectedRoutes.Use(h.jwtService.JWTAuthMiddleware())
	{
		// ===== CART MANAGEMENT =====
		// List carts - requires list permission
		protectedRoutes.GET("/",
			h.middlewareFactory.RequirePermission(internal.ListCartPermission),
			h.cartHandler.ListCarts,
		)

		// Get single cart - requires read permission
		protectedRoutes.GET("/:id",
			h.middlewareFactory.RequirePermission(internal.ReadCartPermission),
			h.cartHandler.GetCart,
		)

		// Create cart - requires create permission
		protectedRoutes.POST("/",
			h.middlewareFactory.RequirePermission(internal.CreateCartPermission),
			h.cartHandler.CreateCart,
		)

		// Update cart - requires update permission
		protectedRoutes.PUT("/:id",
			h.middlewareFactory.RequirePermission(internal.UpdateCartPermission),
			h.cartHandler.UpdateCart,
		)

		// Delete cart - requires delete permission
		protectedRoutes.DELETE("/:id",
			h.middlewareFactory.RequirePermission(internal.DeleteCartPermission),
			h.cartHandler.DeleteCart,
		)

		// ===== CART LIFECYCLE OPERATIONS =====
		// Recalculate cart - requires recalculate permission
		protectedRoutes.POST("/:id/recalculate",
			h.middlewareFactory.RequirePermission(internal.RecalculateCartPermission),
			h.cartHandler.RecalculateCart,
		)

		// Apply coupon - requires apply coupon permission
		protectedRoutes.POST("/:id/apply-coupon",
			h.middlewareFactory.RequirePermission(internal.ApplyCouponPermission),
			h.cartHandler.ApplyCoupon,
		)

		// Expire cart - requires expire permission
		protectedRoutes.POST("/:id/expire",
			h.middlewareFactory.RequirePermission(internal.ExpireCartPermission),
			h.cartHandler.ExpireCart,
		)

		// Convert cart to order - requires convert permission
		protectedRoutes.POST("/:id/convert-to-order",
			h.middlewareFactory.RequirePermission(internal.ConvertCartToOrderPermission),
			h.cartHandler.ConvertCartToOrder,
		)

		// Merge anonymous cart - requires merge permission
		protectedRoutes.POST("/merge",
			h.middlewareFactory.RequirePermission(internal.MergeAnonymousCartPermission),
			h.cartHandler.MergeAnonymousCart,
		)

		// ===== CART ITEMS MANAGEMENT =====
		// Add item to cart - requires add item permission
		protectedRoutes.POST("/:id/items",
			h.middlewareFactory.RequirePermission(internal.AddItemToCartPermission),
			h.cartHandler.AddItemToCart,
		)

		// Update cart item - requires update item permission
		protectedRoutes.PUT("/items/:itemId",
			h.middlewareFactory.RequirePermission(internal.UpdateCartItemPermission),
			h.cartHandler.UpdateCartItem,
		)

		// Remove cart item - requires remove item permission
		protectedRoutes.DELETE("/items/:itemId",
			h.middlewareFactory.RequirePermission(internal.RemoveItemFromCartPermission),
			h.cartHandler.RemoveCartItem,
		)

		// ===== CART ITEM ADDONS MANAGEMENT =====
		// Add addon to cart item - requires add addon permission
		protectedRoutes.POST("/items/:itemId/addons",
			h.middlewareFactory.RequirePermission(internal.AddAddonToCartItemPermission),
			h.cartHandler.AddAddonToCartItem,
		)

		// Remove addon from cart item - requires remove addon permission
		protectedRoutes.DELETE("/addons/:addonId",
			h.middlewareFactory.RequirePermission(internal.RemoveAddonFromCartItemPermission),
			h.cartHandler.RemoveAddonFromCartItem,
		)
	}

	return nil
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, map[string]interface{}{
		"status":  "ok",
		"module":  "cart",
		"message": "Cart module is running",
	})
}
