package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Constants for error codes
const (
	ErrCodeInvalidRequestFormat = "INVALID_REQUEST_FORMAT"
	ErrCodeUnauthorized         = "UNAUTHORIZED"
	ErrCodeInternalServerError  = "INTERNAL_SERVER_ERROR"
	ErrCodeCartNotFound         = "CART_NOT_FOUND"
	ErrCodeCartItemNotFound     = "CART_ITEM_NOT_FOUND"
	ErrCodeAddonNotFound        = "ADDON_NOT_FOUND"
	ErrCodeInvalidItemType      = "INVALID_ITEM_TYPE"
	ErrCodeInvalidAddonType     = "INVALID_ADDON_TYPE"
)

// Constants for error messages
const (
	MsgInvalidCartID  = "Invalid cart ID"
	MsgInvalidItemID  = "Invalid item ID"
	MsgInvalidAddonID = "Invalid addon ID"
)

// apiSuccess sends a successful API response
func apiSuccess(c *gin.Context, statusCode int, message string, data interface{}) {
	c.JSON(statusCode, map[string]interface{}{
		"success": true,
		"message": message,
		"data":    data,
	})
}

// apiSuccessWithMeta sends a successful API response with metadata
func apiSuccessWithMeta(c *gin.Context, statusCode int, message string, data interface{}, meta map[string]interface{}) {
	c.JSON(statusCode, map[string]interface{}{
		"success": true,
		"message": message,
		"data":    data,
		"meta":    meta,
	})
}

// apiError sends an error API response
func apiError(c *gin.Context, statusCode int, errorCode string, message string) {
	c.JSON(statusCode, map[string]interface{}{
		"success":    false,
		"message":    message,
		"error_code": errorCode,
	})
}

// apiErrorWithDetails sends an error API response with detailed information
func apiErrorWithDetails(c *gin.Context, statusCode int, errorCode string, message string, details interface{}) {
	c.JSON(statusCode, map[string]interface{}{
		"success":    false,
		"message":    message,
		"error_code": errorCode,
		"details":    details,
	})
}

// handleValidationError handles validation errors
func handleValidationError(c *gin.Context, err error) {
	details := []interface{}{map[string]string{"message": err.Error()}}
	apiErrorWithDetails(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, "Invalid request data", details)
}
