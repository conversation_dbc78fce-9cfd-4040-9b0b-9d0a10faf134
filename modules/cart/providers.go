package cart

import (
	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/config"
	"wnapi/modules/cart/api"
	"wnapi/modules/cart/internal"
)

// NewCartConfig creates cart configuration
func NewCartConfig(cfg config.Config) (*internal.CartConfig, error) {
	return internal.LoadCartConfig()
}

// RegisterCartRoutes registers cart routes with gin.Engine
func RegisterCartRoutes(handler *api.Handler, engine *gin.Engine) error {
	// Register all cart routes using the full API handler
	return handler.RegisterRoutes(engine)
}
