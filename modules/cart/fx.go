package cart

import (
	"go.uber.org/fx"

	"wnapi/internal/fx/modules"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/modules/cart/api"
	"wnapi/modules/cart/service"
)

func init() {
	modules.GlobalRegistry.Register(&CartModule{})
}

// CartModule implements the FX module interface
type CartModule struct{}

// Name returns the module name
func (m *CartModule) Name() string {
	return "cart"
}

// Dependencies returns module dependencies
func (m *CartModule) Dependencies() []string {
	return []string{"tenant", "auth", "rbac"}
}

// Priority returns module loading priority
func (m *CartModule) Priority() int {
	return 80 // Load after core modules (tenant, auth, rbac)
}

// Enabled returns whether the module is enabled
func (m *CartModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default to enabled
}

// GetMigrationPath returns path to module migrations
func (m *CartModule) GetMigrationPath() string {
	return "modules/cart/migrations"
}

// GetMigrationOrder returns migration priority order
func (m *CartModule) GetMigrationOrder() int {
	return 80 // Cart module runs after core modules (tenant, auth, rbac, etc.)
}

// Module returns FX options for the module
func (m *CartModule) Module() fx.Option {
	return fx.Module("cart",
		// Providers
		fx.Provide(
			// Configuration
			NewCartConfig,

			// API Handler
			NewCartHandler,
		),

		// Route registration
		fx.Invoke(RegisterCartRoutes),
	)
}

// NewCartHandler creates cart API handler
func NewCartHandler(
	cartService service.CartService,
	middlewareFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	logger logger.Logger,
) *api.Handler {
	return api.NewHandler(cartService, middlewareFactory, jwtService, logger)
}
