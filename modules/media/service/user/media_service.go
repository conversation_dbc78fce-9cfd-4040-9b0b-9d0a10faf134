package user

import (
	"context"
	"fmt"
	"io/ioutil"
	"mime/multipart"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"wnapi/internal/pkg/tracing"
	"wnapi/modules/media/dto/media"
	"wnapi/modules/media/models"
	"wnapi/modules/media/repository"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
)

// MediaService triển khai MediaService cho user
type MediaService struct {
	mediaRepo   repository.MediaRepository
	storageRepo repository.StorageRepository
}

// NewMediaService tạo một instance mới của MediaService
func NewMediaService(
	mediaRepo repository.MediaRepository,
	storageRepo repository.StorageRepository,
) *MediaService {
	return &MediaService{
		mediaRepo:   mediaRepo,
		storageRepo: storageRepo,
	}
}

// Upload tải lên một media mới
func (s *MediaService) Upload(ctx context.Context, tenantID string, userID string, req *media.UploadMediaRequest) (*media.UploadMediaResponse, error) {
	ctx, span := tracing.StartSpan(ctx, "media", "user_media_service.upload")
	defer span.End()

	// Add relevant attributes for tracing
	tracing.AddSpanAttributes(ctx,
		attribute.String("media.tenant_id", tenantID),
		attribute.String("media.user_id", userID),
		attribute.String("media.file_name", req.File.Filename),
		attribute.Int64("media.file_size", req.File.Size),
		attribute.String("media.mime_type", req.File.Header.Get("Content-Type")),
		attribute.String("media.operation_type", "upload"),
		attribute.Bool("media.is_public", req.IsPublic),
	)

	// Xác định media type từ content type
	mediaType := getMediaTypeFromContentType(req.File.Header.Get("Content-Type"))
	tracing.AddSpanAttributes(ctx, attribute.String("media.file_type", string(mediaType)))

	fmt.Println("mediaType", mediaType)

	// Lưu file vào storage
	mediaEntity, err := s.SaveFile(ctx, tenantID, req.File, mediaType)
	if err != nil {
		tracing.RecordError(ctx, err, attribute.String("media.operation", "save_file"))
		return nil, err
	}
	// Gán website_id
	mediaEntity.WebsiteID = req.WebsiteID

	// Cập nhật thông tin bổ sung
	mediaEntity.IsPublic = req.IsPublic
	if userIDUint, err := strconv.ParseUint(userID, 10, 32); err == nil {
		uploadedBy := uint(userIDUint)
		mediaEntity.UploadedBy = &uploadedBy
	}

	// Lưu thông tin vào database
	err = s.mediaRepo.Create(ctx, mediaEntity)
	if err != nil {
		// Nếu thất bại, xóa file từ storage
		_ = s.storageRepo.Delete(ctx, mediaEntity.ObjectKey)
		return nil, err
	}

	// Xử lý file nếu cần (chạy trong goroutine)
	go func() {
		ctxBg := context.Background()
		_ = s.ProcessFile(ctxBg, mediaEntity)
	}()

	// Lấy URL truy cập
	url, _ := s.storageRepo.GetURL(ctx, mediaEntity.ObjectKey)

	// Trả về response
	return &media.UploadMediaResponse{
		ID:        mediaEntity.ID,
		WebsiteID: mediaEntity.WebsiteID,
		URL:       url,
		Status:    string(mediaEntity.Status),
	}, nil
}

// Get lấy thông tin chi tiết về một media
func (s *MediaService) Get(ctx context.Context, tenantID string, id string) (*media.MediaResponse, error) {
	// Lấy thông tin từ repository
	mediaEntity, err := s.mediaRepo.GetByID(ctx, id, tenantID)
	if err != nil {
		return nil, err
	}

	if mediaEntity == nil {
		return nil, fmt.Errorf("media không tồn tại")
	}

	// Lấy URL truy cập
	url, _ := s.storageRepo.GetURL(ctx, mediaEntity.ObjectKey)

	// Tạo response
	return &media.MediaResponse{
		ID:               mediaEntity.ID,
		WebsiteID:        mediaEntity.WebsiteID,
		MediaType:        string(mediaEntity.MediaType),
		Filename:         mediaEntity.Filename,
		OriginalFilename: mediaEntity.OriginalFilename,
		ContentType:      mediaEntity.ContentType,
		Size:             mediaEntity.Size,
		Status:           string(mediaEntity.Status),
		PublicURL:        url,
		IsPublic:         mediaEntity.IsPublic,
		Metadata:         mediaEntity.Metadata,
		Properties:       mediaEntity.Properties,
		CreatedAt:        mediaEntity.CreatedAt,
		UpdatedAt:        mediaEntity.UpdatedAt,
	}, nil
}

// List lấy danh sách media
func (s *MediaService) List(ctx context.Context, tenantID string, req *media.ListMediaRequest) ([]*media.MediaResponse, string, error) {
	// Tạo parameters cho repository
	params := repository.ListMediaParams{
		TenantID:  tenantID,
		MediaType: req.MediaType,
		Status:    req.Status,
		IsPublic:  req.IsPublic,
		WebsiteID: req.WebsiteID,
		Search:    req.Search,
		Cursor:    req.Cursor,
		Limit:     req.Limit,
	}

	// Lấy danh sách từ repository
	mediaList, nextCursor, err := s.mediaRepo.List(ctx, params)
	if err != nil {
		return nil, "", err
	}

	// Tạo response list
	mediaResponseList := make([]*media.MediaResponse, len(mediaList))
	for i, mediaEntity := range mediaList {
		// Lấy URL truy cập
		url, _ := s.storageRepo.GetURL(ctx, mediaEntity.ObjectKey)

		// Tạo response item
		mediaResponseList[i] = &media.MediaResponse{
			ID:               mediaEntity.ID,
			WebsiteID:        mediaEntity.WebsiteID,
			MediaType:        string(mediaEntity.MediaType),
			Filename:         mediaEntity.Filename,
			OriginalFilename: mediaEntity.OriginalFilename,
			ContentType:      mediaEntity.ContentType,
			Size:             mediaEntity.Size,
			Status:           string(mediaEntity.Status),
			PublicURL:        url,
			IsPublic:         mediaEntity.IsPublic,
			Metadata:         mediaEntity.Metadata,
			Properties:       mediaEntity.Properties,
			CreatedAt:        mediaEntity.CreatedAt,
			UpdatedAt:        mediaEntity.UpdatedAt,
		}
	}

	return mediaResponseList, nextCursor, nil
}

// Update cập nhật thông tin media
func (s *MediaService) Update(ctx context.Context, tenantID string, id string, req *media.UpdateMediaRequest) (*media.MediaResponse, error) {
	// Lấy thông tin hiện tại
	mediaEntity, err := s.mediaRepo.GetByID(ctx, id, tenantID)
	if err != nil {
		return nil, err
	}

	if mediaEntity == nil {
		return nil, fmt.Errorf("media không tồn tại")
	}

	// Cập nhật thông tin

	if req.IsPublic != nil {
		mediaEntity.IsPublic = *req.IsPublic
	}

	// Lưu thay đổi
	err = s.mediaRepo.Update(ctx, mediaEntity)
	if err != nil {
		return nil, err
	}

	// Lấy thông tin sau khi cập nhật
	return s.Get(ctx, tenantID, id)
}

// Delete xóa media
func (s *MediaService) Delete(ctx context.Context, tenantID string, id string, permanent bool) error {
	// Lấy thông tin media
	mediaEntity, err := s.mediaRepo.GetByID(ctx, id, tenantID)
	if err != nil {
		return err
	}

	if mediaEntity == nil {
		return fmt.Errorf("media không tồn tại")
	}

	// Xóa từ database
	if permanent {
		err = s.mediaRepo.DeletePermanently(ctx, id, tenantID)
	} else {
		err = s.mediaRepo.Delete(ctx, id, tenantID)
	}

	if err != nil {
		return err
	}

	// Nếu xóa vĩnh viễn, xóa cả file từ storage
	if permanent {
		err = s.storageRepo.Delete(ctx, mediaEntity.ObjectKey)
		if err != nil {
			return err
		}
	}

	return nil
}

// GetFile lấy nội dung file của media
func (s *MediaService) GetFile(ctx context.Context, tenantID string, id string) ([]byte, string, error) {
	// Lấy thông tin media
	mediaEntity, err := s.mediaRepo.GetByID(ctx, id, tenantID)
	if err != nil {
		return nil, "", err
	}

	if mediaEntity == nil {
		return nil, "", fmt.Errorf("media không tồn tại")
	}

	// Tải file từ storage
	data, err := s.storageRepo.Download(ctx, mediaEntity.ObjectKey)
	if err != nil {
		return nil, "", err
	}

	return data, mediaEntity.ContentType, nil
}

// ProcessFile xử lý file media (tạo các phiên bản, optimize, v.v.)
func (s *MediaService) ProcessFile(ctx context.Context, mediaEntity *models.Media) error {
	// Hiện tại chỉ cập nhật trạng thái
	mediaEntity.Status = models.MediaStatusReady
	return s.mediaRepo.Update(ctx, mediaEntity)
}

// SaveFile lưu file vào storage
func (s *MediaService) SaveFile(ctx context.Context, tenantID string, file *multipart.FileHeader, mediaType models.MediaType) (*models.Media, error) {
	// Đọc nội dung file
	f, err := file.Open()
	if err != nil {
		fmt.Printf("Lỗi khi mở file: %v\n", err)
		return nil, fmt.Errorf("không thể mở file: %w", err)
	}
	defer f.Close()

	fileData, err := ioutil.ReadAll(f)
	if err != nil {
		fmt.Printf("Lỗi khi đọc file: %v\n", err)
		return nil, fmt.Errorf("không thể đọc nội dung file: %w", err)
	}

	// Tạo key cho object storage
	fileExt := filepath.Ext(file.Filename)
	objectKey := fmt.Sprintf(
		"%s/%s/%s/%s%s",
		tenantID,
		string(mediaType),
		time.Now().Format("2006-01-02"),
		uuid.New().String(),
		fileExt,
	)

	// Thêm log để debug
	fmt.Printf("Uploading file to Minio: %s, Size: %d bytes, Type: %s\n",
		objectKey, len(fileData), file.Header.Get("Content-Type"))

	// Thử upload tối đa 3 lần
	maxRetries := 3
	var uploadErr error

	for i := 0; i < maxRetries; i++ {
		uploadErr = s.storageRepo.Upload(ctx, fileData, objectKey, file.Header.Get("Content-Type"))
		if uploadErr == nil {
			break
		}
		fmt.Printf("Thử upload lần %d thất bại: %v\n", i+1, uploadErr)
		time.Sleep(time.Second * time.Duration(i+1)) // Backoff nhẹ
	}

	if uploadErr != nil {
		fmt.Printf("Lỗi khi upload file lên Minio sau %d lần thử: %v\n", maxRetries, uploadErr)
		return nil, fmt.Errorf("không thể upload file lên storage: %w", uploadErr)
	}

	fmt.Printf("Upload thành công file: %s\n", objectKey)

	// Convert tenantID to uint
	tenantIDUint, err := strconv.ParseUint(tenantID, 10, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid tenant ID: %w", err)
	}

	// Tạo entity mới
	media := &models.Media{
		// ID will be auto-generated by database AUTO_INCREMENT
		TenantID:         uint(tenantIDUint),
		MediaType:        mediaType,
		Filename:         file.Filename,
		OriginalFilename: file.Filename,
		ObjectKey:        objectKey,
		ContentType:      file.Header.Get("Content-Type"),
		Size:             file.Size,
		Status:           models.MediaStatusPending,
		SchemaVersion:    1,
		Metadata:         make(models.Metadata),
		Properties:       make(models.Properties),
	}

	// Thêm metadata cơ bản
	media.Metadata["originalSize"] = file.Size

	return media, nil
}

// Hàm hỗ trợ xác định loại media từ content type
func getMediaTypeFromContentType(contentType string) models.MediaType {
	contentType = strings.ToLower(contentType)

	if strings.HasPrefix(contentType, "image/") {
		return models.MediaTypeImage
	}

	if strings.HasPrefix(contentType, "video/") {
		return models.MediaTypeVideo
	}

	if strings.HasPrefix(contentType, "audio/") {
		return models.MediaTypeAudio
	}

	if contentType == "application/pdf" ||
		contentType == "application/msword" ||
		contentType == "application/vnd.openxmlformats-officedocument.wordprocessingml.document" {
		return models.MediaTypeDocument
	}

	return models.MediaTypeOther
}
