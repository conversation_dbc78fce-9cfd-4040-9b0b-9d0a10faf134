CREATE TABLE media (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    media_type ENUM('image', 'video', 'audio', 'document', 'other') NOT NULL,
    filename VA<PERSON>HA<PERSON>(255) NOT NULL,
    original_filename VA<PERSON>HAR(255) NOT NULL,
    object_key VARCHAR(512) NOT NULL,
    content_type VARCHAR(100) NOT NULL,
    size BIGINT UNSIGNED NOT NULL,
    status ENUM('pending', 'processing', 'ready', 'failed') NOT NULL DEFAULT 'pending',
    public_url VARCHAR(1024),
    uploaded_by INT UNSIGNED,
    checksum VARCHAR(64),
    is_public BOOLEAN NOT NULL DEFAULT FALSE,

    -- JSON fields for flexible metadata and properties
    metadata JSON COMMENT 'Type-specific metadata (dimensions, duration, etc.)',
    properties JSON COMMENT 'Extended dynamic properties',

    -- Schema versioning for future migrations
    schema_version INT NOT NULL DEFAULT 1,

    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_tenant_id (tenant_id),
    INDEX idx_website_id (website_id),
    INDEX idx_media_type (media_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_checksum (checksum),
    INDEX idx_is_public (is_public),
    INDEX idx_uploaded_by (uploaded_by),

    -- Index for JSON fields if supported by MySQL version
    -- INDEX idx_metadata (metadata->'$.width', metadata->'$.height', metadata->'$.duration') USING JSON

    FULLTEXT INDEX ft_filename (filename)
) ENGINE=InnoDB;