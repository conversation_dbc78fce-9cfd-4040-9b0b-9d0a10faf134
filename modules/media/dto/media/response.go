package media

import (
	"time"
)

// MediaResponse định nghĩa response body cho một media
type MediaResponse struct {
	ID               uint                   `json:"id"`
	WebsiteID        uint                   `json:"website_id"`
	MediaType        string                 `json:"media_type"`
	Filename         string                 `json:"filename"`
	OriginalFilename string                 `json:"original_filename"`
	ContentType      string                 `json:"content_type"`
	Size             int64                  `json:"size"`
	Status           string                 `json:"status"`
	PublicURL        string                 `json:"public_url,omitempty"`
	IsPublic         bool                   `json:"is_public"`
	Metadata         map[string]interface{} `json:"metadata,omitempty"`
	Properties       map[string]interface{} `json:"properties,omitempty"`
	CreatedAt        time.Time              `json:"created_at"`
	UpdatedAt        time.Time              `json:"updated_at"`
}

// UploadMediaResponse định nghĩa response body cho API upload media
type UploadMediaResponse struct {
	ID        uint   `json:"id"`
	WebsiteID uint   `json:"website_id"`
	URL       string `json:"url,omitempty"`
	Status    string `json:"status"`
}

// MediaVersionResponse định nghĩa response body cho một phiên bản của media
type MediaVersionResponse struct {
	ID          uint                   `json:"id"`
	VersionType string                 `json:"version_type"`
	Width       *int                   `json:"width,omitempty"`
	Height      *int                   `json:"height,omitempty"`
	ContentType string                 `json:"content_type"`
	Size        int64                  `json:"size"`
	Quality     *int                   `json:"quality,omitempty"`
	PublicURL   string                 `json:"public_url,omitempty"`
	Properties  map[string]interface{} `json:"properties,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
}
