package media

import (
	"mime/multipart"

	"wnapi/modules/media/dto/common"
)

// UploadMediaRequest định nghĩa request body cho API upload media
type UploadMediaRequest struct {
	IsPublic  bool                  `form:"is_public" json:"is_public"`
	File      *multipart.FileHeader `form:"file" json:"-"`
	WebsiteID uint                  `form:"website_id" json:"website_id"`
}

// UpdateMediaRequest định nghĩa request body cho API update media
type UpdateMediaRequest struct {
	IsPublic *bool `json:"is_public,omitempty"`
}

// ListMediaRequest định nghĩa request parameters cho API list media
type ListMediaRequest struct {
	common.CursorPaginationRequest
	MediaType string `form:"media_type" json:"media_type"`
	Status    string `form:"status" json:"status"`
	IsPublic  *bool  `form:"is_public" json:"is_public"`
	Search    string `form:"search" json:"search"`
	WebsiteID uint   `form:"website_id" json:"website_id"`
}

// DeleteMediaRequest định nghĩa request parameters cho API delete media
type DeleteMediaRequest struct {
	Permanent bool `form:"permanent" json:"permanent"`
}
