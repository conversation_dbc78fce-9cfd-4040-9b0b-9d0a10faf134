package repository

import (
	"context"

	"wnapi/modules/media/models"
)

// MediaRepository định nghĩa các phương thức để thao tác với entity media
type MediaRepository interface {
	// Create tạo một media mới
	Create(ctx context.Context, media *models.Media) error

	// GetByID lấy media theo ID
	GetByID(ctx context.Context, id string, tenantID string) (*models.Media, error)

	// Update cập nhật thông tin media
	Update(ctx context.Context, media *models.Media) error

	// Delete xóa media (soft delete)
	Delete(ctx context.Context, id string, tenantID string) error

	// DeletePermanently xóa media vĩnh viễn
	DeletePermanently(ctx context.Context, id string, tenantID string) error

	// List lấy danh sách media với phân trang
	List(ctx context.Context, params ListMediaParams) ([]*models.Media, string, error)
}

// MediaFolderRepository định nghĩa các phư<PERSON>ng thức để thao tác với entity media folder
type MediaFolderRepository interface {
	// Create tạo một thư mục mới
	Create(ctx context.Context, folder *models.MediaFolder) error

	// GetByID lấy thư mục theo ID
	GetByID(ctx context.Context, folderID int, tenantID int) (*models.MediaFolder, error)

	// GetBySlug lấy thư mục theo slug
	GetBySlug(ctx context.Context, slug string, tenantID int) (*models.MediaFolder, error)

	// Update cập nhật thông tin thư mục
	Update(ctx context.Context, folder *models.MediaFolder) error

	// Delete xóa thư mục (soft delete)
	Delete(ctx context.Context, folderID int, tenantID int) error

	// List lấy danh sách thư mục con
	List(ctx context.Context, params ListMediaFolderParams) ([]*models.MediaFolder, string, error)
}

// StorageRepository định nghĩa các phương thức để thao tác với storage
type StorageRepository interface {
	// Upload tải lên file
	Upload(ctx context.Context, fileData []byte, objectKey string, contentType string) error

	// Download tải xuống file
	Download(ctx context.Context, objectKey string) ([]byte, error)

	// Delete xóa file
	Delete(ctx context.Context, objectKey string) error

	// GetURL lấy URL để truy cập file
	GetURL(ctx context.Context, objectKey string) (string, error)
}

// ListMediaParams chứa các tham số để lọc và phân trang danh sách media
type ListMediaParams struct {
	TenantID  string
	MediaType string
	Status    string
	IsPublic  *bool
	Search    string
	Cursor    string
	Limit     int
	WebsiteID uint
}

// ListMediaFolderParams chứa các tham số để lọc và phân trang danh sách thư mục
type ListMediaFolderParams struct {
	TenantID int
	ParentID *int
	IsPublic *bool
	Search   string
	Cursor   string
	Limit    int
}

// Factory định nghĩa các phương thức để tạo repository instances
type Factory interface {
	// NewMediaRepository tạo một media repository mới
	NewMediaRepository() MediaRepository

	// NewMediaFolderRepository tạo một media folder repository mới
	NewMediaFolderRepository() MediaFolderRepository

	// NewStorageRepository tạo một storage repository mới
	NewStorageRepository() StorageRepository
}
