package request

// SeoMetaRequest represents the SEO metadata for a blog post
type SeoMetaRequest struct {
	MetaTitle        string                 `json:"meta_title"`
	MetaDescription  string                 `json:"meta_description"`
	Keywords         string                 `json:"keywords"`
	CanonicalUrl     string                 `json:"canonical_url"`
	OgTitle          string                 `json:"og_title"`
	OgDescription    string                 `json:"og_description"`
	OgImage          string                 `json:"og_image"`
	RobotsIndex      bool                   `json:"robots_index"`
	RobotsFollow     bool                   `json:"robots_follow"`
	RobotsAdvanced   string                 `json:"robots_advanced"`
	SeoScore         uint8                  `json:"seo_score"`
	ReadabilityScore uint8                  `json:"readability_score"`
	SchemaData       map[string]interface{} `json:"schema_data"`
}
