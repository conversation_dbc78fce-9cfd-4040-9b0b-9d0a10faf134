-- B<PERSON><PERSON> viết mặc định cho tenant mới
INSERT INTO blog_posts (tenant_id, author_id, created_by, title, slug, description, content, status, publish_at, created_at, updated_at) VALUES
(@TENANT_ID, @ADMIN_ID, @ADMIN_ID, 'Chào mừng đến với Blog', 'chao-mung-den-voi-blog',
 'Bài viết đầu tiên trên blog của bạn',
 '<p>Đ<PERSON>y là bài viết đầu tiên trên blog của bạn. Hãy chỉnh sửa hoặc xóa nó để bắt đầu!</p>
 <p>Chúng tôi đã tạo sẵn một số danh mục và thẻ để bạn bắt đầu. Hãy khám phá và tùy chỉnh theo nhu cầu của bạn.</p>
 <p>Nếu cần hỗ trợ, hãy liên hệ với chúng tôi qua email hỗ trợ.</p>',
 'published', NOW(), NOW(), NOW()),
 
(@TENANT_ID, @ADMIN_ID, @ADMIN_ID, 'Hướng dẫn sử dụng Blog', 'huong-dan-su-dung-blog',
 'Hướng dẫn cơ bản để bắt đầu sử dụng blog của bạn',
 '<h2>Hướng dẫn cơ bản</h2>
 <p>Đây là hướng dẫn cơ bản để bắt đầu sử dụng blog của bạn:</p>
 <ol>
   <li>Đăng nhập vào trang quản trị</li>
   <li>Tạo các danh mục cho blog</li>
   <li>Thêm các bài viết mới</li>
   <li>Quản lý người dùng và phân quyền</li>
   <li>Thiết lập cấu hình blog</li>
 </ol>
 <p>Để biết thêm chi tiết, hãy tham khảo tài liệu đầy đủ.</p>',
 'published', NOW(), NOW(), NOW());
 
-- Gán danh mục cho bài viết mặc định (cần lấy post_id và category_id thực tế)
SET @POST_1 = LAST_INSERT_ID() - 1;
SET @POST_2 = LAST_INSERT_ID();

INSERT INTO blog_post_categories (tenant_id, post_id, category_id) VALUES
(@TENANT_ID, @POST_1, (SELECT category_id FROM blog_categories WHERE slug = 'tin-tuc' AND tenant_id = @TENANT_ID LIMIT 1)),
(@TENANT_ID, @POST_2, (SELECT category_id FROM blog_categories WHERE slug = 'giao-duc' AND tenant_id = @TENANT_ID LIMIT 1));

-- Gán tags cho bài viết mặc định
INSERT INTO blog_post_tags (tenant_id, post_id, tag_id) VALUES
(@TENANT_ID, @POST_1, (SELECT tag_id FROM blog_tags WHERE slug = 'laravel' AND tenant_id = @TENANT_ID LIMIT 1)),
(@TENANT_ID, @POST_1, (SELECT tag_id FROM blog_tags WHERE slug = 'php' AND tenant_id = @TENANT_ID LIMIT 1)),
(@TENANT_ID, @POST_2, (SELECT tag_id FROM blog_tags WHERE slug = 'marketing' AND tenant_id = @TENANT_ID LIMIT 1)); 