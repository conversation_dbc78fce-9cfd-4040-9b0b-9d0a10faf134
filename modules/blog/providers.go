package blog

import (
	"github.com/gin-gonic/gin"

	"wnapi/internal/middleware"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/internal/pkg/queue"
	"wnapi/modules/blog/api"
	"wnapi/modules/blog/internal"
	"wnapi/modules/blog/repository"
	"wnapi/modules/blog/repository/mysql"
	"wnapi/modules/blog/service"
	seoRepo "wnapi/modules/seo/repository/mysql"
	seoService "wnapi/modules/seo/service"
	socketService "wnapi/modules/integration/socket/service"
)

// NewBlogConfig creates blog configuration
func NewBlogConfig(cfg config.Config) (*internal.BlogConfig, error) {
	return internal.NewBlogConfigFromAppConfig(cfg)
}

// NewScheduleService creates schedule service
func NewScheduleService(
	scheduleRepo *mysql.ScheduleRepository,
	postRepo *mysql.PostRepository,
	queueClient queue.QueueClient,
	logger logger.Logger,
) service.ScheduleService {
	return service.NewScheduleService(scheduleRepo, postRepo, queueClient, logger)
}

// NewPostService creates post service
func NewPostService(
	postRepo *mysql.PostRepository,
	categoryRepo *mysql.CategoryRepository,
	tagRepo repository.TagRepository,
	scheduleService service.ScheduleService,
	socketSvc socketService.SocketService,
	logger logger.Logger,
) service.PostService {
	// Convert interface to concrete type
	tagRepoImpl := tagRepo.(*mysql.TagRepository)

	// Lấy *gorm.DB từ postRepo qua hàm export
	seoGormDB := postRepo.GetGormDB()
	seoRepoImpl := seoRepo.NewSeoMetaRepository(seoGormDB)
	seoMetaService := seoService.NewSeoMetaService(seoRepoImpl, logger)

	return service.NewPostService(*postRepo, *categoryRepo, *tagRepoImpl, scheduleService, seoMetaService, socketSvc)
}

// NewAuthorService creates author service
func NewAuthorService(authorRepo repository.AuthorRepository) service.AuthorService {
	// Convert interface to concrete type
	authorRepoImpl := authorRepo.(*mysql.AuthorRepository)
	return service.NewAuthorService(*authorRepoImpl)
}

// NewCategoryService creates category service
func NewCategoryService(categoryRepo *mysql.CategoryRepository) service.CategoryService {
	return service.NewCategoryService(*categoryRepo)
}

// NewTagService creates tag service
func NewTagService(tagRepo repository.TagRepository) service.TagService {
	// Convert interface to concrete type
	tagRepoImpl := tagRepo.(*mysql.TagRepository)
	return service.NewTagService(*tagRepoImpl)
}

// NewStatisticService creates statistic service
func NewStatisticService(statisticRepo repository.StatisticRepository) service.StatisticService {
	return service.NewStatisticService(statisticRepo)
}

// NewBlogBlockService creates blog block service
func NewBlogBlockService(
	blogBlockRepo repository.BlogBlockRepository,
	blogBlockPostRepo repository.BlogBlockPostRepository,
	postRepo *mysql.PostRepository,
) service.BlogBlockService {
	// Convert interface to concrete type
	blogBlockRepoImpl := blogBlockRepo.(*mysql.BlogBlockRepository)
	blogBlockPostRepoImpl := blogBlockPostRepo.(*mysql.BlogBlockPostRepository)
	return service.NewBlogBlockService(blogBlockRepoImpl, blogBlockPostRepoImpl, postRepo)
}

// NewBlogTimelineService creates blog timeline service
func NewBlogTimelineService(
	blogTimelineRepo repository.BlogTimelineRepository,
	blogTimelinePostRepo repository.BlogTimelinePostRepository,
	postRepo *mysql.PostRepository,
) service.BlogTimelineService {
	// Convert interface to concrete type
	blogTimelineRepoImpl := blogTimelineRepo.(*mysql.BlogTimelineRepository)
	blogTimelinePostRepoImpl := blogTimelinePostRepo.(*mysql.BlogTimelinePostRepository)
	return service.NewBlogTimelineService(blogTimelineRepoImpl, blogTimelinePostRepoImpl, postRepo)
}

// NewBlogHandler creates blog API handler
func NewBlogHandler(
	postService service.PostService,
	authorService service.AuthorService,
	categoryService service.CategoryService,
	tagService service.TagService,
	relatedPostService service.RelatedPostService,
	statisticService service.StatisticService,
	blogBlockService service.BlogBlockService,
	blogTimelineService service.BlogTimelineService,
	middlewareFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	log logger.Logger,
	tenantService middleware.TenantService,
	seoMetaService seoService.Service,
) *api.Handler {
	return api.NewHandler(postService, authorService, categoryService, tagService, relatedPostService, statisticService, blogBlockService, blogTimelineService, middlewareFactory, jwtService, log, tenantService, seoMetaService)
}

// RegisterBlogRoutes registers blog routes with gin.Engine
func RegisterBlogRoutes(handler *api.Handler, engine *gin.Engine, log logger.Logger) error {
	log.Info("Registering blog routes")

	// Register all blog routes using the handler's RegisterRoutesWithEngine method
	if err := handler.RegisterRoutesWithEngine(engine); err != nil {
		log.Error("Failed to register blog routes", "error", err)
		return err
	}

	log.Info("Blog routes registered successfully")
	return nil
}
