CREATE TABLE IF NOT EXISTS blog_authors (
  id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  website_id INT UNSIGNED NOT NULL,
  user_id INT UNSIGNED,
  display_name VA<PERSON>HA<PERSON>(100) NOT NULL,
  bio VARCHAR(255),
  avatar_url VARCHAR(255),
  email <PERSON><PERSON>HA<PERSON>(255),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_blog_author_tenant (tenant_id),
  INDEX idx_blog_author_user (user_id),
  INDEX idx_blog_author_active (is_active),
  INDEX idx_blog_author_website_tenant (website_id, tenant_id)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;