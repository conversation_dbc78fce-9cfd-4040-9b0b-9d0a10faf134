CREATE TABLE IF NOT EXISTS blog_categories (
  category_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  website_id INT UNSIGNED NOT NULL,
  parent_id INT UNSIGNED NULL,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  description TEXT,
  image VARCHAR(255),
  lft INT NOT NULL,            -- Left value for nested set
  rgt INT NOT NULL,            -- Right value for nested set
  depth INT NOT NULL DEFAULT 0, -- Depth in the tree
  position INT NOT NULL DEFAULT 0, -- Position among siblings
  is_active BOOLEAN DEFAULT TRUE,
  is_featured BOOLEAN DEFAULT FALSE,
  meta_title VARCHAR(100),
  meta_description VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by INT UNSIGNED,
  updated_by INT UNSIGNED,
  UNIQUE KEY unique_category_slug_tenant_website (tenant_id, website_id, slug),
  INDEX idx_category_tree (tenant_id, website_id, lft, rgt),
  INDEX idx_category_parent (parent_id),
  INDEX idx_category_active (is_active),
  INDEX idx_category_website_tenant (website_id, tenant_id)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;