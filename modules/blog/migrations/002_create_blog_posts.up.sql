CREATE TABLE IF NOT EXISTS blog_posts (
    post_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    content LONGTEXT,
    image VARCHAR(255),
    status ENUM('draft', 'pending', 'approved', 'schedule', 'published', 'return', 'trash', 'storage', 'request', 'auto', 'delete') DEFAULT 'draft',
    visibility ENUM('public', 'private', 'password_protected') DEFAULT 'public',
    password VARCHAR(255) NULL,
    comment_status ENUM('open', 'closed') DEFAULT 'open',
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    schedule_at TIMESTAMP NULL,
    author_id INT UNSIGNED NOT NULL,
    created_by INT UNSIGNED NOT NULL,
    
    -- Indexes for optimization
    UNIQUE KEY unique_blog_post_slug_tenant_website (tenant_id, website_id, slug),
    INDEX idx_blog_post_tenant_website_status (tenant_id, website_id, status),
    INDEX idx_blog_post_published (published_at),
    INDEX idx_blog_post_created_at (created_at),
    INDEX idx_blog_post_tenant_website_visibility_status (tenant_id, website_id, visibility, status),
    INDEX idx_blog_post_website_tenant (website_id, tenant_id)

) ENGINE=InnoDB 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
ROW_FORMAT=DYNAMIC;