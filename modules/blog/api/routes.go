package api

import (
	"wnapi/internal/middleware"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/blog/api/frontend"
	"wnapi/modules/blog/api/handlers"
	"wnapi/modules/blog/internal"
	"wnapi/modules/blog/service"
	seoService "wnapi/modules/seo/service"

	"github.com/gin-gonic/gin"
)

// Handler là đối tượng chính xử lý API cho module Blog
type Handler struct {
	// Blog handlers
	postHandler         *handlers.PostHandler
	authorHandler       *handlers.AuthorHandler
	categoryHandler     *handlers.CategoryHandler
	tagHandler          *handlers.TagHandler
	relatedPostHandler  *handlers.RelatedPostHandler
	statisticHandler    *handlers.StatisticHandler
	blogBlockHandler    *handlers.BlogBlockHandler
	blogTimelineHandler *handlers.BlogTimelineHandler

	// Services for frontend
	postService     service.PostService
	categoryService service.CategoryService

	// Dependencies
	middlewareFactory *permission.MiddlewareFactory
	jwtService        *auth.JWTService
	logger            logger.Logger
	tenantService     middleware.TenantService
}

// NewHandler tạo một handler mới với dependency injection
func NewHandler(
	postService service.PostService,
	authorService service.AuthorService,
	categoryService service.CategoryService,
	tagService service.TagService,
	relatedPostService service.RelatedPostService,
	statisticService service.StatisticService,
	blogBlockService service.BlogBlockService,
	blogTimelineService service.BlogTimelineService,
	middlewareFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	logger logger.Logger,
	tenantService middleware.TenantService,
	seoMetaService seoService.Service,
) *Handler {

	return &Handler{
		// Initialize all handlers with proper dependency injection
		postHandler:         handlers.NewPostHandler(postService, jwtService, seoMetaService),
		authorHandler:       handlers.NewAuthorHandler(authorService, jwtService),
		categoryHandler:     handlers.NewCategoryHandler(categoryService, jwtService),
		tagHandler:          handlers.NewTagHandler(tagService, jwtService),
		relatedPostHandler:  handlers.NewRelatedPostHandler(relatedPostService),
		statisticHandler:    handlers.NewStatisticHandler(statisticService, jwtService),
		blogBlockHandler:    handlers.NewBlogBlockHandler(blogBlockService, jwtService),
		blogTimelineHandler: handlers.NewBlogTimelineHandler(blogTimelineService, jwtService),

		// Store services for frontend routes
		postService:     postService,
		categoryService: categoryService,

		middlewareFactory: middlewareFactory,
		jwtService:        jwtService,
		logger:            logger,
		tenantService:     tenantService,
	}
}

// SetMiddlewareFactory sets the middleware factory for permission-based routes
func (h *Handler) SetMiddlewareFactory(mwFactory *permission.MiddlewareFactory) {
	h.middlewareFactory = mwFactory
}

// RegisterRoutesWithEngine đăng ký tất cả routes cho module Blog với gin.Engine
func (h *Handler) RegisterRoutesWithEngine(engine *gin.Engine) error {
	// Register routes directly with gin.Engine (duplicate of RegisterRoutes logic)
	return h.registerRoutesDirectly(engine)
}

// registerRoutesDirectly registers routes directly with gin.Engine (duplicate of RegisterRoutes logic)
func (h *Handler) registerRoutesDirectly(engine *gin.Engine) error {
	// ===== MAIN BLOG API GROUP =====
	apiGroup := engine.Group("/api/v1/blog")

	// Thêm middleware tracing cho tất cả các route blog
	apiGroup.Use(tracing.GinMiddleware("blog"))

	// Health check endpoint (public)
	apiGroup.GET("/healthy", h.healthCheck)

	// Public blog API endpoints
	postsPublicGroup := apiGroup.Group("/posts")
	{
		// Get related posts for a specific post
		postsPublicGroup.GET("/:id/related", h.relatedPostHandler.GetPostRelatedPosts)
	}

	// ===== ADMIN API GROUP =====
	adminApiGroup := engine.Group("/api/admin/v1/blog")
	adminApiGroup.Use(tracing.GinMiddleware("blog"))

	// Health check for admin
	adminApiGroup.GET("/healthy", h.healthCheck)

	// Protected routes - require authentication
	// Following middleware ordering: tenant -> auth -> rbac
	protected := adminApiGroup.Group("/")
	protected.Use(
		h.jwtService.JWTAuthMiddleware(),
		middleware.TenantMiddleware(h.tenantService),
	)
	{
		// ===== POSTS MANAGEMENT =====
		postsGroup := protected.Group("/posts")
		{
			// List posts - requires list permission
			postsGroup.GET("",
				h.middlewareFactory.RequirePermission(internal.ListPostPermission),
				h.postHandler.List,
			)

			// Get single post - requires read permission
			postsGroup.GET("/:id",
				h.middlewareFactory.RequirePermission(internal.ReadPostPermission),
				h.postHandler.Get,
			)

			// Get post by slug - requires read permission
			postsGroup.GET("/slug/:slug",
				h.middlewareFactory.RequirePermission(internal.ReadPostPermission),
				h.postHandler.GetBySlug,
			)

			// Create post - requires create permission
			postsGroup.POST("",
				h.middlewareFactory.RequirePermission(internal.CreatePostPermission),
				h.postHandler.Create,
			)

			// Update post - requires update permission
			postsGroup.PUT("/:id",
				h.middlewareFactory.RequirePermission(internal.UpdatePostPermission),
				h.postHandler.Update,
			)

			// Delete post - requires delete permission
			postsGroup.DELETE("/:id",
				h.middlewareFactory.RequirePermission(internal.DeletePostPermission),
				h.postHandler.Delete,
			)

			// Update post status - requires update permission
			postsGroup.PUT("/:id/status",
				h.middlewareFactory.RequirePermission(internal.UpdatePostPermission),
				h.postHandler.UpdateStatus,
			)

			// Get SEO metadata for a post - requires read permission
			postsGroup.GET("/:id/seo",
				h.middlewareFactory.RequirePermission(internal.ReadPostPermission),
				h.postHandler.GetSeo,
			)

			// Related posts for specific post - requires read permission
			postsGroup.GET("/:id/related-posts",
				h.middlewareFactory.RequirePermission(internal.ReadRelatedPostPermission),
				h.relatedPostHandler.GetPostRelatedPosts,
			)

			// Delete all related posts - requires delete permission
			postsGroup.DELETE("/:id/related-posts",
				h.middlewareFactory.RequirePermission(internal.DeleteRelatedPostPermission),
				h.relatedPostHandler.DeleteAllRelatedPosts,
			)

			// Update related posts (manual) - requires update permission
			postsGroup.POST("/:id/related",
				h.middlewareFactory.RequirePermission(internal.UpdateRelatedPostPermission),
				h.relatedPostHandler.UpdatePostRelatedPosts,
			)

			// Delete specific related post relationship - requires delete permission
			postsGroup.DELETE("/:id/related/:related_id",
				h.middlewareFactory.RequirePermission(internal.DeleteRelatedPostPermission),
				h.relatedPostHandler.DeletePostRelatedPost,
			)
		}

		// ===== RELATED POSTS MANAGEMENT =====
		relatedPostsGroup := protected.Group("/related-posts")
		{
			// List related posts - requires list permission
			relatedPostsGroup.GET("",
				h.middlewareFactory.RequirePermission(internal.ListRelatedPostPermission),
				h.relatedPostHandler.ListRelatedPosts,
			)

			// Get single related post - requires read permission
			relatedPostsGroup.GET("/:relation_id",
				h.middlewareFactory.RequirePermission(internal.ReadRelatedPostPermission),
				h.relatedPostHandler.GetRelatedPost,
			)

			// Create related post - requires create permission
			relatedPostsGroup.POST("",
				h.middlewareFactory.RequirePermission(internal.CreateRelatedPostPermission),
				h.relatedPostHandler.CreateRelatedPost,
			)

			// Update related post - requires update permission
			relatedPostsGroup.PUT("/:relation_id",
				h.middlewareFactory.RequirePermission(internal.UpdateRelatedPostPermission),
				h.relatedPostHandler.UpdateRelatedPost,
			)

			// Delete related post - requires delete permission
			relatedPostsGroup.DELETE("/:relation_id",
				h.middlewareFactory.RequirePermission(internal.DeleteRelatedPostPermission),
				h.relatedPostHandler.DeleteRelatedPost,
			)

			// Delete related posts by posts - requires delete permission
			relatedPostsGroup.DELETE("/by-posts",
				h.middlewareFactory.RequirePermission(internal.DeleteRelatedPostPermission),
				h.relatedPostHandler.DeleteRelatedPostByPosts,
			)

			// Bulk create related posts - requires create permission
			relatedPostsGroup.POST("/bulk",
				h.middlewareFactory.RequirePermission(internal.CreateRelatedPostPermission),
				h.relatedPostHandler.BulkCreateRelatedPosts,
			)
		}

		// ===== AUTHORS MANAGEMENT =====
		authorsGroup := protected.Group("/authors")
		{
			// List authors - requires list permission
			authorsGroup.GET("",
				h.middlewareFactory.RequirePermission(internal.ListAuthorPermission),
				h.authorHandler.List,
			)

			// Get single author - requires read permission
			authorsGroup.GET("/:id",
				h.middlewareFactory.RequirePermission(internal.ReadAuthorPermission),
				h.authorHandler.Get,
			)

			// Get author by user ID - requires read permission
			authorsGroup.GET("/user/:user_id",
				h.middlewareFactory.RequirePermission(internal.ReadAuthorPermission),
				h.authorHandler.GetByUserId,
			)

			// Create author - requires create permission
			authorsGroup.POST("",
				h.middlewareFactory.RequirePermission(internal.CreateAuthorPermission),
				h.authorHandler.Create,
			)

			// Update author - requires update permission
			authorsGroup.PUT("/:id",
				h.middlewareFactory.RequirePermission(internal.UpdateAuthorPermission),
				h.authorHandler.Update,
			)

			// Delete author - requires delete permission
			authorsGroup.DELETE("/:id",
				h.middlewareFactory.RequirePermission(internal.DeleteAuthorPermission),
				h.authorHandler.Delete,
			)
		}

		// ===== CATEGORIES MANAGEMENT =====
		categoriesGroup := protected.Group("/categories")
		{
			// List categories - requires list permission
			categoriesGroup.GET("",
				h.middlewareFactory.RequirePermission(internal.ListCategoryPermission),
				h.categoryHandler.List,
			)

			// Get single category - requires read permission
			categoriesGroup.GET("/:id",
				h.middlewareFactory.RequirePermission(internal.ReadCategoryPermission),
				h.categoryHandler.Get,
			)

			// Get category tree - requires read permission
			categoriesGroup.GET("/tree",
				h.middlewareFactory.RequirePermission(internal.ReadCategoryPermission),
				h.categoryHandler.GetTree,
			)

			// Get category subtree - requires read permission
			categoriesGroup.GET("/:id/subtree",
				h.middlewareFactory.RequirePermission(internal.ReadCategoryPermission),
				h.categoryHandler.GetSubtree,
			)

			// Create category - requires create permission
			categoriesGroup.POST("",
				h.middlewareFactory.RequirePermission(internal.CreateCategoryPermission),
				h.categoryHandler.Create,
			)

			// Update category - requires update permission
			categoriesGroup.PUT("/:id",
				h.middlewareFactory.RequirePermission(internal.UpdateCategoryPermission),
				h.categoryHandler.Update,
			)

			// Delete category - requires delete permission
			categoriesGroup.DELETE("/:id",
				h.middlewareFactory.RequirePermission(internal.DeleteCategoryPermission),
				h.categoryHandler.Delete,
			)

			// Category tree operations - require hierarchy management permission
			categoriesGroup.POST("/move",
				h.middlewareFactory.RequirePermission(internal.ManageCategoryHierarchyPermission),
				h.categoryHandler.MoveNode,
			)

			categoriesGroup.POST("/rebuild",
				h.middlewareFactory.RequirePermission(internal.ManageCategoryHierarchyPermission),
				h.categoryHandler.RebuildTree,
			)
		}

		// ===== TAGS MANAGEMENT =====
		tagsGroup := protected.Group("/tags")
		{
			// List tags - requires list permission
			tagsGroup.GET("",
				h.middlewareFactory.RequirePermission(internal.ListTagPermission),
				h.tagHandler.List,
			)

			// Get single tag - requires read permission
			tagsGroup.GET("/:id",
				h.middlewareFactory.RequirePermission(internal.ReadTagPermission),
				h.tagHandler.Get,
			)

			// Get tags with post count - requires read permission
			tagsGroup.GET("/with-count",
				h.middlewareFactory.RequirePermission(internal.ReadTagPermission),
				h.tagHandler.GetWithPostCount,
			)

			// Create tag - requires create permission
			tagsGroup.POST("",
				h.middlewareFactory.RequirePermission(internal.CreateTagPermission),
				h.tagHandler.Create,
			)

			// Update tag - requires update permission
			tagsGroup.PUT("/:id",
				h.middlewareFactory.RequirePermission(internal.UpdateTagPermission),
				h.tagHandler.Update,
			)

			// Delete tag - requires delete permission
			tagsGroup.DELETE("/:id",
				h.middlewareFactory.RequirePermission(internal.DeleteTagPermission),
				h.tagHandler.Delete,
			)
		}

		// ===== STATISTICS =====
		statisticsGroup := protected.Group("/statistics")
		{
			// Get status statistics - requires read statistics permission
			statisticsGroup.GET("/status",
				h.middlewareFactory.RequirePermission(internal.ReadStatisticsPermission),
				h.statisticHandler.GetStatusStatistics,
			)
		}

		// ===== BLOG BLOCKS MANAGEMENT =====
		blogBlocksGroup := protected.Group("/blog-blocks")
		{
			// List blog blocks - requires list permission
			blogBlocksGroup.GET("",
				h.middlewareFactory.RequirePermission(internal.ListBlogBlockPermission),
				h.blogBlockHandler.List,
			)

			// Get single blog block - requires read permission
			blogBlocksGroup.GET("/:id",
				h.middlewareFactory.RequirePermission(internal.ReadBlogBlockPermission),
				h.blogBlockHandler.Get,
			)

			// Get blog block by code - requires read permission
			blogBlocksGroup.GET("/code/:code",
				h.middlewareFactory.RequirePermission(internal.ReadBlogBlockPermission),
				h.blogBlockHandler.GetByCode,
			)

			// Create blog block - requires create permission
			blogBlocksGroup.POST("",
				h.middlewareFactory.RequirePermission(internal.CreateBlogBlockPermission),
				h.blogBlockHandler.Create,
			)

			// Update blog block - requires update permission
			blogBlocksGroup.PUT("/:id",
				h.middlewareFactory.RequirePermission(internal.UpdateBlogBlockPermission),
				h.blogBlockHandler.Update,
			)

			// Delete blog block - requires delete permission
			blogBlocksGroup.DELETE("/:id",
				h.middlewareFactory.RequirePermission(internal.DeleteBlogBlockPermission),
				h.blogBlockHandler.Delete,
			)

			// Blog Block Posts management
			blogBlocksGroup.POST("/:id/posts",
				h.middlewareFactory.RequirePermission(internal.UpdateBlogBlockPermission),
				h.blogBlockHandler.AddPost,
			)

			blogBlocksGroup.DELETE("/:id/posts/:postId",
				h.middlewareFactory.RequirePermission(internal.UpdateBlogBlockPermission),
				h.blogBlockHandler.RemovePost,
			)

			blogBlocksGroup.PUT("/:id/posts/:postId",
				h.middlewareFactory.RequirePermission(internal.UpdateBlogBlockPermission),
				h.blogBlockHandler.UpdatePost,
			)

			blogBlocksGroup.GET("/:id/posts",
				h.middlewareFactory.RequirePermission(internal.ReadBlogBlockPermission),
				h.blogBlockHandler.GetPosts,
			)

			blogBlocksGroup.POST("/:id/posts/batch",
				h.middlewareFactory.RequirePermission(internal.UpdateBlogBlockPermission),
				h.blogBlockHandler.BatchAddPosts,
			)

			blogBlocksGroup.PUT("/:id/posts/reorder",
				h.middlewareFactory.RequirePermission(internal.UpdateBlogBlockPermission),
				h.blogBlockHandler.ReorderPosts,
			)
		}

		// ===== BLOG TIMELINES MANAGEMENT =====
		blogTimelinesGroup := protected.Group("/blog-timelines")
		{
			// List blog timelines - requires list permission
			blogTimelinesGroup.GET("",
				h.middlewareFactory.RequirePermission(internal.ListBlogTimelinePermission),
				h.blogTimelineHandler.List,
			)

			// Get single blog timeline - requires read permission
			blogTimelinesGroup.GET("/:id",
				h.middlewareFactory.RequirePermission(internal.ReadBlogTimelinePermission),
				h.blogTimelineHandler.Get,
			)

			// Get blog timeline by code - requires read permission
			blogTimelinesGroup.GET("/code/:code",
				h.middlewareFactory.RequirePermission(internal.ReadBlogTimelinePermission),
				h.blogTimelineHandler.GetByCode,
			)

			// Create blog timeline - requires create permission
			blogTimelinesGroup.POST("",
				h.middlewareFactory.RequirePermission(internal.CreateBlogTimelinePermission),
				h.blogTimelineHandler.Create,
			)

			// Update blog timeline - requires update permission
			blogTimelinesGroup.PUT("/:id",
				h.middlewareFactory.RequirePermission(internal.UpdateBlogTimelinePermission),
				h.blogTimelineHandler.Update,
			)

			// Delete blog timeline - requires delete permission
			blogTimelinesGroup.DELETE("/:id",
				h.middlewareFactory.RequirePermission(internal.DeleteBlogTimelinePermission),
				h.blogTimelineHandler.Delete,
			)

			// Blog Timeline Posts management
			blogTimelinesGroup.POST("/:id/posts",
				h.middlewareFactory.RequirePermission(internal.UpdateBlogTimelinePermission),
				h.blogTimelineHandler.AddPost,
			)

			blogTimelinesGroup.DELETE("/:id/posts/:postId",
				h.middlewareFactory.RequirePermission(internal.UpdateBlogTimelinePermission),
				h.blogTimelineHandler.RemovePost,
			)

			blogTimelinesGroup.PUT("/:id/posts/:postId",
				h.middlewareFactory.RequirePermission(internal.UpdateBlogTimelinePermission),
				h.blogTimelineHandler.UpdatePost,
			)

			blogTimelinesGroup.GET("/:id/posts",
				h.middlewareFactory.RequirePermission(internal.ReadBlogTimelinePermission),
				h.blogTimelineHandler.GetPosts,
			)

			blogTimelinesGroup.POST("/:id/posts/batch",
				h.middlewareFactory.RequirePermission(internal.UpdateBlogTimelinePermission),
				h.blogTimelineHandler.BatchAddPosts,
			)

			blogTimelinesGroup.PUT("/:id/posts/reorder",
				h.middlewareFactory.RequirePermission(internal.UpdateBlogTimelinePermission),
				h.blogTimelineHandler.ReorderPosts,
			)
		}
	}

	// ===== FRONTEND API GROUP =====
	// Register frontend routes for public access
	frontendApiGroup := engine.Group("/api")
	frontendApiGroup.Use(tracing.GinMiddleware("blog-frontend"))
	frontend.RegisterRoutes(frontendApiGroup, h.postService, h.categoryService)

	return nil
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "blog",
		"message": "Blog module is running",
	})
}

// BlogHandler is an alias for Handler to maintain backward compatibility
type BlogHandler = Handler

// NewBlogHandler creates a new BlogHandler (alias for NewHandler)
func NewBlogHandler(
	postService service.PostService,
	authorService service.AuthorService,
	categoryService service.CategoryService,
	tagService service.TagService,
	relatedPostService service.RelatedPostService,
	statisticService service.StatisticService,
	blogBlockService service.BlogBlockService,
	blogTimelineService service.BlogTimelineService,
	middlewareFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	logger logger.Logger,
	tenantService middleware.TenantService,
	seoMetaService seoService.Service,
) *BlogHandler {
	return NewHandler(postService, authorService, categoryService, tagService, relatedPostService, statisticService, blogBlockService, blogTimelineService, middlewareFactory, jwtService, logger, tenantService, seoMetaService)
}
