package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/service"
	seoRequest "wnapi/modules/seo/dto/request"
	seoService "wnapi/modules/seo/service"
)

// PostHandler handles HTTP requests for blog posts
type PostHandler struct {
	postService service.PostService
	jwtService  *auth.JWTService
	seoService  seoService.Service
}

// NewPostHandler creates a new post handler instance
func NewPostHandler(postService service.PostService, jwtService *auth.JWTService, seoService seoService.Service) *PostHandler {
	return &PostHandler{
		postService: postService,
		jwtService:  jwtService,
		seoService:  seoService,
	}
}

// <PERSON><PERSON> handles the creation of a new blog post
func (h *PostHandler) Create(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Parse request body
	var req request.CreatePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service
	post, err := h.postService.CreatePost(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, post, nil)
}

// GetSeo handles retrieval of SEO metadata for a post by ID
func (h *PostHandler) GetSeo(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get post ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Post ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post ID", "MISSING_ID", details)
		return
	}

	// Convert string ID to uint
	postID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_ID", details)
		return
	}

	// Call SEO service to get SEO metadata for the post
	seoMeta, err := h.seoService.GetSeoMetaByTable(c.Request.Context(), seoRequest.GetSeoMetaByTableRequest{
		TableName: "blog_posts",
		TableID:   uint(postID),
	})
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, seoMeta, nil)
}

// Get handles retrieval of a post by ID
func (h *PostHandler) Get(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get post ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Post ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post ID", "MISSING_ID", details)
		return
	}

	// Convert string ID to uint
	postID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_ID", details)
		return
	}

	// Call service
	post, err := h.postService.GetPost(c.Request.Context(), tenantID, websiteID, uint(postID))
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, post, nil)
}

// GetBySlug handles retrieval of a post by slug
func (h *PostHandler) GetBySlug(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get slug from URL
	slug := c.Param("slug")
	if slug == "" {
		details := []interface{}{map[string]string{"message": "Post slug is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post slug", "MISSING_SLUG", details)
		return
	}

	// Call service
	post, err := h.postService.GetPostBySlug(c.Request.Context(), tenantID, websiteID, slug)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, post, nil)
}

// Update handles updating an existing blog post
func (h *PostHandler) Update(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get post ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Post ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post ID", "MISSING_ID", details)
		return
	}

	// Convert string ID to uint
	postID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.UpdatePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service
	post, err := h.postService.UpdatePost(c.Request.Context(), tenantID, websiteID, uint(postID), req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, post, nil)
}

// Delete handles deletion of a blog post
func (h *PostHandler) Delete(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get post ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Post ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post ID", "MISSING_ID", details)
		return
	}

	// Convert string ID to uint
	postID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_ID", details)
		return
	}

	// Call service
	err = h.postService.DeletePost(c.Request.Context(), tenantID, websiteID, uint(postID))
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, nil, nil)
}

// UpdateStatus handles updating only the status of a blog post
func (h *PostHandler) UpdateStatus(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get post ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Post ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post ID", "MISSING_ID", details)
		return
	}

	// Convert string ID to uint
	postID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.UpdatePostStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service
	post, err := h.postService.UpdatePostStatus(c.Request.Context(), tenantID, websiteID, uint(postID), req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, post, nil)
}

// List handles listing of blog posts with pagination
func (h *PostHandler) List(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Parse query parameters
	var req request.ListPostRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	// Call service
	result, err := h.postService.ListPosts(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response with pagination metadata
	meta := &response.Meta{
		NextCursor: result.Meta.NextCursor,
		HasMore:    result.Meta.HasMore,
	}
	response.Success(c, result.Data, meta)
}
