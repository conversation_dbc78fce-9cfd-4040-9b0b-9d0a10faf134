package examples

import (
	"context"
	"fmt"
	"time"

	"wnapi/modules/blog/dto"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/service"
)

// ScheduleExample demonstrates how to use the blog schedule functionality
type ScheduleExample struct {
	scheduleService service.ScheduleService
}

// NewScheduleExample creates a new schedule example
func NewScheduleExample(scheduleService service.ScheduleService) *ScheduleExample {
	return &ScheduleExample{
		scheduleService: scheduleService,
	}
}

// CreateScheduleExample demonstrates creating a blog schedule
func (e *ScheduleExample) CreateScheduleExample(ctx context.Context) error {
	fmt.Println("=== Creating Blog Schedule Example ===")

	// Schedule a blog post to be published in 1 hour
	scheduledAt := time.Now().Add(1 * time.Hour)

	schedule, err := e.scheduleService.CreateSchedule(
		ctx,
		1,           // tenantID
		123,         // blogID
		1,           // createdBy
		scheduledAt, // scheduledAt
		3,           // maxRetries
	)
	if err != nil {
		return fmt.Errorf("failed to create schedule: %w", err)
	}

	fmt.Printf("Schedule created successfully:\n")
	fmt.Printf("  ID: %d\n", schedule.ID)
	fmt.Printf("  Blog ID: %d\n", schedule.BlogID)
	fmt.Printf("  Scheduled At: %s\n", schedule.ScheduledAt.Format(time.RFC3339))
	fmt.Printf("  Status: %s\n", schedule.Status)
	fmt.Printf("  Max Retries: %d\n", schedule.MaxRetries)

	return nil
}

// ListSchedulesExample demonstrates listing blog schedules
func (e *ScheduleExample) ListSchedulesExample(ctx context.Context) error {
	fmt.Println("\n=== Listing Blog Schedules Example ===")

	// List pending schedules for tenant 1
	status := models.ScheduleStatusPending
	schedules, total, err := e.scheduleService.ListSchedules(
		ctx,
		1,       // tenantID
		&status, // status filter
		10,      // limit
		0,       // offset
	)
	if err != nil {
		return fmt.Errorf("failed to list schedules: %w", err)
	}

	fmt.Printf("Found %d pending schedules (total: %d):\n", len(schedules), total)
	for _, schedule := range schedules {
		fmt.Printf("  Schedule ID: %d, Blog ID: %d, Scheduled: %s, Status: %s\n",
			schedule.ID,
			schedule.BlogID,
			schedule.ScheduledAt.Format(time.RFC3339),
			schedule.Status,
		)
	}

	return nil
}

// CancelScheduleExample demonstrates cancelling a blog schedule
func (e *ScheduleExample) CancelScheduleExample(ctx context.Context, scheduleID uint) error {
	fmt.Printf("\n=== Cancelling Blog Schedule %d Example ===\n", scheduleID)

	err := e.scheduleService.CancelSchedule(ctx, 1, scheduleID)
	if err != nil {
		return fmt.Errorf("failed to cancel schedule: %w", err)
	}

	fmt.Printf("Schedule %d cancelled successfully\n", scheduleID)
	return nil
}

// RetryScheduleExample demonstrates retrying a failed blog schedule
func (e *ScheduleExample) RetryScheduleExample(ctx context.Context, scheduleID uint) error {
	fmt.Printf("\n=== Retrying Blog Schedule %d Example ===\n", scheduleID)

	err := e.scheduleService.RetrySchedule(ctx, 1, scheduleID)
	if err != nil {
		return fmt.Errorf("failed to retry schedule: %w", err)
	}

	fmt.Printf("Schedule %d retry initiated successfully\n", scheduleID)
	return nil
}

// ProcessPendingSchedulesExample demonstrates processing pending schedules
func (e *ScheduleExample) ProcessPendingSchedulesExample(ctx context.Context) error {
	fmt.Println("\n=== Processing Pending Schedules Example ===")

	err := e.scheduleService.ProcessPendingSchedules(ctx)
	if err != nil {
		return fmt.Errorf("failed to process pending schedules: %w", err)
	}

	fmt.Println("Pending schedules processed successfully")
	return nil
}

// ScheduleWorkflowExample demonstrates a complete workflow
func (e *ScheduleExample) ScheduleWorkflowExample(ctx context.Context) error {
	fmt.Println("\n=== Complete Schedule Workflow Example ===")

	// 1. Create a schedule
	fmt.Println("Step 1: Creating schedule...")
	scheduledAt := time.Now().Add(30 * time.Minute)
	schedule, err := e.scheduleService.CreateSchedule(ctx, 1, 456, 1, scheduledAt, 3)
	if err != nil {
		return fmt.Errorf("step 1 failed: %w", err)
	}
	fmt.Printf("  Created schedule ID: %d\n", schedule.ID)

	// 2. Get the schedule
	fmt.Println("Step 2: Retrieving schedule...")
	retrievedSchedule, err := e.scheduleService.GetSchedule(ctx, 1, schedule.ID)
	if err != nil {
		return fmt.Errorf("step 2 failed: %w", err)
	}
	fmt.Printf("  Retrieved schedule status: %s\n", retrievedSchedule.Status)

	// 3. List schedules
	fmt.Println("Step 3: Listing schedules...")
	schedules, total, err := e.scheduleService.ListSchedules(ctx, 1, nil, 10, 0)
	if err != nil {
		return fmt.Errorf("step 3 failed: %w", err)
	}
	fmt.Printf("  Found %d schedules (total: %d)\n", len(schedules), total)

	// 4. Cancel the schedule
	fmt.Println("Step 4: Cancelling schedule...")
	err = e.scheduleService.CancelSchedule(ctx, 1, schedule.ID)
	if err != nil {
		return fmt.Errorf("step 4 failed: %w", err)
	}
	fmt.Printf("  Schedule %d cancelled\n", schedule.ID)

	fmt.Println("Workflow completed successfully!")
	return nil
}

// DTOConversionExample demonstrates DTO conversions
func (e *ScheduleExample) DTOConversionExample() {
	fmt.Println("\n=== DTO Conversion Example ===")

	// Create a sample schedule model
	schedule := &models.BlogSchedule{
		ID:          1,
		TenantID:    1,
		BlogID:      123,
		ScheduledAt: time.Now().Add(1 * time.Hour),
		Status:      models.ScheduleStatusPending,
		RetryCount:  0,
		MaxRetries:  3,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		CreatedBy:   1,
	}

	// Convert to DTO
	scheduleResponse := dto.ConvertToScheduleResponse(schedule)

	fmt.Printf("Original model:\n")
	fmt.Printf("  ID: %d, Status: %s, Scheduled: %s\n",
		schedule.ID, schedule.Status, schedule.ScheduledAt.Format(time.RFC3339))

	fmt.Printf("Converted DTO:\n")
	fmt.Printf("  ID: %d, Status: %s, Scheduled: %s\n",
		scheduleResponse.ID, scheduleResponse.Status, scheduleResponse.ScheduledAt.Format(time.RFC3339))

	// Create list response
	schedules := []*models.BlogSchedule{schedule}
	listResponse := dto.ConvertToScheduleListResponse(schedules, 1, 1, 10)

	fmt.Printf("List response:\n")
	fmt.Printf("  Data count: %d\n", len(listResponse.Data))
	fmt.Printf("  Total: %d, Page: %d, Per page: %d\n",
		listResponse.Pagination.Total,
		listResponse.Pagination.CurrentPage,
		listResponse.Pagination.PerPage)
}

// RunAllExamples runs all the examples
func (e *ScheduleExample) RunAllExamples(ctx context.Context) error {
	fmt.Println("Running all blog schedule examples...")

	// Note: These examples assume you have valid blog posts and proper setup
	// In a real scenario, you would need to create blog posts first

	if err := e.CreateScheduleExample(ctx); err != nil {
		return err
	}

	if err := e.ListSchedulesExample(ctx); err != nil {
		return err
	}

	if err := e.ProcessPendingSchedulesExample(ctx); err != nil {
		return err
	}

	// DTO conversion doesn't need context
	e.DTOConversionExample()

	fmt.Println("\nAll examples completed successfully!")
	return nil
}
