package blog

import (
	"go.uber.org/fx"

	"wnapi/internal/fx/modules"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/blog/repository"
	"wnapi/modules/blog/repository/mysql"
	"wnapi/modules/blog/service"
	seoRepo "wnapi/modules/seo/repository/mysql"
	seoService "wnapi/modules/seo/service"
)

func init() {
	modules.GlobalRegistry.Register(&BlogModule{})
}

// BlogModule implements the FX module interface
type BlogModule struct{}

// Name returns the module name
func (m *BlogModule) Name() string {
	return "blog"
}

// Dependencies returns module dependencies
func (m *BlogModule) Dependencies() []string {
	return []string{"tenant", "auth", "rbac", "socket"}
}

// Priority returns module loading priority
func (m *BlogModule) Priority() int {
	return 50 // Load after auth, tenant, notification, and media
}

// Enabled returns whether the module is enabled
func (m *BlogModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default to enabled
}

// GetMigrationPath returns path to module migrations
func (m *BlogModule) GetMigrationPath() string {
	return "modules/blog/migrations"
}

// GetMigrationOrder returns migration priority order
func (m *BlogModule) GetMigrationOrder() int {
	return 50 // Blog module runs after auth, tenant, notification
}

// Module returns FX options for the module
func (m *BlogModule) Module() fx.Option {
	return fx.Module("blog",
		// Providers
		fx.Provide(
			// Configuration
			NewBlogConfig,

			// Repositories - using concrete types
			mysql.NewPostRepository,
			fx.Annotate(mysql.NewAuthorRepository, fx.As(new(repository.AuthorRepository))),
			mysql.NewCategoryRepository,
			fx.Annotate(mysql.NewTagRepository, fx.As(new(repository.TagRepository))),
			mysql.NewScheduleRepository,
			fx.Annotate(mysql.NewRelatedPostRepository, fx.As(new(repository.RelatedPostRepository))),
			fx.Annotate(mysql.NewStatisticRepository, fx.As(new(repository.StatisticRepository))),
			fx.Annotate(mysql.NewBlogBlockRepository, fx.As(new(repository.BlogBlockRepository))),
			fx.Annotate(mysql.NewBlogBlockPostRepository, fx.As(new(repository.BlogBlockPostRepository))),
			fx.Annotate(mysql.NewBlogTimelineRepository, fx.As(new(repository.BlogTimelineRepository))),
			fx.Annotate(mysql.NewBlogTimelinePostRepository, fx.As(new(repository.BlogTimelinePostRepository))),

			// Services
			NewScheduleService,
			NewPostService,
			NewAuthorService,
			NewCategoryService,
			NewTagService,
			service.NewRelatedPostService,
			NewStatisticService,
			NewBlogBlockService,
			NewBlogTimelineService,
			
			// Provide SEO service
			func(postRepo *mysql.PostRepository, logger logger.Logger) seoService.Service {
				seoGormDB := postRepo.GetGormDB()
				seoRepoImpl := seoRepo.NewSeoMetaRepository(seoGormDB)
				return seoService.NewSeoMetaService(seoRepoImpl, logger)
			},

			// API Handler
			NewBlogHandler,
		),

		// Route registration
		fx.Invoke(RegisterBlogRoutes),
	)
}
