package models

import (
	"time"

	"wnapi/modules/blog/internal"
)

// BlogPostStatus represents the status enum for blog posts
type BlogPostStatus string

const (
	StatusDraft     BlogPostStatus = "draft"
	StatusPending   BlogPostStatus = "pending"
	StatusApproved  BlogPostStatus = "approved"
	StatusSchedule  BlogPostStatus = "schedule"
	StatusPublished BlogPostStatus = "published"
	StatusReturn    BlogPostStatus = "return"
	StatusTrash     BlogPostStatus = "trash"
	StatusStorage   BlogPostStatus = "storage"
	StatusRequest   BlogPostStatus = "request"
	StatusAuto      BlogPostStatus = "auto"
	StatusDelete    BlogPostStatus = "delete"
)

// BlogPostVisibility represents the visibility enum for blog posts
type BlogPostVisibility string

const (
	VisibilityPublic            BlogPostVisibility = "public"
	VisibilityPrivate           BlogPostVisibility = "private"
	VisibilityPasswordProtected BlogPostVisibility = "password_protected"
)

// BlogPostCommentStatus represents the comment status enum for blog posts
type BlogPostCommentStatus string

const (
	CommentStatusOpen   BlogPostCommentStatus = "open"
	CommentStatusClosed BlogPostCommentStatus = "closed"
)

// BlogPost represents the database model for blog posts
type BlogPost struct {
	PostID        uint                  `gorm:"column:post_id;primaryKey;autoIncrement" json:"post_id"`
	TenantID      uint                  `gorm:"column:tenant_id;not null" json:"tenant_id"`
	WebsiteID     uint                  `gorm:"column:website_id;not null" json:"website_id"`
	Title         string                `gorm:"column:title;size:255;not null" json:"title"`
	Slug          string                `gorm:"column:slug;size:255;not null" json:"slug"`
	Description   *string               `gorm:"column:description;type:text" json:"description"`
	Content       *string               `gorm:"column:content;type:longtext" json:"content"`
	Image         *string               `gorm:"column:image;size:255" json:"image"`
	Status        BlogPostStatus        `gorm:"column:status;type:enum('draft','pending','approved','schedule','published','return','trash','storage','request','auto','delete');default:draft" json:"status"`
	Visibility    BlogPostVisibility    `gorm:"column:visibility;type:enum('public','private','password_protected');default:public" json:"visibility"`
	Password      *string               `gorm:"column:password;size:255" json:"password,omitempty"`
	CommentStatus BlogPostCommentStatus `gorm:"column:comment_status;type:enum('open','closed');default:open" json:"comment_status"`
	PublishedAt   *time.Time            `gorm:"column:published_at" json:"published_at"`
	CreatedAt     time.Time             `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time             `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	ScheduleAt    time.Time             `gorm:"column:schedule_at;default:CURRENT_TIMESTAMP" json:"schedule_at"`
	AuthorID      uint                  `gorm:"column:author_id;not null" json:"author_id"`
	CreatedBy     uint                  `gorm:"column:created_by;not null" json:"created_by"`
}

// TableName sets the insert table name for this struct type
func (BlogPost) TableName() string {
	return "blog_posts"
}

// ToInternal converts a database model to internal model
func (b *BlogPost) ToInternal() *internal.BlogPost {
	var content, description string
	if b.Content != nil {
		content = *b.Content
	}
	if b.Description != nil {
		description = *b.Description
	}

	return &internal.BlogPost{
		PostID:        b.PostID,
		TenantID:      b.TenantID,
		WebsiteID:     b.WebsiteID,
		Title:         b.Title,
		Slug:          b.Slug,
		Description:   description,
		Content:       content,
		Image:         b.Image,
		Status:        string(b.Status),
		Visibility:    string(b.Visibility),
		Password:      b.Password,
		CommentStatus: string(b.CommentStatus),
		PublishedAt:   b.PublishedAt,
		CreatedAt:     b.CreatedAt,
		UpdatedAt:     b.UpdatedAt,
		ScheduleAt:    b.ScheduleAt,
		AuthorID:      b.AuthorID,
		CreatedBy:     b.CreatedBy,
	}
}

// BlogPostFromInternal converts an internal model to database model
func BlogPostFromInternal(post *internal.BlogPost) *BlogPost {
	var content, description *string
	if post.Content != "" {
		content = &post.Content
	}
	if post.Description != "" {
		description = &post.Description
	}

	return &BlogPost{
		PostID:        post.PostID,
		TenantID:      post.TenantID,
		WebsiteID:     post.WebsiteID,
		Title:         post.Title,
		Slug:          post.Slug,
		Description:   description,
		Content:       content,
		Image:         post.Image,
		Status:        BlogPostStatus(post.Status),
		Visibility:    BlogPostVisibility(post.Visibility),
		Password:      post.Password,
		CommentStatus: BlogPostCommentStatus(post.CommentStatus),
		PublishedAt:   post.PublishedAt,
		CreatedAt:     post.CreatedAt,
		UpdatedAt:     post.UpdatedAt,
		ScheduleAt:    post.ScheduleAt,
		AuthorID:      post.AuthorID,
		CreatedBy:     post.CreatedBy,
	}
}
