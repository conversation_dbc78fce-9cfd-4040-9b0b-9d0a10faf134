package models

import (
	"time"
)

// Category represents a blog category using the Nested Set Model
type Category struct {
	CategoryID      uint      `db:"category_id" json:"category_id" gorm:"primaryKey"`
	TenantID        uint      `db:"tenant_id" json:"tenant_id"`
	WebsiteID       uint      `db:"website_id" json:"website_id"`
	ParentID        *uint     `db:"parent_id" json:"parent_id"`
	Name            string    `db:"name" json:"name"`
	Slug            string    `db:"slug" json:"slug"`
	Description     string    `db:"description" json:"description"`
	FeaturedImage   string    `db:"image" json:"image"`
	Left            int       `db:"lft" json:"lft"`           // Nested set left value
	Right           int       `db:"rgt" json:"rgt"`           // Nested set right value
	Depth           int       `db:"depth" json:"depth"`       // Depth in tree
	Position        int       `db:"position" json:"position"` // Position among siblings
	IsActive        bool      `db:"is_active" json:"is_active"`
	IsFeatured      bool      `db:"is_featured" json:"is_featured"`
	MetaTitle       string    `db:"meta_title" json:"meta_title"`
	MetaDescription string    `db:"meta_description" json:"meta_description"`
	CreatedAt       time.Time `db:"created_at" json:"created_at"`
	UpdatedAt       time.Time `db:"updated_at" json:"updated_at"`
	CreatedBy       uint      `db:"created_by" json:"created_by"`
	UpdatedBy       uint      `db:"updated_by" json:"updated_by"`

	// Virtual fields (not stored in DB)
	Children  []*Category `db:"-" json:"children,omitempty"`   // For tree display
	PostCount int         `db:"-" json:"post_count,omitempty"` // Number of posts
}
