package models

import (
	"time"
)

// Post status constants
const (
	STATUS_DRAFT     = "draft"
	STATUS_PENDING   = "pending"
	STATUS_APPROVED  = "approved"
	STATUS_SCHEDULE  = "schedule"
	STATUS_PUBLISHED = "published"
	STATUS_RETURN    = "return"
	STATUS_TRASH     = "trash"
	STATUS_STORAGE   = "storage"
	STATUS_REQUEST   = "request"
	STATUS_AUTO      = "auto"
	STATUS_DELETE    = "delete"
)

// Post visibility constants
const (
	VISIBILITY_PUBLIC = "public"
)

type Post struct {
	PostID        uint       `db:"post_id" json:"post_id" gorm:"column:post_id;primaryKey"`
	TenantID      uint       `db:"tenant_id" json:"tenant_id" gorm:"column:tenant_id"`
	Title         string     `db:"title" json:"title" gorm:"column:title"`
	Slug          string     `db:"slug" json:"slug" gorm:"column:slug"`
	Description   string     `db:"description" json:"description" gorm:"column:description"`
	Content       string     `db:"content" json:"content" gorm:"column:content"`
	Image         string     `db:"image" json:"image" gorm:"column:image"`
	Status        string     `db:"status" json:"status" gorm:"column:status"`
	Visibility    string     `db:"visibility" json:"visibility" gorm:"column:visibility"`
	Password      *string    `db:"password" json:"password,omitempty" gorm:"column:password"`
	CommentStatus string     `db:"comment_status" json:"comment_status" gorm:"column:comment_status"`
	PublishedAt   *time.Time `db:"published_at" json:"published_at,omitempty" gorm:"column:published_at"`
	CreatedAt     time.Time  `db:"created_at" json:"created_at" gorm:"column:created_at"`
	UpdatedAt     time.Time  `db:"updated_at" json:"updated_at" gorm:"column:updated_at"`
	ScheduleAt    *time.Time `db:"schedule_at" json:"schedule_at,omitempty" gorm:"column:schedule_at"`
	AuthorID      uint       `db:"author_id" json:"author_id" gorm:"column:author_id"`
	CreatedBy     uint       `db:"created_by" json:"created_by" gorm:"column:created_by"`

	CategoryIDs []uint `db:"-" gorm:"-" json:"category_ids,omitempty"`
	TagIDs      []uint `db:"-" gorm:"-" json:"tag_ids,omitempty"`
	Author      *User  `db:"-" gorm:"-" json:"author,omitempty"`
	Editor      *User  `db:"-" gorm:"-" json:"editor,omitempty"`
}

// TableName returns the table name for the Post model
func (Post) TableName() string {
	return "blog_posts"
}

type User struct {
	UserID    uint   `db:"user_id" json:"user_id" gorm:"primaryKey"`
	Username  string `db:"username" json:"username"`
	Email     string `db:"email" json:"email"`
	FirstName string `db:"first_name" json:"first_name"`
	LastName  string `db:"last_name" json:"last_name"`
	AvatarURL string `db:"avatar_url" json:"avatar_url"`
}

func (User) TableName() string {
	return "users"
}
