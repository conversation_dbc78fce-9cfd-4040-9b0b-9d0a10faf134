package models

import "time"

// BlogAuthor represents a blog author
type BlogAuthor struct {
	ID          uint      `db:"id" json:"id" gorm:"primaryKey"`
	TenantID    uint      `db:"tenant_id" json:"tenant_id"`
	WebsiteID   uint      `db:"website_id" json:"website_id"`
	UserID      uint      `db:"user_id" json:"user_id"`
	DisplayName string    `db:"display_name" json:"display_name"`
	Bio         string    `db:"bio" json:"bio"`
	AvatarURL   string    `db:"avatar_url" json:"avatar_url"`
	Email       string    `db:"email" json:"email"`
	IsActive    bool      `db:"is_active" json:"is_active"`
	CreatedAt   time.Time `db:"created_at" json:"created_at"`
	UpdatedAt   time.Time `db:"updated_at" json:"updated_at"`
}
