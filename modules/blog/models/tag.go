package models

import "time"

// Tag represents a blog tag
type Tag struct {
	TagID       uint      `db:"tag_id" json:"tag_id" gorm:"primaryKey"`
	TenantID    uint      `db:"tenant_id" json:"tenant_id"`
	WebsiteID   uint      `db:"website_id" json:"website_id"`
	Name        string    `db:"name" json:"name"`
	Slug        string    `db:"slug" json:"slug"`
	Description string    `db:"description" json:"description"`
	IsActive    bool      `db:"is_active" json:"is_active"`
	CreatedAt   time.Time `db:"created_at" json:"created_at"`
	UpdatedAt   time.Time `db:"updated_at" json:"updated_at"`

	// Virtual fields
	//PostCount int `db:"-" json:"post_count,omitempty"`
}

func (Tag) TableName() string {
	return "blog_tags"
}
