package internal

import (
	"net/http"
	"time"
)

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrBlogNotFound là lỗi khi không tìm thấy bài viết
	ErrBlogNotFound ServiceError = "blog_not_found"
	// ErrInvalidBlogData là lỗi khi dữ liệu bài viết không hợp lệ
	ErrInvalidBlogData ServiceError = "invalid_blog_data"
	// ErrTitleTooLong là lỗi khi tiêu đề quá dài
	ErrTitleTooLong ServiceError = "title_too_long"
	// ErrContentTooLong là lỗi khi nội dung quá dài
	ErrContentTooLong ServiceError = "content_too_long"
	// ErrDatabaseError là lỗi khi tương tác với database
	ErrDatabaseError ServiceError = "database_error"
	// ErrInvalidSortField là lỗi khi trường sắp xếp không hợp lệ
	ErrInvalidSortField ServiceError = "invalid_sort_field"
)

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// ErrorMap ánh xạ ServiceError với thông tin phản hồi lỗi tương ứng
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrBlogNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy bài viết",
		ErrorCode:  "BLOG_NOT_FOUND",
	},
	ErrInvalidBlogData: {
		StatusCode: http.StatusBadRequest,
		Message:    "Dữ liệu bài viết không hợp lệ",
		ErrorCode:  "INVALID_BLOG_DATA",
	},
	ErrTitleTooLong: {
		StatusCode: http.StatusBadRequest,
		Message:    "Tiêu đề bài viết quá dài",
		ErrorCode:  "TITLE_TOO_LONG",
	},
	ErrContentTooLong: {
		StatusCode: http.StatusBadRequest,
		Message:    "Nội dung bài viết quá dài",
		ErrorCode:  "CONTENT_TOO_LONG",
	},
	ErrDatabaseError: {
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi cơ sở dữ liệu",
		ErrorCode:  "DATABASE_ERROR",
	},
	ErrInvalidSortField: {
		StatusCode: http.StatusBadRequest,
		Message:    "Trường sắp xếp không hợp lệ",
		ErrorCode:  "INVALID_SORT_FIELD",
	},
}

func (e ServiceError) Error() string {
	return string(e)
}

// GetErrorResponse trả về thông tin phản hồi lỗi dựa trên ServiceError
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if resp, exists := ErrorMap[serviceErr]; exists {
			return resp
		}
	}

	// Mặc định trả về lỗi hệ thống nếu không tìm thấy lỗi trong map
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống",
		ErrorCode:  "INTERNAL_ERROR",
	}
}

// BlogPost định nghĩa cấu trúc dữ liệu cho bài viết
type BlogPost struct {
	PostID        uint       `json:"post_id"`
	TenantID      uint       `json:"tenant_id"`
	WebsiteID     uint       `json:"website_id"`
	Title         string     `json:"title"`
	Slug          string     `json:"slug"`
	Description   string     `json:"description"`
	Content       string     `json:"content"`
	Image         *string    `json:"image"`
	Status        string     `json:"status"`
	Visibility    string     `json:"visibility"`
	Password      *string    `json:"password,omitempty"`
	CommentStatus string     `json:"comment_status"`
	PublishedAt   *time.Time `json:"published_at"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	ScheduleAt    time.Time  `json:"schedule_at"`
	AuthorID      uint       `json:"author_id"`
	CreatedBy     uint       `json:"created_by"`
}

// TableName sets the insert table name for this struct type
func (BlogPost) TableName() string {
	return "blog_posts"
}

// Note: Repository and Service interfaces have been moved to avoid circular dependencies
// They can be defined in the service or repository packages directly if needed
