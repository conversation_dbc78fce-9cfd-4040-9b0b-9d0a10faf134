package service

import (
	"context"
	"time"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/dto/response"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository/mysql"
)

// AuthorService defines the interface for author service operations
type AuthorService interface {
	CreateAuthor(ctx context.Context, tenantID, websiteID uint, req request.CreateAuthorRequest) (*response.AuthorResponse, error)
	GetAuthor(ctx context.Context, tenantID, websiteID uint, authorID uint) (*response.AuthorResponse, error)
	GetAuthorByUserId(ctx context.Context, tenantID, websiteID uint, userID uint) (*response.AuthorResponse, error)
	UpdateAuthor(ctx context.Context, tenantID, websiteID uint, authorID uint, req request.UpdateAuthorRequest) (*response.AuthorResponse, error)
	DeleteAuthor(ctx context.Context, tenantID, websiteID uint, authorID uint) error
	ListAuthors(ctx context.Context, tenantID, websiteID uint, req request.ListAuthorRequest) (*response.AuthorListResponse, error)
}

// authorService implements the AuthorService interface
type authorService struct {
	authorRepo mysql.AuthorRepository
}

// NewAuthorService creates a new instance of AuthorService
func NewAuthorService(authorRepo mysql.AuthorRepository) AuthorService {
	return &authorService{
		authorRepo: authorRepo,
	}
}

// CreateAuthor creates a new author
func (s *authorService) CreateAuthor(ctx context.Context, tenantID, websiteID uint, req request.CreateAuthorRequest) (*response.AuthorResponse, error) {
	// Create author model from request
	isActive := true
	if req.IsActive != nil {
		isActive = *req.IsActive
	}

	author := &models.BlogAuthor{
		TenantID:    tenantID,
		WebsiteID:   websiteID,
		UserID:      req.UserID,
		DisplayName: req.DisplayName,
		Bio:         req.Bio,
		AvatarURL:   req.AvatarURL,
		Email:       req.Email,
		IsActive:    isActive,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Create author in repository
	if err := s.authorRepo.Create(ctx, author); err != nil {
		return nil, err
	}

	// Get the created author with all fields populated
	createdAuthor, err := s.authorRepo.GetByID(ctx, tenantID, websiteID, author.ID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	resp := response.FromAuthorModel(*createdAuthor)
	return &resp, nil
}

// GetAuthor retrieves an author by ID
func (s *authorService) GetAuthor(ctx context.Context, tenantID, websiteID uint, authorID uint) (*response.AuthorResponse, error) {
	// Get from repository
	author, err := s.authorRepo.GetByID(ctx, tenantID, websiteID, authorID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	resp := response.FromAuthorModel(*author)
	return &resp, nil
}

// GetAuthorByUserId retrieves an author by user ID
func (s *authorService) GetAuthorByUserId(ctx context.Context, tenantID, websiteID uint, userID uint) (*response.AuthorResponse, error) {
	// Get from repository
	author, err := s.authorRepo.GetByUserID(ctx, tenantID, websiteID, userID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	resp := response.FromAuthorModel(*author)
	return &resp, nil
}

// UpdateAuthor updates an existing author
func (s *authorService) UpdateAuthor(ctx context.Context, tenantID, websiteID uint, authorID uint, req request.UpdateAuthorRequest) (*response.AuthorResponse, error) {
	// Get existing author
	existingAuthor, err := s.authorRepo.GetByID(ctx, tenantID, websiteID, authorID)
	if err != nil {
		return nil, err
	}

	// Update fields if provided
	if req.DisplayName != "" {
		existingAuthor.DisplayName = req.DisplayName
	}

	if req.Bio != "" {
		existingAuthor.Bio = req.Bio
	}

	if req.AvatarURL != "" {
		existingAuthor.AvatarURL = req.AvatarURL
	}

	if req.Email != "" {
		existingAuthor.Email = req.Email
	}

	if req.IsActive != nil {
		existingAuthor.IsActive = *req.IsActive
	}

	existingAuthor.UpdatedAt = time.Now()

	// Update author in repository
	if err := s.authorRepo.Update(ctx, existingAuthor); err != nil {
		return nil, err
	}

	// Get updated author
	updatedAuthor, err := s.authorRepo.GetByID(ctx, tenantID, websiteID, authorID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	resp := response.FromAuthorModel(*updatedAuthor)
	return &resp, nil
}

// DeleteAuthor deletes an author
func (s *authorService) DeleteAuthor(ctx context.Context, tenantID, websiteID uint, authorID uint) error {
	return s.authorRepo.Delete(ctx, tenantID, websiteID, authorID)
}

// ListAuthors lists authors with pagination
func (s *authorService) ListAuthors(ctx context.Context, tenantID, websiteID uint, req request.ListAuthorRequest) (*response.AuthorListResponse, error) {
	// Get from repository with pagination
	authors, nextCursor, hasMore, err := s.authorRepo.List(ctx, tenantID, websiteID, req)
	if err != nil {
		return nil, err
	}

	// Convert to response
	authorResponses := make([]response.AuthorResponse, len(authors))
	for i, author := range authors {
		authorResponses[i] = response.FromAuthorModel(*author)
	}

	// Create list response
	result := &response.AuthorListResponse{
		Authors: <AUTHORS>
	}
	result.Meta.NextCursor = nextCursor
	result.Meta.HasMore = hasMore

	return result, nil
}
