package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gosimple/slug"
	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"wnapi/internal/pkg/pagination"
	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository"
)

// TagRepository implements the repository.TagRepository interface
type TagRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewTagRepository creates a new instance of TagRepository
func NewTagRepository(db *sqlx.DB) repository.TagRepository {
	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: db.DB,
	}), &gorm.Config{})
	if err != nil {
		// If GORM fails, log error but continue with sqlx only
		fmt.Printf("Warning: Failed to initialize GORM for tag repository: %v\n", err)
		gormDB = nil
	}

	return &TagRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create inserts a new tag into the database using GORM
func (r *TagRepository) Create(ctx context.Context, tag *models.Tag) error {
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for Create operation")
	}

	// Generate slug if not provided
	if tag.Slug == "" {
		tag.Slug = slug.Make(tag.Name)
	}

	// Set default values
	if tag.IsActive == false {
		tag.IsActive = true
	}

	now := time.Now()
	tag.CreatedAt = now
	tag.UpdatedAt = now

	// Create tag using GORM
	result := r.gormDB.WithContext(ctx).Create(tag)
	if result.Error != nil {
		return fmt.Errorf("failed to create tag: %w", result.Error)
	}

	return nil
}

// GetByID fetches a tag by ID and tenant
func (r *TagRepository) GetByID(ctx context.Context, tenantID, websiteID, tagID uint) (*models.Tag, error) {
	var tag models.Tag

	query := `
		SELECT tag_id, tenant_id, website_id, name, slug, description, is_active, created_at, updated_at
		FROM blog_tags
		WHERE tag_id = ? AND tenant_id = ? AND website_id = ?
	`

	err := r.db.GetContext(ctx, &tag, query, tagID, tenantID, websiteID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("tag not found")
		}
		return nil, fmt.Errorf("failed to get tag: %w", err)
	}

	return &tag, nil
}

// GetBySlug fetches a tag by slug and tenant
func (r *TagRepository) GetBySlug(ctx context.Context, tenantID uint, websiteID uint, slug string) (*models.Tag, error) {
	var tag models.Tag

	query := `
		SELECT tag_id, tenant_id, website_id, name, slug, description, is_active, created_at, updated_at
		FROM blog_tags
		WHERE slug = ? AND tenant_id = ? AND website_id = ?
	`

	err := r.db.GetContext(ctx, &tag, query, slug, tenantID, websiteID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("tag not found")
		}
		return nil, fmt.Errorf("failed to get tag by slug: %w", err)
	}

	return &tag, nil
}

// Update updates an existing tag
func (r *TagRepository) Update(ctx context.Context, tag *models.Tag) error {
	// Check if tag exists
	var existingTag models.Tag
	checkQuery := `
		SELECT tag_id FROM blog_tags
		WHERE tag_id = ? AND tenant_id = ? AND website_id = ?
	`
	err := r.db.GetContext(ctx, &existingTag, checkQuery, tag.TagID, tag.TenantID, tag.WebsiteID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("tag not found")
		}
		return fmt.Errorf("failed to check tag existence: %w", err)
	}

	// Generate slug if not provided
	if tag.Slug == "" {
		tag.Slug = slug.Make(tag.Name)
	}

	tag.UpdatedAt = time.Now()

	// Update tag
	query := `
		UPDATE blog_tags
		SET name = ?, slug = ?, description = ?, is_active = ?, updated_at = ?
		WHERE tag_id = ? AND tenant_id = ? AND website_id = ?
	`

	_, err = r.db.ExecContext(
		ctx,
		query,
		tag.Name,
		tag.Slug,
		tag.Description,
		tag.IsActive,
		tag.UpdatedAt,
		tag.TagID,
		tag.TenantID,
		tag.WebsiteID,
	)
	if err != nil {
		return fmt.Errorf("failed to update tag: %w", err)
	}

	return nil
}

// Delete removes a tag by ID and tenant
func (r *TagRepository) Delete(ctx context.Context, tenantID, websiteID, tagID uint) error {
	query := "DELETE FROM blog_tags WHERE tag_id = ? AND tenant_id = ? AND website_id = ?"
	result, err := r.db.ExecContext(ctx, query, tagID, tenantID, websiteID)
	if err != nil {
		return fmt.Errorf("failed to delete tag: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("tag not found")
	}

	return nil
}

// List fetches tags with cursor pagination
func (r *TagRepository) List(ctx context.Context, tenantID, websiteID uint, req request.ListTagRequest) ([]*models.Tag, string, bool, error) {
	// Default limit if not specified
	limit := req.Limit
	if limit <= 0 {
		limit = 10
	}

	// Build WHERE clause
	whereConditions := []string{"tenant_id = ?", "website_id = ?"}
	args := []interface{}{tenantID, websiteID}

	if req.IsActive != nil {
		whereConditions = append(whereConditions, "is_active = ?")
		args = append(args, *req.IsActive)
	}

	if req.Search != "" {
		whereConditions = append(whereConditions, "(name LIKE ? OR slug LIKE ?)")
		searchPattern := "%" + req.Search + "%"
		args = append(args, searchPattern, searchPattern)
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// Handle cursor pagination
	if req.Cursor != "" {
		tagID, err := pagination.DecodeCursor(req.Cursor)
		if err != nil {
			return nil, "", false, fmt.Errorf("invalid cursor: %w", err)
		}

		whereClause += " AND tag_id < ?"
		args = append(args, tagID)
	}

	// Build query
	query := fmt.Sprintf(`
		SELECT tag_id, tenant_id, website_id, name, slug, description, is_active, created_at, updated_at
		FROM blog_tags
		WHERE %s
		ORDER BY tag_id DESC
		LIMIT ?
	`, whereClause)

	// Add limit to arguments
	args = append(args, limit+1) // Fetch one more to check if there are more results

	// Execute query
	rows, err := r.db.QueryxContext(ctx, query, args...)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to query tags: %w", err)
	}
	defer rows.Close()

	// Process results
	var tags []*models.Tag
	for rows.Next() {
		var tag models.Tag
		if err := rows.StructScan(&tag); err != nil {
			return nil, "", false, fmt.Errorf("failed to scan tag: %w", err)
		}
		tags = append(tags, &tag)
	}

	if err := rows.Err(); err != nil {
		return nil, "", false, fmt.Errorf("error iterating rows: %w", err)
	}

	// Check if there are more results
	hasMore := false
	nextCursor := ""
	if len(tags) > limit {
		hasMore = true
		tags = tags[:limit]
	}

	// Generate next cursor if there are more results
	if hasMore && len(tags) > 0 {
		lastID := tags[len(tags)-1].TagID
		nextCursor = pagination.EncodeCursor(strconv.FormatUint(uint64(lastID), 10))
	}

	return tags, nextCursor, hasMore, nil
}

// GetTagsWithPostCount fetches tags with post count
func (r *TagRepository) GetTagsWithPostCount(ctx context.Context, tenantID, websiteID uint) ([]*models.Tag, error) {
	query := `
		SELECT t.tag_id, t.tenant_id, t.website_id, t.name, t.slug, t.description, t.is_active, t.created_at, t.updated_at,
		       COUNT(pt.post_id) AS post_count
		FROM blog_tags t
		LEFT JOIN post_tags pt ON t.tag_id = pt.tag_id
		WHERE t.tenant_id = ? AND t.website_id = ?
		GROUP BY t.tag_id
		ORDER BY t.name ASC
	`

	rows, err := r.db.QueryxContext(ctx, query, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to query tags with post count: %w", err)
	}
	defer rows.Close()

	var tags []*models.Tag
	for rows.Next() {
		var tag models.Tag
		if err := rows.StructScan(&tag); err != nil {
			return nil, fmt.Errorf("failed to scan tag with post count: %w", err)
		}
		tags = append(tags, &tag)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return tags, nil
}
