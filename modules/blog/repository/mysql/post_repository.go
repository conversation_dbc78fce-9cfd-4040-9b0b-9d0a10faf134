package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"wnapi/internal/pkg/pagination"
	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/models"
)

// PostTag represents the blog_post_tags table for GORM operations
type PostTag struct {
	TenantID uint `gorm:"primaryKey;column:tenant_id"`
	PostID   uint `gorm:"primaryKey;column:post_id"`
	TagID    uint `gorm:"primaryKey;column:tag_id"`
}

// TableName returns the table name for the PostTag model
func (PostTag) TableName() string {
	return "blog_post_tags"
}

// PostRepository implements the repository.PostRepository interface with MySQL
type PostRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// GetGormDB export *gorm.DB cho các service khác dùng
func (r *PostRepository) GetGormDB() *gorm.DB {
	return r.gormDB
}

// NewPostRepository creates a new PostRepository instance
func NewPostRepository(db *sqlx.DB) *PostRepository {
	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: db.DB,
	}), &gorm.Config{})
	if err != nil {
		// If GORM fails, log error but continue with sqlx only
		fmt.Printf("Warning: Failed to initialize GORM for post repository: %v\n", err)
		gormDB = nil
	}

	return &PostRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create adds a new post to the database using GORM
func (r *PostRepository) Create(ctx context.Context, post *models.Post) error {
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for Create operation")
	}

	// Set default values if not provided
	if post.Status == "" {
		post.Status = "draft"
	}
	if post.Visibility == "" {
		post.Visibility = "public"
	}
	if post.CommentStatus == "" {
		post.CommentStatus = "open"
	}

	// Use GORM transaction
	return r.gormDB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Check if slug exists for this tenant
		var count int64
		err := tx.Model(&models.Post{}).
			Where("tenant_id = ? AND slug = ?", post.TenantID, post.Slug).
			Count(&count).Error
		if err != nil {
			return fmt.Errorf("failed to check slug uniqueness: %w", err)
		}

		if count > 0 {
			return ErrConflict
		}

		// Set timestamps
		now := time.Now()
		post.CreatedAt = now
		post.UpdatedAt = now

		// Create the post
		if err := tx.Create(post).Error; err != nil {
			return fmt.Errorf("failed to create post: %w", err)
		}

		// Save categories if provided
		if len(post.CategoryIDs) > 0 {
			err = r.SetPostCategories(ctx, post.TenantID, post.PostID, post.CategoryIDs)
			if err != nil {
				return fmt.Errorf("failed to set post categories: %w", err)
			}
		}

		// Save tags if provided
		if len(post.TagIDs) > 0 {
			err = r.SetPostTags(ctx, post.TenantID, post.PostID, post.TagIDs)
			if err != nil {
				return fmt.Errorf("failed to set post tags: %w", err)
			}
		}

		return nil
	})
}

// GetByID retrieves a post by its ID
func (r *PostRepository) GetByID(ctx context.Context, tenantID, websiteID, postID uint) (*models.Post, error) {
	var post models.Post

	query := `
		SELECT * FROM blog_posts
		WHERE tenant_id = ? AND website_id = ? AND post_id = ?
	`

	err := r.db.GetContext(ctx, &post, query, tenantID, websiteID, postID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	return &post, nil
}

// GetBySlug retrieves a post by its slug
func (r *PostRepository) GetBySlug(ctx context.Context, tenantID uint, websiteID uint, slug string) (*models.Post, error) {
	var post models.Post

	query := `
		SELECT * FROM blog_posts
		WHERE tenant_id = ? AND website_id = ? AND slug = ?
	`

	err := r.db.GetContext(ctx, &post, query, tenantID, websiteID, slug)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get post by slug: %w", err)
	}

	return &post, nil
}

// Update updates an existing post using GORM
func (r *PostRepository) Update(ctx context.Context, post *models.Post) error {
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for Update operation")
	}

	// Use GORM transaction
	return r.gormDB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Check if the post exists
		var existingPost models.Post
		err := tx.Select("post_id, slug").
			Where("tenant_id = ? AND post_id = ?", post.TenantID, post.PostID).
			First(&existingPost).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return ErrNotFound
			}
			return fmt.Errorf("failed to get existing post: %w", err)
		}

		// If slug is changing, check for uniqueness
		if post.Slug != existingPost.Slug && post.Slug != "" {
			var count int64
			err = tx.Model(&models.Post{}).
				Where("tenant_id = ? AND slug = ? AND post_id != ?", post.TenantID, post.Slug, post.PostID).
				Count(&count).Error
			if err != nil {
				return fmt.Errorf("failed to check slug uniqueness: %w", err)
			}

			if count > 0 {
				return ErrConflict
			}
		}

		// Set updated timestamp
		post.UpdatedAt = time.Now()

		// Update the post using GORM
		result := tx.Model(post).Where("tenant_id = ? AND post_id = ?", post.TenantID, post.PostID).Updates(post)
		if result.Error != nil {
			return fmt.Errorf("failed to update post: %w", result.Error)
		}

		if result.RowsAffected == 0 {
			return ErrNotFound
		}

		// Get the underlying sql.Tx from GORM
		conn, err := tx.DB()
		if err != nil {
			return fmt.Errorf("failed to get sql.DB from gorm tx: %w", err)
		}
		// Start a nested transaction for raw SQL (category/tag)
		sqlTx, err := conn.BeginTx(ctx, nil)
		if err != nil {
			return fmt.Errorf("failed to begin nested sql.Tx: %w", err)
		}
		defer func() {
			if p := recover(); p != nil {
				sqlTx.Rollback()
				panic(p)
			}
		}()

		if post.CategoryIDs != nil {
			err = r.SetPostCategoriesTx(ctx, sqlTx, post.TenantID, post.PostID, post.CategoryIDs)
			if err != nil {
				sqlTx.Rollback()
				return fmt.Errorf("failed to update post categories: %w", err)
			}
		}
		if post.TagIDs != nil {
			err = r.SetPostTagsTx(ctx, sqlTx, post.TenantID, post.PostID, post.TagIDs)
			if err != nil {
				sqlTx.Rollback()
				return fmt.Errorf("failed to update post tags: %w", err)
			}
		}
		if err := sqlTx.Commit(); err != nil {
			return fmt.Errorf("failed to commit nested sql.Tx: %w", err)
		}
		return nil
	})
}

// Delete removes a post using GORM
// Delete xóa một bài viết và các mối quan hệ liên quan (categories, tags) dựa trên tenant_id, website_id và post_id
// Sử dụng GORM transaction để đảm bảo tính nhất quán của dữ liệu
func (r *PostRepository) Delete(ctx context.Context, tenantID, websiteID, postID uint) error {
	// Kiểm tra xem GORM có sẵn không
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for Delete operation")
	}

	// Use GORM transaction
	return r.gormDB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Delete post-category relationships
		if err := tx.Where("tenant_id = ? AND website_id = ? AND post_id = ?", tenantID, websiteID, postID).Delete(&struct {
			TenantID   uint `gorm:"column:tenant_id"`
			WebsiteID  uint `gorm:"column:website_id"`
			PostID     uint `gorm:"column:post_id"`
			CategoryID uint `gorm:"column:category_id"`
		}{}).Error; err != nil {
			return fmt.Errorf("failed to delete post categories: %w", err)
		}

		// Delete post-tag relationships
		if err := tx.Where("tenant_id = ? AND website_id = ? AND post_id = ?", tenantID, websiteID, postID).Delete(&PostTag{}).Error; err != nil {
			return fmt.Errorf("failed to delete post tags: %w", err)
		}

		// Delete the post
		result := tx.Where("tenant_id = ? AND website_id = ? AND post_id = ?", tenantID, websiteID, postID).Delete(&models.Post{})
		if result.Error != nil {
			return fmt.Errorf("failed to delete post: %w", result.Error)
		}

		if result.RowsAffected == 0 {
			return ErrNotFound
		}

		return nil
	})
}

// List retrieves a paginated list of posts
func (r *PostRepository) List(ctx context.Context, tenantID uint, websiteID uint, req request.ListPostRequest) ([]*models.Post, string, bool, error) {
	var posts []*models.Post

	// Base query
	baseQuery := `
		SELECT p.*
		FROM blog_posts p
	`

	// Conditions
	conditions := []string{"p.tenant_id = ?", "p.website_id = ?"}
	args := []interface{}{tenantID, websiteID}

	// Add filters
	if req.Status != "" {
		conditions = append(conditions, "p.status = ?")
		args = append(args, req.Status)
	}

	if req.CategoryID != nil {
		baseQuery += " JOIN blog_post_categories pc ON p.post_id = pc.post_id AND p.tenant_id = pc.tenant_id"
		conditions = append(conditions, "pc.category_id = ?")
		args = append(args, *req.CategoryID)
	}

	if req.TagID != nil {
		baseQuery += " JOIN blog_post_tags pt ON p.post_id = pt.post_id AND p.tenant_id = pt.tenant_id"
		conditions = append(conditions, "pt.tag_id = ?")
		args = append(args, *req.TagID)
	}

	if req.AuthorID != nil {
		conditions = append(conditions, "p.author_id = ?")
		args = append(args, *req.AuthorID)
	}

	if req.Search != "" {
		conditions = append(conditions, "(p.title LIKE ? OR p.description LIKE ? OR p.content LIKE ?)")
		searchPattern := "%" + req.Search + "%"
		args = append(args, searchPattern, searchPattern, searchPattern)
	}

	// Apply cursor pagination
	if req.Cursor != "" {
		cur, err := pagination.DecodeCursor(req.Cursor)
		if err != nil {
			return nil, "", false, fmt.Errorf("invalid cursor: %w", err)
		}
		if cur != nil && cur.Type == pagination.CursorTypeID {
			id, err := cur.GetIDValue()
			if err != nil {
				return nil, "", false, fmt.Errorf("invalid cursor value: %w", err)
			}
			if cur.IsNext() {
				conditions = append(conditions, "p.post_id < ?")
				args = append(args, id)
			} else if cur.IsPrev() {
				conditions = append(conditions, "p.post_id > ?")
				args = append(args, id)
			}
		}
	}

	// Combine conditions
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	// Order and limit
	orderBy := "p.post_id DESC" // Default sorting by ID descending
	if req.OrderBy == "newest" {
		orderBy = "p.publish_at DESC, p.post_id DESC"
	} else if req.OrderBy == "oldest" {
		orderBy = "p.publish_at ASC, p.post_id ASC"
	} else if req.OrderBy == "title" {
		orderBy = "p.title ASC, p.post_id DESC"
	}

	limit := 10 // Default limit
	if req.Limit > 0 {
		limit = req.Limit
	}
	if limit > 100 {
		limit = 100 // Enforce max limit
	}

	// Complete query
	query := baseQuery + whereClause + " ORDER BY " + orderBy + " LIMIT ?"
	args = append(args, limit+1) // Fetch one extra for has_more check

	// Execute query
	err := r.db.SelectContext(ctx, &posts, query, args...)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to list posts: %w", err)
	}

	// Check if there are more results
	hasMore := false
	var nextCursor string
	if len(posts) > limit {
		hasMore = true
		posts = posts[:limit]
	}

	// Generate next cursor if there are more results
	if hasMore && len(posts) > 0 {
		cursor, err := pagination.IDCursor(posts[len(posts)-1].PostID, "next").Encode()
		if err == nil {
			nextCursor = cursor
		}
	}

	return posts, nextCursor, hasMore, nil
}

// GetPostCategories retrieves the categories for a post
func (r *PostRepository) GetPostCategories(ctx context.Context, tenantID, websiteID, postID uint) ([]uint, error) {
	var categoryIDs []uint

	query := `
		SELECT category_id
		FROM blog_post_categories
		WHERE tenant_id = ? AND website_id = ? AND post_id = ?
	`

	err := r.db.SelectContext(ctx, &categoryIDs, query, tenantID, websiteID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to get post categories: %w", err)
	}

	return categoryIDs, nil
}

// SetPostCategories sets the categories for a post
func (r *PostRepository) SetPostCategories(ctx context.Context, tenantID, postID uint, categoryIDs []uint) error {
	// Lấy danh sách category IDs hiện tại
	var existingCategoryIDs []uint
	err := r.db.SelectContext(ctx, &existingCategoryIDs,
		"SELECT category_id FROM blog_post_categories WHERE tenant_id = ? AND post_id = ?",
		tenantID, postID)
	if err != nil {
		return fmt.Errorf("failed to get existing categories: %w", err)
	}

	// Sử dụng map để tối ưu hóa việc tìm kiếm các category cần thêm và xóa
	existingCategoryMap := make(map[uint]bool, len(existingCategoryIDs))
	for _, id := range existingCategoryIDs {
		existingCategoryMap[id] = true
	}

	newCategoryMap := make(map[uint]bool, len(categoryIDs))
	for _, id := range categoryIDs {
		newCategoryMap[id] = true
	}

	// Tìm category IDs cần xóa (có trong existingCategoryIDs nhưng không có trong categoryIDs)
	var toRemove []uint
	for _, existingID := range existingCategoryIDs {
		if !newCategoryMap[existingID] {
			toRemove = append(toRemove, existingID)
		}
	}

	// Tìm category IDs cần thêm (có trong categoryIDs nhưng không có trong existingCategoryIDs)
	var toAdd []uint
	for _, newID := range categoryIDs {
		if !existingCategoryMap[newID] {
			toAdd = append(toAdd, newID)
		}
	}

	// Xóa các categories không còn cần thiết
	if len(toRemove) > 0 {
		// Build placeholders for IN clause
		placeholders := make([]string, len(toRemove))
		args := make([]interface{}, 0, len(toRemove)+2)
		args = append(args, tenantID, postID)

		for i, categoryID := range toRemove {
			placeholders[i] = "?"
			args = append(args, categoryID)
		}

		query := fmt.Sprintf("DELETE FROM blog_post_categories WHERE tenant_id = ? AND post_id = ? AND category_id IN (%s)",
			strings.Join(placeholders, ", "))

		// Retry mechanism for deadlock scenarios
		maxRetries := 3
		for retry := 0; retry < maxRetries; retry++ {
			_, err := r.db.ExecContext(ctx, query, args...)
			if err == nil {
				break // Success, exit retry loop
			}

			// Check if it's a deadlock or lock timeout error
			if strings.Contains(err.Error(), "Lock wait timeout") ||
				strings.Contains(err.Error(), "Deadlock found") {
				if retry < maxRetries-1 {
					// Wait a bit before retrying (exponential backoff)
					time.Sleep(time.Duration(retry+1) * 10 * time.Millisecond)
					continue
				}
			}

			// If not a retryable error or max retries reached, return error
			return fmt.Errorf("failed to delete categories (retry %d/%d): %w", retry+1, maxRetries, err)
		}
	}

	// Thêm categories mới
	if len(toAdd) > 0 {
		// Use INSERT IGNORE to handle potential duplicates and batch processing
		const batchSize = 50 // Reduced batch size to minimize lock duration

		for i := 0; i < len(toAdd); i += batchSize {
			end := i + batchSize
			if end > len(toAdd) {
				end = len(toAdd)
			}

			batch := toAdd[i:end]
			query := "INSERT IGNORE INTO blog_post_categories (tenant_id, post_id, category_id) VALUES "
			var placeholders []string
			var args []interface{}

			for _, categoryID := range batch {
				placeholders = append(placeholders, "(?, ?, ?)")
				args = append(args, tenantID, postID, categoryID)
			}

			query += strings.Join(placeholders, ", ")

			// Retry mechanism for deadlock scenarios
			maxRetries := 3
			for retry := 0; retry < maxRetries; retry++ {
				_, err = r.db.ExecContext(ctx, query, args...)
				if err == nil {
					break // Success, exit retry loop
				}

				// Check if it's a deadlock or lock timeout error
				if strings.Contains(err.Error(), "Lock wait timeout") ||
					strings.Contains(err.Error(), "Deadlock found") {
					if retry < maxRetries-1 {
						// Wait a bit before retrying (exponential backoff)
						time.Sleep(time.Duration(retry+1) * 10 * time.Millisecond)
						continue
					}
				}

				// If not a retryable error or max retries reached, return error
				return fmt.Errorf("failed to insert categories batch (retry %d/%d): %w", retry+1, maxRetries, err)
			}
		}
	}

	return nil
}

// SetPostCategories sets the categories for a post
func (r *PostRepository) SetPostCategoriesTx(ctx context.Context, tx *sql.Tx, tenantID, postID uint, categoryIDs []uint) error {
	// Lấy danh sách category IDs hiện tại
	var existingCategoryIDs []uint
	rows, err := tx.QueryContext(ctx, "SELECT category_id FROM blog_post_categories WHERE tenant_id = ? AND post_id = ?", tenantID, postID)
	if err != nil {
		return fmt.Errorf("failed to get existing categories: %w", err)
	}
	defer rows.Close()
	for rows.Next() {
		var id uint
		if err := rows.Scan(&id); err != nil {
			return err
		}
		existingCategoryIDs = append(existingCategoryIDs, id)
	}

	existingCategoryMap := make(map[uint]bool, len(existingCategoryIDs))
	for _, id := range existingCategoryIDs {
		existingCategoryMap[id] = true
	}
	newCategoryMap := make(map[uint]bool, len(categoryIDs))
	for _, id := range categoryIDs {
		newCategoryMap[id] = true
	}
	var toRemove []uint
	for _, existingID := range existingCategoryIDs {
		if !newCategoryMap[existingID] {
			toRemove = append(toRemove, existingID)
		}
	}
	var toAdd []uint
	for _, newID := range categoryIDs {
		if !existingCategoryMap[newID] {
			toAdd = append(toAdd, newID)
		}
	}
	if len(toRemove) > 0 {
		placeholders := make([]string, len(toRemove))
		args := make([]interface{}, 0, len(toRemove)+2)
		args = append(args, tenantID, postID)
		for i, categoryID := range toRemove {
			placeholders[i] = "?"
			args = append(args, categoryID)
		}
		query := fmt.Sprintf("DELETE FROM blog_post_categories WHERE tenant_id = ? AND post_id = ? AND category_id IN (%s)", strings.Join(placeholders, ", "))
		_, err := tx.ExecContext(ctx, query, args...)
		if err != nil {
			return fmt.Errorf("failed to delete categories: %w", err)
		}
	}
	if len(toAdd) > 0 {
		const batchSize = 50
		for i := 0; i < len(toAdd); i += batchSize {
			end := i + batchSize
			if end > len(toAdd) {
				end = len(toAdd)
			}
			batch := toAdd[i:end]
			query := "INSERT IGNORE INTO blog_post_categories (tenant_id, post_id, category_id) VALUES "
			var placeholders []string
			var args []interface{}
			for _, categoryID := range batch {
				placeholders = append(placeholders, "(?, ?, ?)")
				args = append(args, tenantID, postID, categoryID)
			}
			query += strings.Join(placeholders, ", ")
			_, err := tx.ExecContext(ctx, query, args...)
			if err != nil {
				return fmt.Errorf("failed to insert categories batch: %w", err)
			}
		}
	}
	return nil
}

// GetPostTags retrieves the tags for a post
func (r *PostRepository) GetPostTags(ctx context.Context, tenantID, websiteID, postID uint) ([]uint, error) {
	var tagIDs []uint

	query := `
		SELECT tag_id
		FROM blog_post_tags
		WHERE tenant_id = ? AND website_id = ? AND post_id = ?
	`

	err := r.db.SelectContext(ctx, &tagIDs, query, tenantID, websiteID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to get post tags: %w", err)
	}

	return tagIDs, nil
}

// SetPostTags sets the tags for a post
// SetPostTags cập nhật danh sách tags cho một bài viết
// Hàm này sẽ thêm các tags mới và xóa các tags không còn cần thiết
// Sử dụng GORM khi có thể và fallback về SQL khi cần thiết
func (r *PostRepository) SetPostTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error {
	// Lấy danh sách tag IDs hiện tại
	var existingTagIDs []uint
	err := r.db.SelectContext(ctx, &existingTagIDs,
		"SELECT tag_id FROM blog_post_tags WHERE tenant_id = ? AND post_id = ?",
		tenantID, postID)
	if err != nil {
		return fmt.Errorf("failed to get existing tags: %w", err)
	}

	// Sử dụng map để tối ưu hóa việc tìm kiếm các tag cần thêm và xóa
	existingTagMap := make(map[uint]bool, len(existingTagIDs))
	for _, id := range existingTagIDs {
		existingTagMap[id] = true
	}

	newTagMap := make(map[uint]bool, len(tagIDs))
	for _, id := range tagIDs {
		newTagMap[id] = true
	}

	// Tìm tag IDs cần xóa (có trong existingTagIDs nhưng không có trong tagIDs)
	var toRemove []uint
	for _, existingID := range existingTagIDs {
		if !newTagMap[existingID] {
			toRemove = append(toRemove, existingID)
		}
	}

	// Tìm tag IDs cần thêm (có trong tagIDs nhưng không có trong existingTagIDs)
	var toAdd []uint
	for _, newID := range tagIDs {
		if !existingTagMap[newID] {
			toAdd = append(toAdd, newID)
		}
	}

	// Xóa các tags không còn cần thiết
	if len(toRemove) > 0 {
		// Use raw SQL for consistency and better performance
		// Build placeholders for IN clause
		placeholders := make([]string, len(toRemove))
		args := make([]interface{}, 0, len(toRemove)+2)
		args = append(args, tenantID, postID)

		for i, tagID := range toRemove {
			placeholders[i] = "?"
			args = append(args, tagID)
		}

		query := fmt.Sprintf("DELETE FROM blog_post_tags WHERE tenant_id = ? AND post_id = ? AND tag_id IN (%s)",
			strings.Join(placeholders, ", "))

		// Retry mechanism for deadlock scenarios
		maxRetries := 3
		for retry := 0; retry < maxRetries; retry++ {
			_, err := r.db.ExecContext(ctx, query, args...)
			if err == nil {
				break // Success, exit retry loop
			}

			// Check if it's a deadlock or lock timeout error
			if strings.Contains(err.Error(), "Lock wait timeout") ||
				strings.Contains(err.Error(), "Deadlock found") {
				if retry < maxRetries-1 {
					// Wait a bit before retrying (exponential backoff)
					time.Sleep(time.Duration(retry+1) * 10 * time.Millisecond)
					continue
				}
			}

			// If not a retryable error or max retries reached, return error
			return fmt.Errorf("failed to delete tags (retry %d/%d): %w", retry+1, maxRetries, err)
		}
	}

	// Thêm tags mới
	if len(toAdd) > 0 {
		// Always use raw SQL for better performance and to avoid GORM deadlock issues
		// Xây dựng truy vấn để chèn nhiều giá trị cùng lúc với batch processing
		const batchSize = 50 // Reduced batch size to minimize lock duration

		for i := 0; i < len(toAdd); i += batchSize {
			end := i + batchSize
			if end > len(toAdd) {
				end = len(toAdd)
			}

			batch := toAdd[i:end]
			query := "INSERT IGNORE INTO blog_post_tags (tenant_id, post_id, tag_id) VALUES "
			var placeholders []string
			var args []interface{}

			for _, tagID := range batch {
				placeholders = append(placeholders, "(?, ?, ?)")
				args = append(args, tenantID, postID, tagID)
			}

			query += strings.Join(placeholders, ", ")

			// Retry mechanism for deadlock scenarios
			maxRetries := 3
			for retry := 0; retry < maxRetries; retry++ {
				_, err = r.db.ExecContext(ctx, query, args...)
				if err == nil {
					break // Success, exit retry loop
				}

				// Check if it's a deadlock or lock timeout error
				if strings.Contains(err.Error(), "Lock wait timeout") ||
					strings.Contains(err.Error(), "Deadlock found") {
					if retry < maxRetries-1 {
						// Wait a bit before retrying (exponential backoff)
						time.Sleep(time.Duration(retry+1) * 10 * time.Millisecond)
						continue
					}
				}

				// If not a retryable error or max retries reached, return error
				return fmt.Errorf("failed to insert tags batch (retry %d/%d): %w", retry+1, maxRetries, err)
			}
		}
	}

	return nil
}

// SetPostTags sets the tags for a post (transactional)
func (r *PostRepository) SetPostTagsTx(ctx context.Context, tx *sql.Tx, tenantID, postID uint, tagIDs []uint) error {
	var existingTagIDs []uint
	rows, err := tx.QueryContext(ctx, "SELECT tag_id FROM blog_post_tags WHERE tenant_id = ? AND post_id = ?", tenantID, postID)
	if err != nil {
		return fmt.Errorf("failed to get existing tags: %w", err)
	}
	defer rows.Close()
	for rows.Next() {
		var id uint
		if err := rows.Scan(&id); err != nil {
			return err
		}
		existingTagIDs = append(existingTagIDs, id)
	}
	existingTagMap := make(map[uint]bool, len(existingTagIDs))
	for _, id := range existingTagIDs {
		existingTagMap[id] = true
	}
	newTagMap := make(map[uint]bool, len(tagIDs))
	for _, id := range tagIDs {
		newTagMap[id] = true
	}
	var toRemove []uint
	for _, existingID := range existingTagIDs {
		if !newTagMap[existingID] {
			toRemove = append(toRemove, existingID)
		}
	}
	var toAdd []uint
	for _, newID := range tagIDs {
		if !existingTagMap[newID] {
			toAdd = append(toAdd, newID)
		}
	}
	if len(toRemove) > 0 {
		placeholders := make([]string, len(toRemove))
		args := make([]interface{}, 0, len(toRemove)+2)
		args = append(args, tenantID, postID)
		for i, tagID := range toRemove {
			placeholders[i] = "?"
			args = append(args, tagID)
		}
		query := fmt.Sprintf("DELETE FROM blog_post_tags WHERE tenant_id = ? AND post_id = ? AND tag_id IN (%s)", strings.Join(placeholders, ", "))
		_, err := tx.ExecContext(ctx, query, args...)
		if err != nil {
			return fmt.Errorf("failed to delete tags: %w", err)
		}
	}
	if len(toAdd) > 0 {
		const batchSize = 50
		for i := 0; i < len(toAdd); i += batchSize {
			end := i + batchSize
			if end > len(toAdd) {
				end = len(toAdd)
			}
			batch := toAdd[i:end]
			query := "INSERT IGNORE INTO blog_post_tags (tenant_id, post_id, tag_id) VALUES "
			var placeholders []string
			var args []interface{}
			for _, tagID := range batch {
				placeholders = append(placeholders, "(?, ?, ?)")
				args = append(args, tenantID, postID, tagID)
			}
			query += strings.Join(placeholders, ", ")
			_, err := tx.ExecContext(ctx, query, args...)
			if err != nil {
				return fmt.Errorf("failed to insert tags batch: %w", err)
			}
		}
	}
	return nil
}

// CountPosts counts posts with optional filters
func (r *PostRepository) CountPosts(ctx context.Context, tenantID uint, status *string, categoryID *uint) (int, error) {
	baseQuery := "SELECT COUNT(*) FROM blog_posts p"
	conditions := []string{"p.tenant_id = ?"}
	args := []interface{}{tenantID}

	if status != nil {
		conditions = append(conditions, "p.status = ?")
		args = append(args, *status)
	}

	if categoryID != nil {
		baseQuery += " JOIN blog_post_categories pc ON p.post_id = pc.post_id AND p.tenant_id = pc.tenant_id"
		conditions = append(conditions, "pc.category_id = ?")
		args = append(args, *categoryID)
	}

	whereClause := " WHERE " + strings.Join(conditions, " AND ")
	query := baseQuery + whereClause

	var count int
	err := r.db.GetContext(ctx, &count, query, args...)
	if err != nil {
		return 0, fmt.Errorf("failed to count posts: %w", err)
	}

	return count, nil
}
