package request

import (
	"time"

	"wnapi/modules/website/models"
)

// CreatePageRequest đại diện cho yêu cầu tạo page mới
type CreatePageRequest struct {
	Title           string         `json:"title" binding:"required,max=255"`
	Slug            string         `json:"slug" binding:"required,max=255"`
	Content         models.Content `json:"content" binding:"omitempty"`
	Layout          *string        `json:"layout" binding:"omitempty,max=100"`
	MetaTitle       *string        `json:"meta_title" binding:"omitempty,max=255"`
	MetaDescription *string        `json:"meta_description" binding:"omitempty"`
	IsHomepage      *bool          `json:"is_homepage" binding:"omitempty"`
	Status          string         `json:"status" binding:"omitempty,oneof=draft published scheduled"`
	PublishedAt     *time.Time     `json:"published_at" binding:"omitempty"`
}
