CREATE TABLE IF NOT EXISTS website_pages (
  page_id INT UNSIGNED NOT NULL AUTO_INCREMENT,
  website_id INT UNSIGNED NOT NULL,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  content JSON NULL,
  layout VARCHAR(100) NULL,
  meta_title VARCHAR(255) NULL,
  meta_description TEXT NULL,
  is_homepage BOOLEAN NOT NULL DEFAULT 0,
  status VARCHAR(20) NOT NULL DEFAULT 'draft',
  published_at TIMESTAMP NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (page_id),
  INDEX idx_website_id (website_id),
  INDEX idx_slug (slug),
  INDEX idx_status (status),
  INDEX idx_is_homepage (is_homepage),
  UNIQUE INDEX unique_website_slug (website_id, slug)
)
ENGINE = InnoDB
CHARACTER SET = utf8mb4
COLLATE = utf8mb4_unicode_ci; 