CREATE TABLE IF NOT EXISTS website_templates (
  template_id INT UNSIGNED NOT NULL AUTO_INCREMENT,
  theme_id INT UNSIGNED NOT NULL,
  name VARCHAR(100) NOT NULL,
  type VARCHAR(50) NOT NULL,
  html_structure MEDIUMTEXT NOT NULL,
  css MEDIUMTEXT NULL,
  default_content JSON NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (template_id),
  INDEX idx_theme_id (theme_id),
  INDEX idx_name (name),
  INDEX idx_type (type)
)
ENGINE = InnoDB
CHARACTER SET = utf8mb4
COLLATE = utf8mb4_unicode_ci; 