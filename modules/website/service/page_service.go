package service

import (
	"context"
	"wnapi/modules/website/dto/request"
	"wnapi/modules/website/dto/response"
	"wnapi/modules/website/models"
	"wnapi/modules/website/repository"
)

// PageService định nghĩa interface cho business logic page
type PageService interface {
	// <PERSON><PERSON><PERSON> thao tác CRUD cơ bản
	CreatePage(ctx context.Context, websiteID int, req request.CreatePageRequest) (*response.PageResponse, error)
	GetPage(ctx context.Context, websiteID, pageID int) (*response.PageResponse, error)
	GetPageBySlug(ctx context.Context, websiteID int, slug string) (*response.PageResponse, error)
	GetHomepage(ctx context.Context, websiteID int) (*response.PageResponse, error)
	UpdatePage(ctx context.Context, websiteID, pageID int, req request.UpdatePageRequest) (*response.PageResponse, error)
	DeletePage(ctx context.Context, websiteID, pageID int) error
	ListPages(ctx context.Context, websiteID int, req request.ListPageRequest) (*response.PageListResponse, error)
}

// pageService triển khai PageService interface
type pageService struct {
	pageRepo    repository.PageRepository
	websiteRepo repository.WebsiteRepository
}

// NewPageService tạo một instance mới của PageService
func NewPageService(pageRepo repository.PageRepository, websiteRepo repository.WebsiteRepository) PageService {
	return &pageService{
		pageRepo:    pageRepo,
		websiteRepo: websiteRepo,
	}
}

// CreatePage tạo một page mới
func (s *pageService) CreatePage(ctx context.Context, websiteID int, req request.CreatePageRequest) (*response.PageResponse, error) {
	// Tạo model từ request
	isHomepage := false
	if req.IsHomepage != nil {
		isHomepage = *req.IsHomepage
	}

	status := "draft"
	if req.Status != "" {
		status = req.Status
	}

	page := &models.Page{
		WebsiteID:       websiteID,
		Title:           req.Title,
		Slug:            req.Slug,
		Content:         req.Content,
		Layout:          req.Layout,
		MetaTitle:       req.MetaTitle,
		MetaDescription: req.MetaDescription,
		IsHomepage:      isHomepage,
		Status:          status,
		PublishedAt:     req.PublishedAt,
	}

	// Lưu vào repository
	if err := s.pageRepo.Create(ctx, page); err != nil {
		return nil, err
	}

	// Chuyển đổi sang response
	return s.getPageWithRelations(ctx, websiteID, page.PageID, false)
}

// GetPage lấy thông tin page theo ID
func (s *pageService) GetPage(ctx context.Context, websiteID, pageID int) (*response.PageResponse, error) {
	return s.getPageWithRelations(ctx, websiteID, pageID, false)
}

// GetPageBySlug lấy thông tin page theo slug
func (s *pageService) GetPageBySlug(ctx context.Context, websiteID int, slug string) (*response.PageResponse, error) {
	// Lấy từ repository
	page, err := s.pageRepo.GetBySlug(ctx, websiteID, slug)
	if err != nil {
		return nil, err
	}

	return s.getPageWithRelations(ctx, websiteID, page.PageID, false)
}

// GetHomepage lấy trang chủ của website
func (s *pageService) GetHomepage(ctx context.Context, websiteID int) (*response.PageResponse, error) {
	// Lấy từ repository
	page, err := s.pageRepo.GetHomepage(ctx, websiteID)
	if err != nil {
		return nil, err
	}

	return s.getPageWithRelations(ctx, websiteID, page.PageID, false)
}

// UpdatePage cập nhật thông tin page
func (s *pageService) UpdatePage(ctx context.Context, websiteID, pageID int, req request.UpdatePageRequest) (*response.PageResponse, error) {
	// Lấy page hiện tại
	existingPage, err := s.pageRepo.GetByID(ctx, websiteID, pageID)
	if err != nil {
		return nil, err
	}

	// Cập nhật các trường được gửi trong request
	if req.Title != nil {
		existingPage.Title = *req.Title
	}

	if req.Slug != nil {
		existingPage.Slug = *req.Slug
	}

	if req.Content != nil {
		existingPage.Content = *req.Content
	}

	if req.Layout != nil {
		existingPage.Layout = req.Layout
	}

	if req.MetaTitle != nil {
		existingPage.MetaTitle = req.MetaTitle
	}

	if req.MetaDescription != nil {
		existingPage.MetaDescription = req.MetaDescription
	}

	if req.IsHomepage != nil {
		existingPage.IsHomepage = *req.IsHomepage
	}

	if req.Status != nil {
		existingPage.Status = *req.Status
	}

	if req.PublishedAt != nil {
		existingPage.PublishedAt = req.PublishedAt
	}

	// Lưu vào repository
	if err := s.pageRepo.Update(ctx, existingPage); err != nil {
		return nil, err
	}

	// Chuyển đổi sang response
	return s.getPageWithRelations(ctx, websiteID, pageID, false)
}

// DeletePage xóa page
func (s *pageService) DeletePage(ctx context.Context, websiteID, pageID int) error {
	return s.pageRepo.Delete(ctx, websiteID, pageID)
}

// ListPages liệt kê danh sách page với phân trang
func (s *pageService) ListPages(ctx context.Context, websiteID int, req request.ListPageRequest) (*response.PageListResponse, error) {
	// Lấy danh sách từ repository
	pages, nextCursor, hasMore, err := s.pageRepo.List(ctx, websiteID, req)
	if err != nil {
		return nil, err
	}

	// Chuyển đổi sang response
	pageResponses := make([]response.PageResponse, 0, len(pages))
	for _, page := range pages {
		pageResp, err := s.convertToResponse(ctx, page, req.WithWebsite)
		if err != nil {
			return nil, err
		}
		pageResponses = append(pageResponses, *pageResp)
	}

	return &response.PageListResponse{
		Pages:      pageResponses,
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}, nil
}

// Helper functions

// getPageWithRelations lấy page và thông tin liên quan
func (s *pageService) getPageWithRelations(ctx context.Context, websiteID, pageID int, withWebsite bool) (*response.PageResponse, error) {
	// Lấy page từ repository
	page, err := s.pageRepo.GetByID(ctx, websiteID, pageID)
	if err != nil {
		return nil, err
	}

	return s.convertToResponse(ctx, page, withWebsite)
}

// convertToResponse chuyển đổi model sang response
func (s *pageService) convertToResponse(ctx context.Context, page *models.Page, withWebsite bool) (*response.PageResponse, error) {
	resp := &response.PageResponse{
		ID:              page.PageID,
		WebsiteID:       page.WebsiteID,
		Title:           page.Title,
		Slug:            page.Slug,
		Content:         page.Content,
		Layout:          page.Layout,
		MetaTitle:       page.MetaTitle,
		MetaDescription: page.MetaDescription,
		IsHomepage:      page.IsHomepage,
		Status:          page.Status,
		PublishedAt:     page.PublishedAt,
		CreatedAt:       page.CreatedAt,
		UpdatedAt:       page.UpdatedAt,
	}

	// Nếu cần thêm thông tin website
	if withWebsite {
		website, err := s.websiteRepo.GetByID(ctx, 0, page.WebsiteID) // tenantID sẽ được lấy từ website
		if err == nil {
			resp.Website = &response.WebsiteResponse{
				ID:           website.WebsiteID,
				TenantID:     website.TenantID,
				Name:         website.Name,
				Subdomain:    website.Subdomain,
				CustomDomain: website.CustomDomain,
				Description:  website.Description,
				Status:       website.Status,
				ThemeID:      website.ThemeID,
				CreatedAt:    website.CreatedAt,
				UpdatedAt:    website.UpdatedAt,
			}
		}
	}

	return resp, nil
}
