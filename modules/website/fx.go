package website

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/fx"

	"wnapi/internal/fx/modules"
	"wnapi/internal/pkg/auth"
	"wnapi/modules/tenant/service"
	"wnapi/modules/website/api"
)

func init() {
	modules.GlobalRegistry.Register(&WebsiteModule{})
}

// WebsiteModule implements the FX module interface
type WebsiteModule struct{}

// Name returns the module name
func (m *WebsiteModule) Name() string {
	return "website"
}

// Dependencies returns module dependencies
func (m *WebsiteModule) Dependencies() []string {
	return []string{"tenant", "auth"}
}

// Priority returns module loading priority
func (m *WebsiteModule) Priority() int {
	return 90 // Load after core modules
}

// Enabled returns whether the module is enabled
func (m *WebsiteModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default to enabled
}

// GetMigrationPath returns path to module migrations
func (m *WebsiteModule) GetMigrationPath() string {
	return "modules/website/migrations/mysql"
}

// GetMigrationOrder returns migration priority order
func (m *WebsiteModule) GetMigrationOrder() int {
	return 90 // Website module runs late
}

// Module returns FX options for the module
func (m *WebsiteModule) Module() fx.Option {
	return fx.Module("website",
		// Providers
		fx.Provide(
			// Configuration
			NewWebsiteConfig,

			// Simple route registration
			NewWebsiteHandler,
		),

		// Route registration
		fx.Invoke(func(
			handler *api.Handler,
			engine *gin.Engine,
			jwtService *auth.JWTService,
			tenantService service.TenantService,
		) {
			handler.RegisterRoutes(engine)
		}),
	)
}
