package hello

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/fx"

	"wnapi/internal/fx/modules"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
)

// HelloModule implements FXModule interface for hello module
type HelloModule struct{}

// NewHelloModule creates a new hello module instance
func NewHelloModule() modules.FXModule {
	return &HelloModule{}
}

// Name returns the module name
func (m *HelloModule) Name() string {
	return "hello"
}

// Dependencies returns list of module dependencies
func (m *HelloModule) Dependencies() []string {
	return []string{} // No dependencies
}

// Priority returns loading priority (lower = higher priority)
func (m *HelloModule) Priority() int {
	return 100 // Low priority, load after core modules
}

// Enabled checks if module should be loaded
func (m *HelloModule) Enabled(config map[string]interface{}) bool {
	return true // Always enabled
}

// GetMigrationPath returns path to module migrations
func (m *HelloModule) GetMigrationPath() string {
	return "" // Hello module has no migrations
}

// GetMigrationOrder returns migration priority order
func (m *HelloModule) GetMigrationOrder() int {
	return 100 // Hello module runs last (no migrations anyway)
}

// Module returns fx.Module for hello
func (m *HelloModule) Module() fx.Option {
	return fx.Module("hello",
		// Providers
		fx.Provide(
			NewHelloConfig,
			NewHelloHandler,
		),

		// Route registration
		fx.Invoke(RegisterHelloRoutes),
	)
}

// HelloConfig holds configuration for hello module
type HelloConfig struct {
	Message string
}

// NewHelloConfig creates hello configuration from app config
func NewHelloConfig(cfg config.Config) *HelloConfig {
	return &HelloConfig{
		Message: cfg.GetStringWithDefault("HELLO_MESSAGE", "Hello, World!"),
	}
}

// HelloHandler handles hello module routes
type HelloHandler struct {
	config *HelloConfig
	logger logger.Logger
}

// NewHelloHandler creates a new hello handler
func NewHelloHandler(config *HelloConfig, log logger.Logger) *HelloHandler {
	return &HelloHandler{
		config: config,
		logger: log,
	}
}

// RegisterHelloRoutes registers hello module routes
func RegisterHelloRoutes(
	engine *gin.Engine,
	handler *HelloHandler,
	log logger.Logger,
) {
	log.Info("Registering hello module routes")

	// Hello routes
	helloGroup := engine.Group("/api/v1/hello")
	{
		helloGroup.GET("/", handler.HelloHandler)
		helloGroup.GET("/json", handler.HelloJSONHandler)
	}

	log.Info("Hello module routes registered successfully")
}

// HelloHandler returns a simple hello message
func (h *HelloHandler) HelloHandler(c *gin.Context) {
	c.String(http.StatusOK, h.config.Message)
}

// HelloJSONHandler returns a hello message in JSON format
func (h *HelloHandler) HelloJSONHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": h.config.Message,
	})
}

// printRoutes prints all registered routes for debugging
func printRoutes(engine *gin.Engine, log logger.Logger) {
	routes := engine.Routes()
	log.Info("Hello module registered routes:")
	for _, route := range routes {
		if route.Path == "/api/v1/hello/" || route.Path == "/api/v1/hello/json" {
			log.Info("Route registered", "method", route.Method, "path", route.Path)
		}
	}
}

func init() {
	// Register with FX module registry
	modules.RegisterModule(NewHelloModule())
}
