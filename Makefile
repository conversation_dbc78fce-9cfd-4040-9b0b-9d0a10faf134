.PHONY: build run dev clean migrate migrate-create test test-coverage create-module create-plugin docker-build docker-up docker-down docker-logs plan docs build-seed seed-dev seed-staging seed-prod seed-list test-seed kill kill-dev kill-port kill-all ps cron-test cron-list cron-status cron-start cron-setup

# Biến môi trường
APP_NAME=wnapi
BUILD_DIR=./build
# MAIN_FILE=./cmd/server/main.go  # Old main file - commented out after FX migration
MAIN_FILE=./cmd/fx-server/main.go  # New FX-based main file
PROJECT ?= default
DOCKER_IMAGE_NAME=wnapi
DOCKER_TAG=latest
COVER_PROFILE=coverage.out
COVER_HTML=coverage.html

# Build ứng dụng
build:
	@echo "Building application..."
	@mkdir -p $(BUILD_DIR)
	@go build -o $(BUILD_DIR)/$(APP_NAME) $(MAIN_FILE)

# Chạy ứng dụng
run:
	@echo "Running application..."

# Xóa các file build
clean:
	@echo "Cleaning build files..."
	@rm -rf $(BUILD_DIR)
	@find . -name "*.test" -delete
	@find . -name "*.out" -delete
	@find . -name "coverage.*" -delete
	@echo "Removing Go build ID files..."
	@grep -rl "Go build ID" . --exclude-dir=bin | xargs rm -f 2>/dev/null || true
ifdef PROJECT
	@go run $(MAIN_FILE) --project=$(PROJECT)
else
	@go run $(MAIN_FILE)
endif

# Chạy ứng dụng với debugger (dev mode)
dev:
	@echo "Building application for debugging..."
	@mkdir -p ./tmp
	@go build -gcflags="all=-N -l" -o ./tmp/main $(MAIN_FILE)
	@echo "Starting application with debugger..."
	@exec dlv exec --accept-multiclient --log --headless --continue --listen :9034 --api-version 2 ./tmp/main

# Dọn dẹp build
clean:
	@echo "Cleaning build..."
	@rm -rf $(BUILD_DIR)
	@rm -f $(COVER_PROFILE) $(COVER_HTML)

# Tải dependencies
deps:
	@echo "Downloading dependencies..."
	@go mod download

# Tạo thư mục build nếu chưa tồn tại
init:
	@echo "Initializing project..."
	@mkdir -p $(BUILD_DIR)
	@mkdir -p logs
	@mkdir -p projects

# Format code
fmt:
	@echo "Formatting code..."
	@go fmt ./...

# Chạy tests
test:
	@echo "Running tests..."
	@go test -v ./...

# Chạy test với coverage
test-coverage:
	@echo "Running tests with coverage..."
	@go test -v -coverprofile=$(COVER_PROFILE) ./...
	@go tool cover -html=$(COVER_PROFILE) -o $(COVER_HTML)
	@echo "Coverage report generated at $(COVER_HTML)"
	@go tool cover -func=$(COVER_PROFILE)

# Chạy migrate database
migrate: migrate-up

migrate-up:
	@echo "Running database migrations for project: $(PROJECT)"
	@./scripts/migrate.sh $(PROJECT) up

migrate-down:
	@echo "Rolling back migrations for project: $(PROJECT)"
	@./scripts/migrate.sh $(PROJECT) down

migrate-version:
	@echo "Checking migration version for project: $(PROJECT)"
	@./scripts/migrate.sh $(PROJECT) version

create-migration:
	@if [ -z "$(MODULE)" ] || [ -z "$(NAME)" ]; then \
		echo "Usage: make create-migration MODULE=<module-name> NAME=<migration-name>"; \
		exit 1; \
	fi
	@echo "Creating migration for module: $(MODULE)"
	@./scripts/create-migration.sh $(MODULE) $(NAME)

# Tạo module mới
create-module:
	@if [ -z "$(name)" ]; then \
		echo "Usage: make create-module name=<module-name>"; \
		exit 1; \
	fi
	@echo "Creating new module: $(name)"
	@mkdir -p modules/$(name)
	@mkdir -p modules/$(name)/api
	@mkdir -p modules/$(name)/domain
	@mkdir -p modules/$(name)/dto
	@mkdir -p modules/$(name)/repository
	@mkdir -p modules/$(name)/migrations
	@mkdir -p modules/$(name)/internal
	@cat > modules/$(name)/module.go <<-'EOF'
	package $(name)

	import (
		"context"
		"wnapi/internal/core"
	)

	func init() {
		// Đăng ký module với global registry
		core.RegisterModuleFactory("$(name)", NewModule)
	}

	// Module triển khai $(name) module
	type Module struct {
		name   string
		app    *core.App
		config map[string]interface{}
	}

	// NewModule tạo module mới
	func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
		return &Module{
			name:   "$(name)",
			app:    app,
			config: config,
		}, nil
	}

	// Name trả về tên của module
	func (m *Module) Name() string {
		return m.name
	}

	// Init khởi tạo module
	func (m *Module) Init(ctx context.Context) error {
		log := m.app.GetLogger()
		log.Info("Initializing $(name) module")

		// TODO: Khởi tạo repository, service, etc.

		return nil
	}

	// RegisterRoutes đăng ký các routes của module
	func (m *Module) RegisterRoutes(router *core.Server) error {
		log := m.app.GetLogger()
		log.Info("Registering $(name) module routes")

		// TODO: Đăng ký routes

		return nil
	}

	// Cleanup dọn dẹp tài nguyên của module
	func (m *Module) Cleanup(ctx context.Context) error {
		log := m.app.GetLogger()
		log.Info("Cleaning up $(name) module")

		// TODO: Dọn dẹp tài nguyên

		return nil
	}

	// GetMigrationPath trả về đường dẫn chứa migrations của module
	func (m *Module) GetMigrationPath() string {
		return "modules/$(name)/migrations"
	}
	EOF
	@echo "Module $(name) created successfully!"

# Tạo plugin mới
create-plugin:
	@if [ -z "$(name)" ]; then \
		echo "Usage: make create-plugin name=<plugin-name>"; \
		exit 1; \
	fi
	@echo "Creating new plugin: $(name)"
	@mkdir -p plugins/$(name)
	@cat > plugins/$(name)/plugin.go <<-'EOF'
	package $(name)

	import (
		"context"
		"wnapi/plugins"
	)

	func init() {
		// Đăng ký plugin factory
		plugins.RegisterPluginFactory("$(name)", NewPlugin)
	}

	// Plugin triển khai plugin $(name)
	type Plugin struct {
		name   string
		config map[string]interface{}
	}

	// NewPlugin tạo một plugin mới
	func NewPlugin(config map[string]interface{}) (plugins.Plugin, error) {
		plugin := &Plugin{
			name:   "$(name)",
			config: config,
		}

		return plugin, nil
	}

	// Name trả về tên của plugin
	func (p *Plugin) Name() string {
		return p.name
	}

	// Init khởi tạo plugin
	func (p *Plugin) Init(ctx context.Context) error {
		// TODO: Khởi tạo plugin
		return nil
	}

	// Shutdown dọn dẹp tài nguyên của plugin
	func (p *Plugin) Shutdown(ctx context.Context) error {
		// TODO: Dọn dẹp tài nguyên
		return nil
	}
	EOF
	@echo "Plugin $(name) created successfully!"

# Docker commands
docker-build:
	@echo "Building Docker image..."
	@docker build -t $(DOCKER_IMAGE_NAME):$(DOCKER_TAG) .

docker-up:
	@echo "Starting Docker containers..."
	@docker-compose up -d

docker-down:
	@echo "Stopping Docker containers..."
	@docker-compose down

docker-logs:
	@echo "Showing Docker logs..."
	@docker-compose logs -f

# Seed commands
build-seed:
	@echo "Building seed command..."
	@go build -o bin/seed cmd/seed/main.go

seed-dev:
	@echo "Seeding development environment..."
	@./bin/seed --env=dev

seed-staging:
	@echo "Seeding staging environment..."
	@./bin/seed --env=staging

seed-prod:
	@echo "Seeding production environment..."
	@./bin/seed --env=prod

seed-list:
	@echo "Listing available seeds..."
	@./bin/seed -list

seed-module:
	@if [ -z "$(MODULE)" ]; then \
		echo "Usage: make seed-module MODULE=<module-name> [ENV=<environment>]"; \
		exit 1; \
	fi
	@echo "Seeding module: $(MODULE)"
	@./bin/seed --module=$(MODULE) --env=$(or $(ENV),dev)

seed-specific:
	@if [ -z "$(MODULE)" ] || [ -z "$(SEEDER)" ]; then \
		echo "Usage: make seed-specific MODULE=<module-name> SEEDER=<seeder-name> [ENV=<environment>]"; \
		exit 1; \
	fi
	@echo "Seeding specific: $(MODULE)/$(SEEDER)"
	@./bin/seed --module=$(MODULE) --seeder=$(SEEDER) --env=$(or $(ENV),dev)

seed-dry-run:
	@echo "Running seed in dry-run mode..."
	@./bin/seed --dry-run --env=$(or $(ENV),dev)

test-seed:
	@echo "Running seed system tests..."
	@go test -v ./internal/pkg/seed/... ./modules/auth/seeds/...

# Tạo file kế hoạch cho module mới
plan:
	@if [ -z "$(name)" ]; then \
		echo "Usage: make plan name=<module-name>"; \
		exit 1; \
	fi
	@mkdir -p docs/plans
	@if [ ! -f "docs/plans/$(name).md" ]; then \
		cp docs/plan-sample-module.md docs/plans/$(name).md; \
		echo "Created plan file: docs/plans/$(name).md"; \
	else \
		echo "Plan file already exists: docs/plans/$(name).md"; \
	fi

# Kill process commands
kill:
	@echo "Killing all wnapi processes and ports 9033, 9034..."
	@pkill -f "wnapi" || echo "No wnapi processes found"
	@pkill -f "$(APP_NAME)" || echo "No $(APP_NAME) processes found"
	@echo "Killing processes on port 9033..."
	@lsof -ti:9033 | xargs kill -9 2>/dev/null || echo "No processes found on port 9033"
	@echo "Killing processes on port 9034..."
	@lsof -ti:9034 | xargs kill -9 2>/dev/null || echo "No processes found on port 9034"

kill-dev:
	@echo "Killing development processes..."
	@pkill -f "dlv" || echo "No dlv (debugger) processes found"
	@pkill -f "tmp/main" || echo "No tmp/main processes found"

kill-port:
	@if [ -z "$(PORT)" ]; then \
		echo "Usage: make kill-port PORT=<port-number>"; \
		echo "Example: make kill-port PORT=8080"; \
		exit 1; \
	fi
	@echo "Killing processes on port $(PORT)..."
	@lsof -ti:$(PORT) | xargs kill -9 2>/dev/null || echo "No processes found on port $(PORT)"

kill-all:
	@echo "Killing all related processes..."
	@pkill -f "wnapi" || echo "No wnapi processes found"
	@pkill -f "$(APP_NAME)" || echo "No $(APP_NAME) processes found"
	@pkill -f "dlv" || echo "No dlv processes found"
	@pkill -f "tmp/main" || echo "No tmp/main processes found"
	@pkill -f "go run" || echo "No go run processes found"

ps:
	@echo "Showing all related processes..."
	@echo "=== WNAPI Processes ==="
	@ps aux | grep -E "(wnapi|$(APP_NAME))" | grep -v grep || echo "No wnapi processes found"
	@echo ""
	@echo "=== Development Processes ==="
	@ps aux | grep -E "(dlv|tmp/main)" | grep -v grep || echo "No development processes found"
	@echo ""
	@echo "=== Go Processes ==="
	@ps aux | grep "go run" | grep -v grep || echo "No go run processes found"

# Tạo tài liệu mẫu
docs:
	@mkdir -p docs
	@if [ ! -f "docs/plan-sample-module.md" ]; then \
		echo "Creating sample documentation..."; \
		mkdir -p docs/plans; \
		cat > docs/plan-sample-module.md <<-'EOF'\
		# Module Plan: [Module Name]

		## Tổng quan
		Mô tả ngắn gọn về mục đích và chức năng chính của module.

		## Yêu cầu chức năng
		- [ ] Chức năng 1
		- [ ] Chức năng 2
		- [ ] Chức năng 3

		## Công nghệ sử dụng
		- Công nghệ 1
		- Công nghệ 2

		## Cấu trúc thư mục
		```
		modules/[module-name]/
		├── api/             # API handlers
		├── domain/          # Business logic
		├── dto/             # Data transfer objects
		├── repository/      # Database operations
		├── migrations/      # Database migrations
		└── internal/        # Internal package code
		```

		## API Endpoints
		| Method | Path | Description |
		|--------|------|-------------|
		| GET    | /api/... | Get resource |
		| POST   | /api/... | Create resource |

		## Cấu hình
		```yaml
		modules:
		  [module-name]:
		    enabled: true
		    # Các thông số cấu hình khác
		```

		## Phụ thuộc
		- Module/Service 1
		- Module/Service 2

		## Tài liệu tham khảo
		- [Tài liệu 1](link)
		- [Tài liệu 2](link)
		EOF
		echo "Sample documentation created at: docs/plan-sample-module.md"; \
	else \
		echo "Sample documentation already exists at: docs/plan-sample-module.md"; \
	fi

# =============================================================================
# CRON COMMANDS
# =============================================================================

# Setup cron environment for local development
cron-setup:
	@echo "Setting up cron environment for local development..."
	@./scripts/test-cron-local.sh setup

# List all available cron jobs
cron-list:
	@echo "Listing all available cron jobs..."
	@./scripts/test-cron-local.sh list

# Show cron system status
cron-status:
	@echo "Showing cron system status..."
	@./scripts/test-cron-local.sh status

# Test all cron jobs individually
cron-test:
	@echo "Testing all cron jobs..."
	@./scripts/test-cron-local.sh test

# Test cron jobs with dry-run mode
cron-test-dry:
	@echo "Testing cron jobs with dry-run mode..."
	@./scripts/test-cron-local.sh test-dry

# Start cron scheduler
cron-start:
	@echo "Starting cron scheduler..."
	@./scripts/test-cron-local.sh start

# Monitor cron logs
cron-logs:
	@echo "Monitoring cron logs..."
	@./scripts/test-cron-local.sh logs

# Check Redis queue status
cron-redis:
	@echo "Checking Redis queue status..."
	@./scripts/test-cron-local.sh redis

# Run performance test on cron jobs
cron-performance:
	@echo "Running performance test on cron jobs..."
	@./scripts/test-cron-local.sh performance

# Run complete cron test suite
cron-all:
	@echo "Running complete cron test suite..."
	@./scripts/test-cron-local.sh all

# Clean up cron logs
cron-cleanup:
	@echo "Cleaning up cron logs..."
	@./scripts/test-cron-local.sh cleanup

# Run specific cron job manually
cron-run:
	@if [ -z "$(JOB)" ]; then \
		echo "Usage: make cron-run JOB=<job-name>"; \
		echo "Available jobs: system_cleanup, auth_session_cleanup, media_temp_cleanup, system_health_check"; \
		exit 1; \
	fi
	@echo "Running cron job: $(JOB)"
	@$(BUILD_DIR)/$(APP_NAME) cron run $(JOB)

# Show cron help
cron-help:
	@echo "Available cron commands:"
	@echo "  make cron-setup       - Setup local cron environment"
	@echo "  make cron-list        - List all cron jobs"
	@echo "  make cron-status      - Show cron system status"
	@echo "  make cron-test        - Test all cron jobs"
	@echo "  make cron-test-dry    - Test with dry-run mode"
	@echo "  make cron-start       - Start cron scheduler"
	@echo "  make cron-logs        - Monitor cron logs"
	@echo "  make cron-redis       - Check Redis queue"
	@echo "  make cron-performance - Run performance test"
	@echo "  make cron-all         - Run complete test suite"
	@echo "  make cron-cleanup     - Clean up logs"
	@echo "  make cron-run JOB=<name> - Run specific job"
	@echo ""
	@echo "Examples:"
	@echo "  make cron-setup"
	@echo "  make cron-test"
	@echo "  make cron-run JOB=system_cleanup"
	@echo "  make cron-start"