# Stage 1: Build application
FROM golang:1.23-alpine AS builder

# C<PERSON><PERSON> đặt các gói cần thiết
RUN apk add --no-cache git make gcc musl-dev

# Thư mục làm việc
WORKDIR /app

# Copy go.mod và go.sum trước để tận dụng Docker cache
COPY go.mod go.sum ./
RUN go mod download

# Copy toàn bộ source code
COPY . .

# Build ứng dụng
RUN make build

# Stage 2: Runtime
FROM alpine:3.20

# Cài đặt các gói cần thiết
RUN apk add --no-cache ca-certificates tzdata

# Tạo thư mục làm việc
WORKDIR /app

# Copy binary từ stage 1
COPY --from=builder /app/build/wnapi /app/wnapi



# Tạo thư mục logs
RUN mkdir -p /app/logs

# Thiết lập timezone
ENV TZ=Asia/Ho_Chi_Minh

# Expose port
EXPOSE 8080

# Thông tin về image
LABEL maintainer="<EMAIL>"
LABEL version="1.0"
LABEL description="WNAPI - Framework Golang Modular Microservices"

# Command để chạy ứng dụng
ENTRYPOINT ["/app/wnapi"]
# Các tham số mặc định, có thể ghi đè khi chạy container
CMD []